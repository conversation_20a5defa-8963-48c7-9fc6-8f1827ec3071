"use client";

import { useState, useEffect } from "react";
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
import { MainLayout } from "@/components/layout/MainLayout";
import { PageHeader } from "@/components/layout/PageHeader";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import { ArrowLeft, Loader2, Package, Save, AlertTriangle } from "lucide-react";
import { cn } from "@/lib/utils";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import Link from "next/link";
import { toast } from "sonner";

interface PurchaseOrderItem {
  id: string;
  quantity: number;
  receivedQuantity: number;
  unitPrice: number;
  subtotal: number;
  product: {
    id: string;
    name: string;
    sku: string;
    category?: {
      name: string;
    };
    unit?: {
      name: string;
    };
  };
}

interface PurchaseOrder {
  id: string;
  orderDate: string;
  status: string;
  subtotal: number;
  tax: number;
  total: number;
  notes?: string;
  supplier: {
    id: string;
    name: string;
  };
  items: PurchaseOrderItem[];
}

interface ReceivingItem {
  purchaseOrderItemId: string;
  receivedQuantity: number;
  discrepancyReason?: string;
  notes?: string;
}

const receivingSchema = z.object({
  notes: z.string().optional(),
  discrepancyReason: z.string().optional(),
});

type ReceivingFormValues = z.infer<typeof receivingSchema>;

export default function ReceivePurchaseOrderPage() {
  const params = useParams();
  const router = useRouter();
  const id = params.id as string;

  const [purchaseOrder, setPurchaseOrder] = useState<PurchaseOrder | null>(null);
  const [receivingItems, setReceivingItems] = useState<ReceivingItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [processing, setProcessing] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const form = useForm<ReceivingFormValues>({
    resolver: zodResolver(receivingSchema),
    defaultValues: {
      notes: "",
      discrepancyReason: "",
    },
  });

  // Fetch purchase order data
  const fetchPurchaseOrder = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch(`/api/purchase-orders/${id}`);
      if (!response.ok) {
        if (response.status === 404) {
          throw new Error("Purchase order not found");
        }
        throw new Error("Failed to fetch purchase order");
      }

      const data = await response.json();
      setPurchaseOrder(data);

      // Check if PO can be received
      if (!["APPROVED", "ORDERED", "PARTIALLY_RECEIVED"].includes(data.status)) {
        setError(
          "This purchase order cannot be received. Only APPROVED, ORDERED, or PARTIALLY_RECEIVED orders can be processed."
        );
        return;
      }

      // Initialize receiving items with remaining quantities
      const initialReceivingItems: ReceivingItem[] = data.items.map((item: PurchaseOrderItem) => ({
        purchaseOrderItemId: item.id,
        receivedQuantity: 0,
        discrepancyReason: "",
        notes: "",
      }));
      setReceivingItems(initialReceivingItems);
    } catch (error) {
      console.error("Error fetching purchase order:", error);
      setError(error instanceof Error ? error.message : "Failed to load purchase order");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (id) {
      fetchPurchaseOrder();
    }
  }, [id]);

  const updateReceivingItem = (index: number, field: keyof ReceivingItem, value: any) => {
    const updatedItems = [...receivingItems];
    updatedItems[index] = { ...updatedItems[index], [field]: value };
    setReceivingItems(updatedItems);
  };

  const getRemainingQuantity = (item: PurchaseOrderItem) => {
    return Number(item.quantity) - Number(item.receivedQuantity);
  };

  const getReceivingQuantity = (itemId: string) => {
    const receivingItem = receivingItems.find((ri) => ri.purchaseOrderItemId === itemId);
    return receivingItem?.receivedQuantity || 0;
  };

  const hasDiscrepancy = (item: PurchaseOrderItem) => {
    const receivingQty = getReceivingQuantity(item.id);
    const remainingQty = getRemainingQuantity(item);
    return receivingQty !== remainingQty && receivingQty > 0;
  };

  const validateReceiving = () => {
    const errors: string[] = [];

    // Check if at least one item has quantity > 0
    const hasReceivingItems = receivingItems.some((item) => item.receivedQuantity > 0);
    if (!hasReceivingItems) {
      errors.push("Please specify received quantities for at least one item");
    }

    // Check for over-receiving
    receivingItems.forEach((receivingItem, index) => {
      const poItem = purchaseOrder?.items.find(
        (item) => item.id === receivingItem.purchaseOrderItemId
      );
      if (poItem) {
        const remainingQty = getRemainingQuantity(poItem);
        if (receivingItem.receivedQuantity > remainingQty) {
          errors.push(
            `${poItem.product.name}: Cannot receive ${receivingItem.receivedQuantity}, only ${remainingQty} remaining`
          );
        }
      }
    });

    // Check for discrepancies without reasons
    receivingItems.forEach((receivingItem, index) => {
      const poItem = purchaseOrder?.items.find(
        (item) => item.id === receivingItem.purchaseOrderItemId
      );
      if (poItem && hasDiscrepancy(poItem) && !receivingItem.discrepancyReason?.trim()) {
        errors.push(
          `${poItem.product.name}: Discrepancy reason required when receiving partial quantity`
        );
      }
    });

    return errors;
  };

  const onSubmit = async (data: ReceivingFormValues) => {
    const validationErrors = validateReceiving();
    if (validationErrors.length > 0) {
      toast.error(validationErrors[0]);
      return;
    }

    try {
      setProcessing(true);
      setError(null);

      // Filter out items with zero quantity
      const itemsToReceive = receivingItems.filter((item) => item.receivedQuantity > 0);

      const payload = {
        notes: data.notes || "",
        discrepancyReason: data.discrepancyReason || "",
        items: itemsToReceive,
      };

      const response = await fetch(`/api/purchase-orders/${id}/receive`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(payload),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to process receiving");
      }

      toast.success("Receiving processed successfully");
      router.push(`/inventory/purchase-orders/${id}`);
    } catch (error) {
      console.error("Error processing receiving:", error);
      toast.error(error instanceof Error ? error.message : "Failed to process receiving");
    } finally {
      setProcessing(false);
    }
  };

  if (loading) {
    return (
      <MainLayout>
        <div className="flex justify-center items-center p-12">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
          <span className="ml-2">Loading purchase order...</span>
        </div>
      </MainLayout>
    );
  }

  if (error || !purchaseOrder) {
    return (
      <MainLayout>
        <PageHeader
          title="Receive Purchase Order"
          description="Process incoming inventory"
          actions={
            <Button variant="outline" asChild>
              <Link href="/inventory/purchase-orders">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Purchase Orders
              </Link>
            </Button>
          }
        />
        <Alert className="mt-6">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>{error || "Purchase order not found"}</AlertDescription>
        </Alert>
      </MainLayout>
    );
  }

  const totalReceivingItems = receivingItems.filter((item) => item.receivedQuantity > 0).length;
  const totalItemsWithDiscrepancies = purchaseOrder.items.filter(hasDiscrepancy).length;

  return (
    <MainLayout>
      <PageHeader
        title={`Receive Purchase Order #${purchaseOrder.id.slice(-8).toUpperCase()}`}
        description={`Processing delivery from ${purchaseOrder.supplier.name}`}
        actions={
          <div className="flex gap-2">
            <Button variant="outline" asChild>
              <Link href={`/inventory/purchase-orders/${id}`}>
                <ArrowLeft className="h-4 w-4 mr-2" />
                Cancel
              </Link>
            </Button>
            <Button type="submit" form="receiving-form" disabled={processing}>
              {processing ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Processing...
                </>
              ) : (
                <>
                  <Package className="h-4 w-4 mr-2" />
                  Process Receiving
                </>
              )}
            </Button>
          </div>
        }
      />

      <div className="space-y-6">
        {/* Status and Summary */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium text-muted-foreground">Current Status</span>
                <Badge variant="outline">{purchaseOrder.status.replace("_", " ")}</Badge>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium text-muted-foreground">Items to Receive</span>
                <span className="text-lg font-semibold">{totalReceivingItems}</span>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium text-muted-foreground">Discrepancies</span>
                <span
                  className={cn(
                    "text-lg font-semibold",
                    totalItemsWithDiscrepancies > 0 ? "text-orange-600" : "text-green-600"
                  )}
                >
                  {totalItemsWithDiscrepancies}
                </span>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Receiving Form */}
        <Card>
          <CardHeader>
            <CardTitle>Receiving Details</CardTitle>
          </CardHeader>
          <CardContent>
            <Form {...form}>
              <form
                onSubmit={form.handleSubmit(onSubmit)}
                className="space-y-6"
                id="receiving-form"
              >
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <FormField
                    control={form.control}
                    name="notes"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Receiving Notes (Optional)</FormLabel>
                        <FormControl>
                          <Textarea
                            placeholder="General notes about this receiving..."
                            className="resize-none"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="discrepancyReason"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>General Discrepancy Reason (Optional)</FormLabel>
                        <FormControl>
                          <Textarea
                            placeholder="Overall reason for any discrepancies..."
                            className="resize-none"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </form>
            </Form>
          </CardContent>
        </Card>

        {/* Items Receiving Table */}
        <Card>
          <CardHeader>
            <CardTitle>Items to Receive</CardTitle>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Product</TableHead>
                  <TableHead>Ordered</TableHead>
                  <TableHead>Previously Received</TableHead>
                  <TableHead>Remaining</TableHead>
                  <TableHead>Receiving Now</TableHead>
                  <TableHead>Discrepancy Reason</TableHead>
                  <TableHead>Notes</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {purchaseOrder.items.map((item, index) => {
                  const remainingQty = getRemainingQuantity(item);
                  const receivingQty = getReceivingQuantity(item.id);
                  const hasDisc = hasDiscrepancy(item);
                  const receivingItemIndex = receivingItems.findIndex(
                    (ri) => ri.purchaseOrderItemId === item.id
                  );

                  return (
                    <TableRow
                      key={item.id}
                      className={cn(remainingQty === 0 && "opacity-50", hasDisc && "bg-orange-50")}
                    >
                      <TableCell>
                        <div>
                          <div className="font-medium">{item.product.name}</div>
                          <div className="text-sm text-muted-foreground">
                            {item.product.sku}
                            {item.product.category && ` • ${item.product.category.name}`}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="text-center">
                          <div className="font-medium">{Number(item.quantity)}</div>
                          {item.product.unit && (
                            <div className="text-xs text-muted-foreground">
                              {item.product.unit.name}
                            </div>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="text-center font-medium">
                          {Number(item.receivedQuantity)}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div
                          className={cn(
                            "text-center font-medium",
                            remainingQty === 0 ? "text-green-600" : "text-blue-600"
                          )}
                        >
                          {remainingQty}
                        </div>
                      </TableCell>
                      <TableCell>
                        <Input
                          type="number"
                          min="0"
                          max={remainingQty}
                          value={receivingQty}
                          onChange={(e) =>
                            updateReceivingItem(
                              receivingItemIndex,
                              "receivedQuantity",
                              parseInt(e.target.value) || 0
                            )
                          }
                          className={cn(
                            "w-20 text-center",
                            hasDisc && "border-orange-300 bg-orange-50"
                          )}
                          disabled={remainingQty === 0}
                        />
                      </TableCell>
                      <TableCell>
                        <Input
                          placeholder="Reason..."
                          value={receivingItems[receivingItemIndex]?.discrepancyReason || ""}
                          onChange={(e) =>
                            updateReceivingItem(
                              receivingItemIndex,
                              "discrepancyReason",
                              e.target.value
                            )
                          }
                          className={cn(
                            "w-32",
                            hasDisc &&
                              !receivingItems[receivingItemIndex]?.discrepancyReason?.trim() &&
                              "border-red-300 bg-red-50"
                          )}
                          disabled={!hasDisc && receivingQty === 0}
                        />
                      </TableCell>
                      <TableCell>
                        <Input
                          placeholder="Notes..."
                          value={receivingItems[receivingItemIndex]?.notes || ""}
                          onChange={(e) =>
                            updateReceivingItem(receivingItemIndex, "notes", e.target.value)
                          }
                          className="w-32"
                          disabled={receivingQty === 0}
                        />
                      </TableCell>
                    </TableRow>
                  );
                })}
              </TableBody>
            </Table>
          </CardContent>
        </Card>

        {/* Quick Actions */}
        <Card>
          <CardHeader>
            <CardTitle>Quick Actions</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex gap-2 flex-wrap">
              <Button
                variant="outline"
                size="sm"
                onClick={() => {
                  const updatedItems = receivingItems.map((item, index) => {
                    const poItem = purchaseOrder.items.find(
                      (poi) => poi.id === item.purchaseOrderItemId
                    );
                    if (poItem) {
                      return {
                        ...item,
                        receivedQuantity: getRemainingQuantity(poItem),
                      };
                    }
                    return item;
                  });
                  setReceivingItems(updatedItems);
                }}
              >
                Receive All Remaining
              </Button>

              <Button
                variant="outline"
                size="sm"
                onClick={() => {
                  const updatedItems = receivingItems.map((item) => ({
                    ...item,
                    receivedQuantity: 0,
                    discrepancyReason: "",
                    notes: "",
                  }));
                  setReceivingItems(updatedItems);
                }}
              >
                Clear All
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Summary */}
        {totalReceivingItems > 0 && (
          <Card>
            <CardHeader>
              <CardTitle>Receiving Summary</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span>Items to receive:</span>
                  <span className="font-medium">{totalReceivingItems}</span>
                </div>
                {totalItemsWithDiscrepancies > 0 && (
                  <div className="flex justify-between text-orange-600">
                    <span>Items with discrepancies:</span>
                    <span className="font-medium">{totalItemsWithDiscrepancies}</span>
                  </div>
                )}
                <div className="pt-2 border-t">
                  <div className="text-sm text-muted-foreground">
                    This receiving will update warehouse inventory levels and may change the
                    purchase order status.
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </MainLayout>
  );
}
