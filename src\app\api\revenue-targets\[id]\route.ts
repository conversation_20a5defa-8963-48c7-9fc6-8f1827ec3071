import { NextRequest, NextResponse } from "next/server";
import { PrismaClient } from "@/generated/prisma";
import { verifyAuthToken } from "@/lib/auth-utils";

// Create a singleton instance of PrismaClient to prevent too many connections
const globalForPrisma = globalThis as unknown as { prisma: PrismaClient };
const prisma = globalForPrisma.prisma || new PrismaClient();
if (process.env.NODE_ENV !== "production") globalForPrisma.prisma = prisma;

// GET /api/revenue-targets/[id] - Get specific revenue target
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    console.log('[Revenue Target API] GET request for ID:', params.id);

    // Verify authentication
    const authResult = await verifyAuthToken(request);
    if (!authResult.authenticated || !authResult.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Check if user has permission (SUPER_ADMIN or FINANCE_ADMIN)
    if (!['SUPER_ADMIN', 'FINANCE_ADMIN'].includes(authResult.user.role)) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    const target = await prisma.revenueTarget.findUnique({
      where: { id: params.id },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true
          }
        }
      }
    });

    if (!target) {
      return NextResponse.json(
        { error: "Revenue target not found" },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: target,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('[Revenue Target API] Error:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to fetch revenue target',
        timestamp: new Date().toISOString()
      },
      { status: 500 }
    );
  }
}

// PUT /api/revenue-targets/[id] - Update revenue target
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    console.log('[Revenue Target API] PUT request for ID:', params.id);

    // Verify authentication
    const authResult = await verifyAuthToken(request);
    if (!authResult.authenticated || !authResult.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Check if user has permission (SUPER_ADMIN or FINANCE_ADMIN)
    if (!['SUPER_ADMIN', 'FINANCE_ADMIN'].includes(authResult.user.role)) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    // Check if target exists
    const existingTarget = await prisma.revenueTarget.findUnique({
      where: { id: params.id }
    });

    if (!existingTarget) {
      return NextResponse.json(
        { error: "Revenue target not found" },
        { status: 404 }
      );
    }

    const body = await request.json();
    const { name, description, targetType, startDate, endDate, amount, isActive } = body;

    // Validate date range if provided
    if (startDate && endDate) {
      const start = new Date(startDate);
      const end = new Date(endDate);
      
      if (start >= end) {
        return NextResponse.json(
          { error: "Start date must be before end date" },
          { status: 400 }
        );
      }

      // Check for overlapping targets of the same type (excluding current target)
      if (targetType) {
        const overlapping = await prisma.revenueTarget.findFirst({
          where: {
            id: { not: params.id },
            targetType,
            isActive: true,
            OR: [
              {
                AND: [
                  { startDate: { lte: start } },
                  { endDate: { gte: start } }
                ]
              },
              {
                AND: [
                  { startDate: { lte: end } },
                  { endDate: { gte: end } }
                ]
              },
              {
                AND: [
                  { startDate: { gte: start } },
                  { endDate: { lte: end } }
                ]
              }
            ]
          }
        });

        if (overlapping) {
          return NextResponse.json(
            { error: `Overlapping ${targetType.toLowerCase()} target already exists for this period` },
            { status: 400 }
          );
        }
      }
    }

    // Update the revenue target
    const updateData: any = {};
    if (name !== undefined) updateData.name = name;
    if (description !== undefined) updateData.description = description;
    if (targetType !== undefined) updateData.targetType = targetType;
    if (startDate !== undefined) updateData.startDate = new Date(startDate);
    if (endDate !== undefined) updateData.endDate = new Date(endDate);
    if (amount !== undefined) updateData.amount = parseFloat(amount);
    if (isActive !== undefined) updateData.isActive = isActive;

    const target = await prisma.revenueTarget.update({
      where: { id: params.id },
      data: updateData,
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true
          }
        }
      }
    });

    console.log('[Revenue Target API] Target updated:', target.id);

    return NextResponse.json({
      success: true,
      data: target,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('[Revenue Target API] Error updating target:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to update revenue target',
        timestamp: new Date().toISOString()
      },
      { status: 500 }
    );
  }
}

// DELETE /api/revenue-targets/[id] - Delete revenue target
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    console.log('[Revenue Target API] DELETE request for ID:', params.id);

    // Verify authentication
    const authResult = await verifyAuthToken(request);
    if (!authResult.authenticated || !authResult.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Check if user has permission (SUPER_ADMIN only for deletion)
    if (authResult.user.role !== 'SUPER_ADMIN') {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    // Check if target exists
    const existingTarget = await prisma.revenueTarget.findUnique({
      where: { id: params.id }
    });

    if (!existingTarget) {
      return NextResponse.json(
        { error: "Revenue target not found" },
        { status: 404 }
      );
    }

    // Delete the revenue target
    await prisma.revenueTarget.delete({
      where: { id: params.id }
    });

    console.log('[Revenue Target API] Target deleted:', params.id);

    return NextResponse.json({
      success: true,
      message: "Revenue target deleted successfully",
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('[Revenue Target API] Error deleting target:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to delete revenue target',
        timestamp: new Date().toISOString()
      },
      { status: 500 }
    );
  }
}
