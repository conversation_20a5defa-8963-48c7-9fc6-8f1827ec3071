import { NextRequest, NextResponse } from 'next/server';
import { PrismaClient } from '@/generated/prisma';
import { verifyAuthToken } from '@/lib/auth-utils';
import { z } from 'zod';

// Create a singleton instance of PrismaClient to prevent too many connections
const globalForPrisma = globalThis as unknown as { prisma: PrismaClient };
const prisma = globalForPrisma.prisma || new PrismaClient();
if (process.env.NODE_ENV !== "production") globalForPrisma.prisma = prisma;

// Schema for creating a supplier return
const createSupplierReturnSchema = z.object({
  purchaseOrderId: z.string(),
  supplierId: z.string(),
  reason: z.string().min(1, 'Reason is required'),
  notes: z.string().optional(),
  items: z.array(z.object({
    productId: z.string(),
    quantity: z.number().positive(),
    unitPrice: z.number().positive(),
    subtotal: z.number().positive(),
  })).min(1, 'At least one item is required'),
});

// GET /api/supplier-returns - List supplier returns
export async function GET(request: NextRequest) {
  try {
    const auth = await verifyAuthToken(request);
    if (!auth.authenticated || !auth.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = ['SUPER_ADMIN', 'WAREHOUSE_ADMIN', 'FINANCE_ADMIN'].includes(auth.user.role);
    if (!hasPermission) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 });
    }

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const status = searchParams.get('status');
    const search = searchParams.get('search');

    const skip = (page - 1) * limit;

    // Build where clause
    const where: any = {};
    if (status) {
      where.status = status;
    }
    if (search) {
      where.OR = [
        { reason: { contains: search, mode: 'insensitive' } },
        { supplier: { name: { contains: search, mode: 'insensitive' } } },
        { purchaseOrder: { id: { contains: search, mode: 'insensitive' } } },
      ];
    }

    const [supplierReturns, total] = await Promise.all([
      prisma.supplierReturn.findMany({
        where,
        include: {
          supplier: true,
          purchaseOrder: true,
          items: {
            include: {
              product: true,
            },
          },
        },
        orderBy: { createdAt: 'desc' },
        skip,
        take: limit,
      }),
      prisma.supplierReturn.count({ where }),
    ]);

    return NextResponse.json({
      supplierReturns,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    });
  } catch (error) {
    console.error('Error fetching supplier returns:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

// POST /api/supplier-returns - Create supplier return
export async function POST(request: NextRequest) {
  try {
    const auth = await verifyAuthToken(request);
    if (!auth.authenticated || !auth.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = ['SUPER_ADMIN', 'WAREHOUSE_ADMIN'].includes(auth.user.role);
    if (!hasPermission) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 });
    }

    const body = await request.json();
    const validatedData = createSupplierReturnSchema.parse(body);

    // Verify purchase order exists
    const purchaseOrder = await prisma.purchaseOrder.findUnique({
      where: { id: validatedData.purchaseOrderId },
      include: { items: true },
    });

    if (!purchaseOrder) {
      return NextResponse.json({ error: 'Purchase order not found' }, { status: 404 });
    }

    if (purchaseOrder.status !== 'RECEIVED') {
      return NextResponse.json({ 
        error: 'Can only return items from received purchase orders' 
      }, { status: 400 });
    }

    // Validate return items against purchase order items
    for (const returnItem of validatedData.items) {
      const poItem = purchaseOrder.items.find(item => item.productId === returnItem.productId);
      if (!poItem) {
        return NextResponse.json({ 
          error: `Product ${returnItem.productId} was not in the original purchase order` 
        }, { status: 400 });
      }
      if (returnItem.quantity > poItem.quantity) {
        return NextResponse.json({ 
          error: `Return quantity exceeds original purchase quantity for product ${returnItem.productId}` 
        }, { status: 400 });
      }
    }

    // Calculate total
    const total = validatedData.items.reduce((sum, item) => sum + item.subtotal, 0);

    // Create supplier return with items
    const newSupplierReturn = await prisma.supplierReturn.create({
      data: {
        purchaseOrderId: validatedData.purchaseOrderId,
        supplierId: validatedData.supplierId,
        reason: validatedData.reason,
        notes: validatedData.notes,
        total,
        items: {
          create: validatedData.items,
        },
      },
      include: {
        supplier: true,
        purchaseOrder: true,
        items: {
          include: {
            product: true,
          },
        },
      },
    });

    return NextResponse.json(newSupplierReturn, { status: 201 });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json({ error: 'Validation error', details: error.errors }, { status: 400 });
    }
    console.error('Error creating supplier return:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
