import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/auth";
import { TransferStatus } from "@/generated/prisma";

// Helper function to verify the authentication token
async function verifyAuthToken(request: NextRequest) {
  // Get the session token from cookies
  const token = request.cookies.get("session-token");

  // Check if token exists
  if (!token) {
    console.log("[API] /api/inventory/simple-transfers/[id]/cancel - No session token found");
    return {
      authenticated: false,
      error: "Unauthorized - No session token",
      status: 403,
      user: null
    };
  }

  // Verify the token
  try {
    const { payload } = await import("jose").then(({ jwtVerify }) =>
      jwtVerify(
        token.value,
        new TextEncoder().encode(process.env.NEXTAUTH_SECRET || "fallback-secret")
      )
    );

    return {
      authenticated: true,
      user: {
        id: payload.id as string,
        name: payload.name as string,
        email: payload.email as string,
        role: payload.role as string
      }
    };
  } catch (error) {
    console.error("[API] /api/inventory/simple-transfers/[id]/cancel - JWT verification failed:", error);
    return {
      authenticated: false,
      error: "Unauthorized - Invalid token",
      status: 403,
      user: null
    };
  }
}

// POST /api/inventory/simple-transfers/[id]/cancel - Cancel a simple stock transfer
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    console.log("[API] POST /api/inventory/simple-transfers/[id]/cancel - Start");

    // Check authentication
    const auth = await verifyAuthToken(request);

    if (!auth.authenticated) {
      return NextResponse.json(
        { error: auth.error },
        { status: auth.status }
      );
    }

    // Get request body
    let body;
    try {
      body = await request.json();
    } catch (error) {
      return NextResponse.json(
        { error: "Invalid request body", message: "Could not parse JSON body" },
        { status: 400 }
      );
    }

    // Get the transfer
    const transfer = await prisma.simpleStockTransfer.findUnique({
      where: { id: params.id },
      include: {
        product: true,
        requestedBy: true
      }
    });

    if (!transfer) {
      return NextResponse.json(
        { error: "Transfer not found" },
        { status: 404 }
      );
    }

    // Check if transfer can be cancelled
    if (transfer.status === TransferStatus.COMPLETED || transfer.status === TransferStatus.CANCELLED) {
      return NextResponse.json(
        { error: `Transfer cannot be cancelled because it is in ${transfer.status} status` },
        { status: 400 }
      );
    }

    // Check if user has permission to cancel the transfer
    // Only the requester, super admin, or warehouse admin can cancel
    const isRequester = auth.user.id === transfer.requestedById;
    const isAdmin = ["SUPER_ADMIN", "WAREHOUSE_ADMIN"].includes(auth.user.role);
    
    if (!isRequester && !isAdmin) {
      return NextResponse.json(
        { error: "Unauthorized - You don't have permission to cancel this transfer" },
        { status: 403 }
      );
    }

    // Update the transfer status
    const updatedTransfer = await prisma.simpleStockTransfer.update({
      where: { id: params.id },
      data: {
        status: TransferStatus.CANCELLED,
        notes: body.notes || transfer.notes
      },
      include: {
        product: {
          include: {
            category: true,
            unit: true
          }
        },
        requestedBy: {
          select: {
            id: true,
            name: true,
            email: true,
            role: true
          }
        },
        approvedBy: {
          select: {
            id: true,
            name: true,
            email: true,
            role: true
          }
        }
      }
    });

    // Log activity
    await prisma.activityLog.create({
      data: {
        userId: auth.user.id,
        action: "CANCEL_SIMPLE_STOCK_TRANSFER",
        details: `Cancelled stock transfer #${transfer.id} for ${transfer.product.name}: ${transfer.quantity} units from ${transfer.fromStore ? "Store" : "Warehouse"} to ${transfer.toStore ? "Store" : "Warehouse"}`,
      }
    });

    return NextResponse.json({ transfer: updatedTransfer });
  } catch (error) {
    console.error("Error cancelling simple stock transfer:", error);
    return NextResponse.json(
      { error: "Failed to cancel simple stock transfer", message: (error as Error).message },
      { status: 500 }
    );
  }
}
