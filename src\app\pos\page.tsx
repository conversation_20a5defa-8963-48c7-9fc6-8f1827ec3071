"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { useClientAuth } from "@/hooks/use-client-auth";
import { POSHeader } from "@/components/pos/POSHeader";
import { ProductSearch } from "@/components/pos/ProductSearch";
import { ShoppingCart } from "@/components/pos/ShoppingCart";
import { CustomerSelect } from "@/components/pos/CustomerSelect";
import { OrderSummary } from "@/components/pos/OrderSummary";
import { DrawerInfo } from "@/components/pos/DrawerInfo";
import { PaymentModal } from "@/components/pos/PaymentModal";

import { Loader2, AlertCircle } from "lucide-react";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { POSCartProvider } from "@/contexts/POSCartContext";
import { POSProvider, usePOS } from "@/contexts/POSContext";

// Inner component that has access to POS context
function POSPageContent() {
  const router = useRouter();
  const { user, loading } = useClientAuth();
  const { focusSearchInput } = usePOS();
  const [isInitialized, setIsInitialized] = useState(false);
  const [paymentModalOpen, setPaymentModalOpen] = useState(false);
  const [selectedCustomer, setSelectedCustomer] = useState<any>(null);
  const [drawerError, setDrawerError] = useState<string | null>(null);

  // Check authentication and role
  useEffect(() => {
    console.log(
      "POS: useEffect triggered - loading:",
      loading,
      "user:",
      user?.email,
      "role:",
      user?.role
    );

    if (loading) {
      console.log("POS: Still loading, skipping checks");
      return;
    }

    if (!user) {
      console.log("POS: No user found, redirecting to login");
      router.push("/login");
      return;
    }

    // Only CASHIER role can access POS
    if (user.role !== "CASHIER") {
      console.log("POS: User is not CASHIER, redirecting to access-denied");
      router.push("/access-denied");
      return;
    }

    console.log("POS: User is CASHIER, initializing POS...");
    // Initialize POS for cashier users
    setIsInitialized(true);
  }, [user, loading, router]);

  // Focus search input after successful login and initialization
  useEffect(() => {
    if (isInitialized && user && user.role === "CASHIER") {
      console.log("POS: User authenticated and initialized, focusing search input");
      // Delay to ensure all components are mounted and rendered
      setTimeout(() => {
        focusSearchInput(500);
      }, 1000);
    }
  }, [isInitialized, user, focusSearchInput]);

  // Handle window focus events to restore focus when returning from receipt tabs
  useEffect(() => {
    if (!isInitialized) return;

    let focusTimeout: NodeJS.Timeout;

    const handleWindowFocus = () => {
      console.log("POS: Window regained focus, restoring search input focus");
      // Clear any existing timeout
      if (focusTimeout) {
        clearTimeout(focusTimeout);
      }
      // Delay focus restoration to ensure tab switching is complete
      focusTimeout = setTimeout(() => {
        focusSearchInput(200);
      }, 300);
    };

    const handleVisibilityChange = () => {
      if (!document.hidden) {
        console.log("POS: Page became visible, restoring search input focus");
        // Clear any existing timeout
        if (focusTimeout) {
          clearTimeout(focusTimeout);
        }
        // Delay focus restoration to ensure visibility change is complete
        focusTimeout = setTimeout(() => {
          focusSearchInput(200);
        }, 300);
      }
    };

    // Add event listeners
    window.addEventListener("focus", handleWindowFocus);
    document.addEventListener("visibilitychange", handleVisibilityChange);

    // Cleanup
    return () => {
      if (focusTimeout) {
        clearTimeout(focusTimeout);
      }
      window.removeEventListener("focus", handleWindowFocus);
      document.removeEventListener("visibilitychange", handleVisibilityChange);
    };
  }, [isInitialized, focusSearchInput]);

  // Load selected customer from localStorage
  useEffect(() => {
    if (typeof window !== "undefined") {
      const savedCustomer = localStorage.getItem("pos_selected_customer");
      if (savedCustomer) {
        try {
          const parsedCustomer = JSON.parse(savedCustomer);
          setSelectedCustomer(parsedCustomer);
        } catch (e) {
          console.error("Error parsing saved customer:", e);
          localStorage.removeItem("pos_selected_customer");
        }
      }
    }
  }, []);

  // Check drawer status and auto-open if needed
  useEffect(() => {
    const checkDrawerStatusAndAutoOpen = async () => {
      if (!user?.id) return;

      try {
        // First check if user already has an active drawer session
        const sessionResponse = await fetch("/api/drawer-sessions/current");
        if (sessionResponse.ok) {
          const sessionData = await sessionResponse.json();
          if (sessionData.session) {
            // User already has an active session
            setDrawerError(null);
            return;
          }
        }

        // Check drawer assignment
        const drawerResponse = await fetch(`/api/users/${user.id}/drawer`);
        const drawerData = await drawerResponse.json();

        if (!drawerResponse.ok) {
          setDrawerError("Failed to check drawer status");
          return;
        }

        const drawer = drawerData.drawer;
        if (!drawer) {
          setDrawerError("No drawer assigned to your account. Contact admin.");
          return;
        } else if (!drawer.isActive) {
          setDrawerError("Drawer inactive. Contact admin.");
          return;
        } else if (!drawer.terminal) {
          setDrawerError("Drawer not assigned to terminal.");
          return;
        }

        // Check if user has an active drawer session
        try {
          const sessionResponse = await fetch("/api/drawer-sessions/current");
          if (sessionResponse.ok) {
            const sessionData = await sessionResponse.json();
            if (sessionData.session) {
              setDrawerError(null);
            } else {
              setDrawerError("No active drawer session. Please open your drawer to start.");
            }
          } else {
            setDrawerError("Failed to check drawer session status");
          }
        } catch (sessionError) {
          console.error("Error checking drawer session:", sessionError);
          setDrawerError("Failed to check drawer session status");
        }
      } catch (error) {
        console.error("Error checking drawer status:", error);
        setDrawerError("Failed to check drawer status");
      }
    };

    if (isInitialized) {
      checkDrawerStatusAndAutoOpen();
    }

    // Listen for drawer session creation events
    const handleDrawerSessionCreated = (event: CustomEvent) => {
      console.log("POS: Drawer session created, clearing drawer error", event.detail);
      setDrawerError(null);
    };

    // Listen for drawer session closure events
    const handleDrawerSessionClosed = (event: CustomEvent) => {
      console.log("POS: Drawer session closed, setting drawer error", event.detail);
      setDrawerError("No active drawer session. Please open your drawer to start.");
    };

    window.addEventListener("drawerSessionCreated", handleDrawerSessionCreated as EventListener);
    window.addEventListener("drawerSessionClosed", handleDrawerSessionClosed as EventListener);

    return () => {
      window.removeEventListener(
        "drawerSessionCreated",
        handleDrawerSessionCreated as EventListener
      );
      window.removeEventListener("drawerSessionClosed", handleDrawerSessionClosed as EventListener);
    };
  }, [user?.id, isInitialized]);

  // Show loading while checking authentication
  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="flex items-center space-x-2">
          <Loader2 className="h-6 w-6 animate-spin" />
          <span>Loading POS...</span>
        </div>
      </div>
    );
  }

  // Show access denied if not cashier
  if (!user || user.role !== "CASHIER") {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-red-100">
              <AlertCircle className="h-6 w-6 text-red-600" />
            </div>
            <CardTitle className="text-xl font-semibold text-red-600">Access Denied</CardTitle>
            <CardDescription>Only cashiers can access the POS system.</CardDescription>
          </CardHeader>
        </Card>
      </div>
    );
  }

  // Only show POS if user is authenticated, is a cashier, and initialized
  if (!isInitialized) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="flex items-center space-x-2">
          <Loader2 className="h-6 w-6 animate-spin" />
          <span>Initializing POS...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* POS Header */}
      <POSHeader user={user} />

      {/* Main POS Content - 70/30 Split Layout */}
      <div className="flex h-[calc(100vh-80px)]">
        {/* Left Section - Cart & Product Handling (70%) */}
        <div className="w-[70%] p-6 border-r border-gray-200">
          <div className="h-full flex flex-col space-y-6">
            {/* Product Search */}
            <div className="flex-shrink-0">
              <ProductSearch />
            </div>

            {/* Shopping Cart */}
            <div className="flex-1 overflow-hidden">
              <ShoppingCart />
            </div>
          </div>
        </div>

        {/* Right Section - Customer & Payment (30%) */}
        <div className="w-[30%] p-6">
          <div className="h-full flex flex-col space-y-6">
            {/* Customer Selection */}
            <div className="flex-shrink-0">
              <CustomerSelect />
            </div>

            {/* Order Summary */}
            <div className="flex-shrink-0">
              <OrderSummary
                onProceedToPayment={() => setPaymentModalOpen(true)}
                disabled={!!drawerError}
              />
            </div>

            {/* Drawer Error Message */}
            {drawerError && (
              <div className="flex-shrink-0">
                <Card className="border-red-200 bg-red-50">
                  <CardContent className="pt-6">
                    <div className="flex items-center space-x-2 text-red-600">
                      <AlertCircle className="h-4 w-4" />
                      <span className="text-sm font-medium">{drawerError}</span>
                    </div>
                  </CardContent>
                </Card>
              </div>
            )}

            {/* Drawer Info (Collapsible) */}
            <div className="flex-1">
              <DrawerInfo />
            </div>
          </div>
        </div>
      </div>

      {/* Payment Modal */}
      <PaymentModal
        isOpen={paymentModalOpen}
        onClose={() => setPaymentModalOpen(false)}
        selectedCustomer={selectedCustomer}
      />
    </div>
  );
}

// Main wrapper component
export default function POSPage() {
  return (
    <POSProvider>
      <POSCartProvider>
        <POSPageContent />
      </POSCartProvider>
    </POSProvider>
  );
}
