"use client";

import { useState, useEffect } from "react";
import { MainLayout } from "@/components/layout/MainLayout";
import { PageHeader } from "@/components/layout/PageHeader";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Separator } from "@/components/ui/separator";
import {
  ArrowLeft,
  CheckCircle,
  XCircle,
  Clock,
  Edit,
  AlertTriangle,
  Package,
  User,
  Calendar,
  FileText,
  DollarSign,
  Ban,
  ArrowRight,
} from "lucide-react";
import Link from "next/link";
import { useParams, useRouter } from "next/navigation";
import { toast } from "sonner";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialog<PERSON>ooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Checkbox } from "@/components/ui/checkbox";

interface ReturnItem {
  id: string;
  productId: string;
  quantity: number;
  unitPrice: number;
  subtotal: number;
  product: {
    id: string;
    name: string;
    sku: string;
    category?: {
      name: string;
    };
    unit: {
      name: string;
      abbreviation: string;
    };
  };
}

interface Return {
  id: string;
  returnDate: string;
  reason: string;
  total: number;
  status: "PENDING" | "APPROVED" | "COMPLETED" | "REJECTED";
  disposition?: "RETURN_TO_STOCK" | "DO_NOT_RETURN_TO_STOCK";
  dispositionReason?: string;
  addToSupplierQueue?: boolean;
  supplierReturnQueueId?: string;
  customerResolution?: "REPLACEMENT" | "REFUND" | "PENDING_REPLACEMENT" | "NONE";
  customerResolutionNotes?: string;
  customerResolutionProcessedAt?: string;
  customerResolutionProcessedBy?: string;
  awaitingRestock?: boolean;
  notes?: string;
  createdAt: string;
  updatedAt: string;
  customer: {
    id: string;
    name: string;
    phone?: string;
    email?: string;
  } | null;
  transaction: {
    id: string;
    transactionDate: string;
    total: number;
    cashier: {
      id: string;
      name: string;
      email: string;
    };
  };
  items: ReturnItem[];
}

const statusColors = {
  PENDING: "bg-yellow-100 text-yellow-800",
  APPROVED: "bg-blue-100 text-blue-800",
  COMPLETED: "bg-green-100 text-green-800",
  REJECTED: "bg-red-100 text-red-800",
};

const statusIcons = {
  PENDING: <Clock className="h-4 w-4" />,
  APPROVED: <CheckCircle className="h-4 w-4" />,
  COMPLETED: <CheckCircle className="h-4 w-4" />,
  REJECTED: <XCircle className="h-4 w-4" />,
};

export default function ReturnDetailsPage() {
  const params = useParams();
  const router = useRouter();
  const [returnData, setReturnData] = useState<Return | null>(null);
  const [loading, setLoading] = useState(true);
  const [actionLoading, setActionLoading] = useState(false);
  const [rejectionReason, setRejectionReason] = useState("");
  const [disposition, setDisposition] = useState<"RETURN_TO_STOCK" | "DO_NOT_RETURN_TO_STOCK">(
    "RETURN_TO_STOCK"
  );
  const [dispositionReason, setDispositionReason] = useState("");
  const [addToSupplierQueue, setAddToSupplierQueue] = useState(false);
  const [dispositionDialogOpen, setDispositionDialogOpen] = useState(false);
  const [cancelReason, setCancelReason] = useState("");
  const [customerResolutionDialogOpen, setCustomerResolutionDialogOpen] = useState(false);
  const [customerResolution, setCustomerResolution] = useState<
    "REPLACEMENT" | "REFUND" | "PENDING_REPLACEMENT"
  >("REPLACEMENT");
  const [customerResolutionNotes, setCustomerResolutionNotes] = useState("");
  const [stockCheckData, setStockCheckData] = useState<any>(null);
  const [stockCheckLoading, setStockCheckLoading] = useState(false);

  const fetchReturn = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/returns/${params.id}`);
      if (!response.ok) {
        if (response.status === 404) {
          toast.error("Return not found");
          router.push("/inventory/returns");
          return;
        }
        throw new Error("Failed to fetch return");
      }
      const data = await response.json();
      setReturnData(data);
    } catch (error) {
      console.error("Error fetching return:", error);
      toast.error("Failed to load return details");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchReturn();
  }, [params.id]);

  const handleApprove = async () => {
    if (!returnData) return;

    try {
      setActionLoading(true);
      const response = await fetch(`/api/returns/${returnData.id}/approve`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          disposition,
          dispositionReason: dispositionReason.trim() || undefined,
          addToSupplierQueue: disposition === "DO_NOT_RETURN_TO_STOCK" ? addToSupplierQueue : false,
        }),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || "Failed to approve return");
      }

      toast.success("Return approved successfully");
      setDispositionDialogOpen(false);
      setDispositionReason("");
      setAddToSupplierQueue(false);
      fetchReturn(); // Refresh data
    } catch (error) {
      console.error("Error approving return:", error);
      toast.error(error instanceof Error ? error.message : "Failed to approve return");
    } finally {
      setActionLoading(false);
    }
  };

  const handleReject = async () => {
    if (!returnData || !rejectionReason.trim()) {
      toast.error("Please provide a rejection reason");
      return;
    }

    try {
      setActionLoading(true);
      const response = await fetch(`/api/returns/${returnData.id}/reject`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ reason: rejectionReason }),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || "Failed to reject return");
      }

      toast.success("Return rejected");
      setRejectionReason("");
      fetchReturn(); // Refresh data
    } catch (error) {
      console.error("Error rejecting return:", error);
      toast.error(error instanceof Error ? error.message : "Failed to reject return");
    } finally {
      setActionLoading(false);
    }
  };

  const handleComplete = async () => {
    if (!returnData) return;

    try {
      setActionLoading(true);
      const response = await fetch(`/api/returns/${returnData.id}/complete`, {
        method: "POST",
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || "Failed to complete return");
      }

      toast.success("Return completed successfully");
      fetchReturn(); // Refresh data
    } catch (error) {
      console.error("Error completing return:", error);
      toast.error(error instanceof Error ? error.message : "Failed to complete return");
    } finally {
      setActionLoading(false);
    }
  };

  const handleCancel = async () => {
    if (!returnData || !cancelReason.trim()) {
      toast.error("Please provide a cancellation reason");
      return;
    }

    try {
      setActionLoading(true);
      const response = await fetch(`/api/returns/${returnData.id}/cancel`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ reason: cancelReason }),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || "Failed to cancel return");
      }

      toast.success("Return cancelled");
      setCancelReason("");
      fetchReturn(); // Refresh data
    } catch (error) {
      console.error("Error cancelling return:", error);
      toast.error(error instanceof Error ? error.message : "Failed to cancel return");
    } finally {
      setActionLoading(false);
    }
  };

  const handleAddToSupplierQueue = async () => {
    if (!returnData) return;

    try {
      setActionLoading(true);
      const response = await fetch(`/api/returns/${returnData.id}/add-to-supplier-queue`, {
        method: "POST",
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || "Failed to add to supplier queue");
      }

      toast.success("Return added to supplier queue successfully");
      fetchReturn(); // Refresh data
    } catch (error) {
      console.error("Error adding to supplier queue:", error);
      toast.error(error instanceof Error ? error.message : "Failed to add to supplier queue");
    } finally {
      setActionLoading(false);
    }
  };

  const fetchStockCheck = async () => {
    if (!returnData) return;

    try {
      setStockCheckLoading(true);
      const response = await fetch(`/api/returns/${returnData.id}/stock-check`);
      if (!response.ok) {
        throw new Error("Failed to check stock availability");
      }
      const data = await response.json();
      setStockCheckData(data);
    } catch (error) {
      console.error("Error checking stock:", error);
      toast.error("Failed to check stock availability");
    } finally {
      setStockCheckLoading(false);
    }
  };

  const handleCustomerResolution = async () => {
    if (!returnData) return;

    try {
      setActionLoading(true);
      const response = await fetch(`/api/returns/${returnData.id}/customer-resolution`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          resolution: customerResolution,
          notes: customerResolutionNotes.trim() || undefined,
        }),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || "Failed to process customer resolution");
      }

      const resolutionLabel =
        customerResolution === "PENDING_REPLACEMENT"
          ? "scheduled for replacement when stock is available"
          : customerResolution.toLowerCase();

      toast.success(`Customer resolution (${resolutionLabel}) processed successfully`);
      setCustomerResolutionDialogOpen(false);
      setCustomerResolutionNotes("");
      setStockCheckData(null);
      fetchReturn(); // Refresh data
    } catch (error) {
      console.error("Error processing customer resolution:", error);
      toast.error(error instanceof Error ? error.message : "Failed to process customer resolution");
    } finally {
      setActionLoading(false);
    }
  };

  const handleCustomerResolutionDialogOpen = async (open: boolean) => {
    setCustomerResolutionDialogOpen(open);
    if (open) {
      // Reset form and fetch stock data when opening
      setCustomerResolution("REPLACEMENT");
      setCustomerResolutionNotes("");
      await fetchStockCheck();
    } else {
      // Clear stock data when closing
      setStockCheckData(null);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("id-ID", {
      style: "currency",
      currency: "IDR",
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("id-ID", {
      year: "numeric",
      month: "long",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  const getWorkflowStep = (status: string, disposition?: string) => {
    switch (status) {
      case "PENDING":
        return { step: 1, total: 2, message: "Awaiting Approval Decision" };
      case "APPROVED":
        return { step: 2, total: 2, message: "Awaiting Physical Return Completion" };
      case "COMPLETED":
        return { step: 2, total: 2, message: "Return Process Complete" };
      case "REJECTED":
        return { step: 0, total: 2, message: "Return Rejected" };
      default:
        return { step: 1, total: 2, message: "Processing" };
    }
  };

  const getStatusMessage = (status: string, disposition?: string) => {
    switch (status) {
      case "PENDING":
        return "Action Required: Review and approve or reject this return request";
      case "APPROVED":
        if (disposition === "RETURN_TO_STOCK") {
          return "Action Required: Confirm physical product has been returned to shelf";
        } else if (disposition === "DO_NOT_RETURN_TO_STOCK") {
          return "Action Required: Confirm defective product has been set aside for supplier return";
        }
        return "Action Required: Complete the return process";
      case "COMPLETED":
        if (disposition === "RETURN_TO_STOCK") {
          return "Return completed - Items have been returned to inventory";
        } else if (disposition === "DO_NOT_RETURN_TO_STOCK") {
          return "Return completed - Defective items have been added to supplier return queue";
        }
        return "Return process completed successfully";
      case "REJECTED":
        return "Return has been rejected and will not be processed";
      default:
        return "Processing return request";
    }
  };

  if (loading) {
    return (
      <MainLayout>
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        </div>
      </MainLayout>
    );
  }

  if (!returnData) {
    return (
      <MainLayout>
        <div className="text-center py-8">
          <h2 className="text-2xl font-bold text-gray-900">Return Not Found</h2>
          <p className="text-gray-600 mt-2">The return you're looking for doesn't exist.</p>
          <Link href="/inventory/returns">
            <Button className="mt-4">Back to Returns</Button>
          </Link>
        </div>
      </MainLayout>
    );
  }

  const canApprove = returnData.status === "PENDING";
  const canComplete = returnData.status === "APPROVED";
  const canCancel = returnData.status === "APPROVED";
  const canEdit = returnData.status === "PENDING";

  const workflowStep = getWorkflowStep(returnData.status, returnData.disposition);
  const statusMessage = getStatusMessage(returnData.status, returnData.disposition);

  return (
    <MainLayout>
      <div className="space-y-6">
        <PageHeader
          title={`Return #${returnData.id.slice(-8)}`}
          description="Return details and management"
        >
          <div className="flex gap-2">
            <Link href="/inventory/returns">
              <Button variant="outline">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Returns
              </Button>
            </Link>
            {canEdit && (
              <Link href={`/inventory/returns/${returnData.id}/edit`}>
                <Button variant="outline">
                  <Edit className="h-4 w-4 mr-2" />
                  Edit
                </Button>
              </Link>
            )}
          </div>
        </PageHeader>

        {/* Workflow Progress */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">Return Workflow Progress</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {/* Step Indicator */}
              <div className="flex items-center gap-4">
                <div className="flex items-center gap-2">
                  <div
                    className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                      workflowStep.step >= 1
                        ? "bg-primary text-primary-foreground"
                        : "bg-muted text-muted-foreground"
                    }`}
                  >
                    1
                  </div>
                  <span
                    className={workflowStep.step >= 1 ? "font-medium" : "text-muted-foreground"}
                  >
                    Approval
                  </span>
                </div>

                <ArrowRight
                  className={`h-4 w-4 ${workflowStep.step >= 2 ? "text-primary" : "text-muted-foreground"}`}
                />

                <div className="flex items-center gap-2">
                  <div
                    className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                      workflowStep.step >= 2
                        ? "bg-primary text-primary-foreground"
                        : "bg-muted text-muted-foreground"
                    }`}
                  >
                    2
                  </div>
                  <span
                    className={workflowStep.step >= 2 ? "font-medium" : "text-muted-foreground"}
                  >
                    Completion
                  </span>
                </div>
              </div>

              {/* Current Status */}
              <div className="flex items-center gap-2">
                <Badge variant="secondary" className={statusColors[returnData.status]}>
                  {statusIcons[returnData.status]}
                  <span className="ml-1">{returnData.status}</span>
                </Badge>
                <span className="text-sm text-muted-foreground">
                  Step {workflowStep.step} of {workflowStep.total}: {workflowStep.message}
                </span>
              </div>

              {/* Status Message */}
              <div
                className={`p-3 rounded-lg border ${
                  returnData.status === "PENDING" || returnData.status === "APPROVED"
                    ? "bg-yellow-50 border-yellow-200 text-yellow-800"
                    : returnData.status === "COMPLETED"
                      ? "bg-green-50 border-green-200 text-green-800"
                      : "bg-red-50 border-red-200 text-red-800"
                }`}
              >
                <div className="flex items-start gap-2">
                  {(returnData.status === "PENDING" || returnData.status === "APPROVED") && (
                    <AlertTriangle className="h-4 w-4 mt-0.5 flex-shrink-0" />
                  )}
                  <span className="text-sm font-medium">{statusMessage}</span>
                </div>
              </div>

              {/* Disposition Info */}
              {returnData.disposition && (
                <div className="space-y-2">
                  <Label className="text-sm font-medium">Product Disposition</Label>
                  <div className="flex items-center gap-2">
                    <Badge variant="outline">
                      {returnData.disposition === "RETURN_TO_STOCK"
                        ? "Return to Stock"
                        : "Do Not Return to Stock"}
                    </Badge>
                    {returnData.dispositionReason && (
                      <span className="text-sm text-muted-foreground">
                        - {returnData.dispositionReason}
                      </span>
                    )}
                  </div>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Actions */}
        <Card>
          <CardHeader>
            <div className="flex justify-between items-center">
              <CardTitle>Actions</CardTitle>
              <div className="flex gap-2">
                {canApprove && (
                  <Dialog open={dispositionDialogOpen} onOpenChange={setDispositionDialogOpen}>
                    <DialogTrigger asChild>
                      <Button disabled={actionLoading} className="bg-green-600 hover:bg-green-700">
                        <CheckCircle className="h-4 w-4 mr-2" />
                        Approve Return
                      </Button>
                    </DialogTrigger>
                    <DialogContent className="sm:max-w-md">
                      <DialogHeader>
                        <DialogTitle>Approve Return - Set Product Disposition</DialogTitle>
                        <DialogDescription>
                          Choose what should happen to the returned products. This decision affects
                          inventory and supplier returns.
                        </DialogDescription>
                      </DialogHeader>
                      <div className="space-y-4">
                        <div>
                          <Label className="text-base font-medium">Product Disposition</Label>
                          <RadioGroup
                            value={disposition}
                            onValueChange={(value: "RETURN_TO_STOCK" | "DO_NOT_RETURN_TO_STOCK") =>
                              setDisposition(value)
                            }
                            className="mt-2"
                          >
                            <div className="flex items-center space-x-2">
                              <RadioGroupItem value="RETURN_TO_STOCK" id="return-to-stock" />
                              <Label htmlFor="return-to-stock" className="font-normal">
                                <div>
                                  <div className="font-medium">Return to Stock</div>
                                  <div className="text-sm text-muted-foreground">
                                    Items are in good condition and can be resold
                                  </div>
                                </div>
                              </Label>
                            </div>
                            <div className="flex items-center space-x-2">
                              <RadioGroupItem value="DO_NOT_RETURN_TO_STOCK" id="do-not-return" />
                              <Label htmlFor="do-not-return" className="font-normal">
                                <div>
                                  <div className="font-medium">Do Not Return to Stock</div>
                                  <div className="text-sm text-muted-foreground">
                                    Items are defective, damaged, or expired
                                  </div>
                                </div>
                              </Label>
                            </div>
                          </RadioGroup>
                        </div>

                        {disposition === "DO_NOT_RETURN_TO_STOCK" && (
                          <div className="space-y-4">
                            <div>
                              <Label htmlFor="disposition-reason">
                                Reason for Not Returning to Stock
                              </Label>
                              <Textarea
                                id="disposition-reason"
                                value={dispositionReason}
                                onChange={(e) => setDispositionReason(e.target.value)}
                                placeholder="Explain why these items cannot be returned to stock (e.g., defective, damaged, expired)..."
                                rows={3}
                              />
                            </div>

                            <div className="flex items-start space-x-2">
                              <Checkbox
                                id="add-to-supplier-queue"
                                checked={addToSupplierQueue}
                                onCheckedChange={(checked) =>
                                  setAddToSupplierQueue(checked as boolean)
                                }
                              />
                              <div className="grid gap-1.5 leading-none">
                                <Label
                                  htmlFor="add-to-supplier-queue"
                                  className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                                >
                                  Add to Supplier Return Queue
                                </Label>
                                <p className="text-xs text-muted-foreground">
                                  Check this box to add defective items to the supplier return queue
                                  for potential return to supplier. Note: Consider supplier return
                                  policies and time limitations before selecting.
                                </p>
                              </div>
                            </div>
                          </div>
                        )}
                      </div>
                      <DialogFooter>
                        <Button variant="outline" onClick={() => setDispositionDialogOpen(false)}>
                          Cancel
                        </Button>
                        <Button onClick={handleApprove} disabled={actionLoading}>
                          {actionLoading ? "Approving..." : "Approve Return"}
                        </Button>
                      </DialogFooter>
                    </DialogContent>
                  </Dialog>
                )}

                {canApprove && (
                  <AlertDialog>
                    <AlertDialogTrigger asChild>
                      <Button variant="destructive" disabled={actionLoading}>
                        <XCircle className="h-4 w-4 mr-2" />
                        Reject
                      </Button>
                    </AlertDialogTrigger>
                    <AlertDialogContent>
                      <AlertDialogHeader>
                        <AlertDialogTitle>Reject Return</AlertDialogTitle>
                        <AlertDialogDescription>
                          Please provide a reason for rejecting this return.
                        </AlertDialogDescription>
                      </AlertDialogHeader>
                      <div className="py-4">
                        <Label htmlFor="rejection-reason">Rejection Reason</Label>
                        <Textarea
                          id="rejection-reason"
                          value={rejectionReason}
                          onChange={(e) => setRejectionReason(e.target.value)}
                          placeholder="Explain why this return is being rejected..."
                          rows={3}
                        />
                      </div>
                      <AlertDialogFooter>
                        <AlertDialogCancel>Cancel</AlertDialogCancel>
                        <AlertDialogAction
                          onClick={handleReject}
                          className="bg-destructive hover:bg-destructive/90"
                        >
                          Reject Return
                        </AlertDialogAction>
                      </AlertDialogFooter>
                    </AlertDialogContent>
                  </AlertDialog>
                )}

                {canComplete && (
                  <AlertDialog>
                    <AlertDialogTrigger asChild>
                      <Button disabled={actionLoading} className="bg-blue-600 hover:bg-blue-700">
                        <CheckCircle className="h-4 w-4 mr-2" />
                        Complete Return
                      </Button>
                    </AlertDialogTrigger>
                    <AlertDialogContent>
                      <AlertDialogHeader>
                        <AlertDialogTitle>Complete Return</AlertDialogTitle>
                        <AlertDialogDescription>
                          {returnData.disposition === "RETURN_TO_STOCK"
                            ? "Confirm that the physical products have been returned to the shelf and are ready for resale. This will add the items back to inventory."
                            : "Confirm that the defective products have been set aside for supplier return. This will NOT add items back to inventory."}
                        </AlertDialogDescription>
                      </AlertDialogHeader>
                      <AlertDialogFooter>
                        <AlertDialogCancel>Cancel</AlertDialogCancel>
                        <AlertDialogAction onClick={handleComplete}>
                          Complete Return
                        </AlertDialogAction>
                      </AlertDialogFooter>
                    </AlertDialogContent>
                  </AlertDialog>
                )}

                {canCancel && (
                  <AlertDialog>
                    <AlertDialogTrigger asChild>
                      <Button variant="destructive" disabled={actionLoading}>
                        <Ban className="h-4 w-4 mr-2" />
                        Cancel Return
                      </Button>
                    </AlertDialogTrigger>
                    <AlertDialogContent>
                      <AlertDialogHeader>
                        <AlertDialogTitle>Cancel Approved Return</AlertDialogTitle>
                        <AlertDialogDescription>
                          This will cancel the approved return and mark it as rejected. Any supplier
                          return queue entries will be removed.
                        </AlertDialogDescription>
                      </AlertDialogHeader>
                      <div className="py-4">
                        <Label htmlFor="cancel-reason">Cancellation Reason</Label>
                        <Textarea
                          id="cancel-reason"
                          value={cancelReason}
                          onChange={(e) => setCancelReason(e.target.value)}
                          placeholder="Explain why this approved return is being cancelled..."
                          rows={3}
                        />
                      </div>
                      <AlertDialogFooter>
                        <AlertDialogCancel>Cancel</AlertDialogCancel>
                        <AlertDialogAction
                          onClick={handleCancel}
                          className="bg-destructive hover:bg-destructive/90"
                        >
                          Cancel Return
                        </AlertDialogAction>
                      </AlertDialogFooter>
                    </AlertDialogContent>
                  </AlertDialog>
                )}

                {/* Post-completion actions for DO_NOT_RETURN_TO_STOCK returns */}
                {returnData.status === "COMPLETED" &&
                  returnData.disposition === "DO_NOT_RETURN_TO_STOCK" &&
                  !returnData.supplierReturnQueueId && (
                    <AlertDialog>
                      <AlertDialogTrigger asChild>
                        <Button variant="outline" disabled={actionLoading}>
                          <Package className="h-4 w-4 mr-2" />
                          Add to Supplier Queue
                        </Button>
                      </AlertDialogTrigger>
                      <AlertDialogContent>
                        <AlertDialogHeader>
                          <AlertDialogTitle>Add to Supplier Return Queue</AlertDialogTitle>
                          <AlertDialogDescription>
                            This will add the defective items from this return to the supplier
                            return queue. Only items with associated suppliers will be added.
                          </AlertDialogDescription>
                        </AlertDialogHeader>
                        <AlertDialogFooter>
                          <AlertDialogCancel>Cancel</AlertDialogCancel>
                          <AlertDialogAction onClick={handleAddToSupplierQueue}>
                            Add to Queue
                          </AlertDialogAction>
                        </AlertDialogFooter>
                      </AlertDialogContent>
                    </AlertDialog>
                  )}

                {/* Customer resolution for completed damaged returns */}
                {returnData.status === "COMPLETED" &&
                  returnData.disposition === "DO_NOT_RETURN_TO_STOCK" &&
                  (!returnData.customerResolution ||
                    returnData.customerResolution === "PENDING_REPLACEMENT") && (
                    <Dialog
                      open={customerResolutionDialogOpen}
                      onOpenChange={handleCustomerResolutionDialogOpen}
                    >
                      <DialogTrigger asChild>
                        <Button variant="outline" disabled={actionLoading}>
                          <User className="h-4 w-4 mr-2" />
                          {returnData.customerResolution === "PENDING_REPLACEMENT"
                            ? "Update Resolution"
                            : "Customer Resolution"}
                        </Button>
                      </DialogTrigger>
                      <DialogContent className="sm:max-w-2xl">
                        <DialogHeader>
                          <DialogTitle>
                            {returnData.customerResolution === "PENDING_REPLACEMENT"
                              ? "Update Customer Resolution"
                              : "Customer Resolution"}
                          </DialogTitle>
                          <DialogDescription>
                            Choose how to resolve this return for the customer. This affects
                            inventory and financial records.
                          </DialogDescription>
                        </DialogHeader>
                        <div className="space-y-4">
                          {/* Stock Check Loading */}
                          {stockCheckLoading && (
                            <div className="flex items-center gap-2 p-3 bg-blue-50 border border-blue-200 rounded-lg">
                              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
                              <span className="text-sm text-blue-700">
                                Checking stock availability...
                              </span>
                            </div>
                          )}

                          {/* Stock Summary */}
                          {stockCheckData && (
                            <div className="p-3 bg-gray-50 border border-gray-200 rounded-lg">
                              <h4 className="font-medium text-sm mb-2">Current Stock Levels:</h4>
                              <div className="space-y-1">
                                {stockCheckData.items.map((item: any) => (
                                  <div
                                    key={item.productId}
                                    className="flex justify-between items-center text-sm"
                                  >
                                    <span>{item.productName}</span>
                                    <div className="flex items-center gap-2">
                                      <span
                                        className={`font-medium ${item.hasSufficientStock ? "text-green-600" : "text-red-600"}`}
                                      >
                                        {item.currentStock} {item.unitAbbreviation} available
                                      </span>
                                      <span className="text-muted-foreground">
                                        (need {item.returnedQuantity})
                                      </span>
                                    </div>
                                  </div>
                                ))}
                              </div>
                              <div className="mt-2 pt-2 border-t border-gray-300">
                                <span
                                  className={`text-sm font-medium ${stockCheckData.canProcessReplacement ? "text-green-600" : "text-red-600"}`}
                                >
                                  {stockCheckData.summary.message}
                                </span>
                              </div>
                            </div>
                          )}

                          <div>
                            <Label className="text-base font-medium">Resolution Type</Label>
                            <RadioGroup
                              value={customerResolution}
                              onValueChange={(
                                value: "REPLACEMENT" | "REFUND" | "PENDING_REPLACEMENT"
                              ) => setCustomerResolution(value)}
                              className="mt-2"
                            >
                              <div className="flex items-center space-x-2">
                                <RadioGroupItem
                                  value="REPLACEMENT"
                                  id="replacement"
                                  disabled={stockCheckData && !stockCheckData.canProcessReplacement}
                                />
                                <Label
                                  htmlFor="replacement"
                                  className={`font-normal ${stockCheckData && !stockCheckData.canProcessReplacement ? "opacity-50" : ""}`}
                                >
                                  <div>
                                    <div className="font-medium">Product Replacement</div>
                                    <div className="text-sm text-muted-foreground">
                                      {stockCheckData && !stockCheckData.canProcessReplacement
                                        ? "Insufficient stock available for immediate replacement"
                                        : "Provide replacement products to customer (decreases store stock)"}
                                    </div>
                                  </div>
                                </Label>
                              </div>

                              {/* Schedule Replacement Option - only show if stock is insufficient */}
                              {stockCheckData && !stockCheckData.canProcessReplacement && (
                                <div className="flex items-center space-x-2">
                                  <RadioGroupItem
                                    value="PENDING_REPLACEMENT"
                                    id="pending-replacement"
                                  />
                                  <Label htmlFor="pending-replacement" className="font-normal">
                                    <div>
                                      <div className="font-medium">
                                        Schedule Replacement (Wait for Restock)
                                      </div>
                                      <div className="text-sm text-muted-foreground">
                                        Mark for replacement when stock becomes available
                                      </div>
                                    </div>
                                  </Label>
                                </div>
                              )}
                              <div className="flex items-center space-x-2">
                                <RadioGroupItem value="REFUND" id="refund" />
                                <Label htmlFor="refund" className="font-normal">
                                  <div>
                                    <div className="font-medium">Refund</div>
                                    <div className="text-sm text-muted-foreground">
                                      Refund the purchase amount (voids original transaction)
                                    </div>
                                  </div>
                                </Label>
                              </div>
                            </RadioGroup>
                          </div>

                          <div>
                            <Label htmlFor="customer-resolution-notes mt">
                              Resolution Notes (Optional)
                            </Label>
                            <Textarea
                              id="customer-resolution-notes"
                              value={customerResolutionNotes}
                              onChange={(e) => setCustomerResolutionNotes(e.target.value)}
                              placeholder="Add any additional notes about the customer resolution..."
                              rows={3}
                              className="mt-2"
                            />
                          </div>
                        </div>
                        <DialogFooter>
                          <Button
                            variant="outline"
                            onClick={() => setCustomerResolutionDialogOpen(false)}
                          >
                            Cancel
                          </Button>
                          <Button onClick={handleCustomerResolution} disabled={actionLoading}>
                            {actionLoading ? "Processing..." : `Process ${customerResolution}`}
                          </Button>
                        </DialogFooter>
                      </DialogContent>
                    </Dialog>
                  )}
              </div>
            </div>
          </CardHeader>
        </Card>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Return Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileText className="h-5 w-5" />
                Return Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label className="text-sm font-medium text-muted-foreground">Return ID</Label>
                  <p className="font-mono">{returnData.id}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium text-muted-foreground">Return Date</Label>
                  <p>{formatDate(returnData.returnDate)}</p>
                </div>
              </div>

              <div>
                <Label className="text-sm font-medium text-muted-foreground">Reason</Label>
                <p>{returnData.reason}</p>
              </div>

              {returnData.notes && (
                <div>
                  <Label className="text-sm font-medium text-muted-foreground">Notes</Label>
                  <p className="text-sm">{returnData.notes}</p>
                </div>
              )}

              {returnData.customerResolution && (
                <div>
                  <Label className="text-sm font-medium text-muted-foreground">
                    Customer Resolution
                  </Label>
                  <div className="space-y-1">
                    <div className="flex items-center gap-2">
                      <Badge
                        variant="outline"
                        className={
                          returnData.customerResolution === "PENDING_REPLACEMENT"
                            ? "bg-yellow-50 text-yellow-700 border-yellow-200"
                            : "bg-blue-50 text-blue-700 border-blue-200"
                        }
                      >
                        {returnData.customerResolution === "REPLACEMENT"
                          ? "Product Replacement"
                          : returnData.customerResolution === "PENDING_REPLACEMENT"
                            ? "Pending Replacement"
                            : "Refund"}
                      </Badge>
                      {returnData.customerResolutionProcessedAt && (
                        <span className="text-xs text-muted-foreground">
                          Processed {formatDate(returnData.customerResolutionProcessedAt)}
                        </span>
                      )}
                    </div>
                    {returnData.customerResolutionNotes && (
                      <p className="text-sm text-muted-foreground">
                        {returnData.customerResolutionNotes}
                      </p>
                    )}
                  </div>
                </div>
              )}

              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <Label className="text-sm font-medium text-muted-foreground">Created</Label>
                  <p>{formatDate(returnData.createdAt)}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium text-muted-foreground">Last Updated</Label>
                  <p>{formatDate(returnData.updatedAt)}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Customer & Transaction Info */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <User className="h-5 w-5" />
                Customer & Transaction
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label className="text-sm font-medium text-muted-foreground">Customer</Label>
                {returnData.customer ? (
                  <>
                    <p className="font-medium">{returnData.customer.name}</p>
                    {returnData.customer.phone && (
                      <p className="text-sm text-muted-foreground">{returnData.customer.phone}</p>
                    )}
                    {returnData.customer.email && (
                      <p className="text-sm text-muted-foreground">{returnData.customer.email}</p>
                    )}
                  </>
                ) : (
                  <p className="font-medium text-muted-foreground">Walk-in Customer</p>
                )}
              </div>

              <Separator />

              <div>
                <Label className="text-sm font-medium text-muted-foreground">
                  Original Transaction
                </Label>
                <div className="space-y-1">
                  <p className="font-mono">#{returnData.transaction.id.slice(-8)}</p>
                  <p className="text-sm text-muted-foreground">
                    {formatDate(returnData.transaction.transactionDate)}
                  </p>
                  <p className="text-sm text-muted-foreground">
                    Cashier: {returnData.transaction.cashier.name}
                  </p>
                  <p className="font-medium">
                    Total: {formatCurrency(returnData.transaction.total)}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Return Items */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Package className="h-5 w-5" />
              Return Items ({returnData.items.length})
            </CardTitle>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Product</TableHead>
                  <TableHead>SKU</TableHead>
                  <TableHead>Category</TableHead>
                  <TableHead>Unit Price</TableHead>
                  <TableHead>Quantity</TableHead>
                  <TableHead>Unit</TableHead>
                  <TableHead>Subtotal</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {returnData.items.map((item) => (
                  <TableRow key={item.id}>
                    <TableCell className="font-medium">{item.product.name}</TableCell>
                    <TableCell className="font-mono text-sm">{item.product.sku}</TableCell>
                    <TableCell>{item.product.category?.name || "-"}</TableCell>
                    <TableCell>{formatCurrency(item.unitPrice)}</TableCell>
                    <TableCell>{item.quantity}</TableCell>
                    <TableCell>{item.product.unit.abbreviation}</TableCell>
                    <TableCell>{formatCurrency(item.subtotal)}</TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>

            <div className="mt-4 pt-4 border-t">
              <div className="flex justify-between items-center">
                <span className="font-medium">Total Return Amount:</span>
                <span className="text-lg font-bold">{formatCurrency(returnData.total)}</span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </MainLayout>
  );
}
