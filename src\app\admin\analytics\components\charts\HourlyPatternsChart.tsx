"use client";

import { useState, useEffect } from "react";
import { BaseChart } from "@/components/charts/BaseChart";
import {
  AreaChart,
  Area,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  Legend,
} from "recharts";
import { formatCurrency, CHART_COLORS } from "@/lib/analytics/chartUtils";
import { HourlyPatternData, AnalyticsFilters } from "@/lib/types/analytics";

interface HourlyPatternsChartProps {
  filters: AnalyticsFilters;
  className?: string;
}

export function HourlyPatternsChart({ filters, className }: HourlyPatternsChartProps) {
  const [data, setData] = useState<HourlyPatternData[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchData = async () => {
    try {
      setIsLoading(true);
      setError(null);

      // Build query parameters
      const params = new URLSearchParams();

      // Date range
      if (filters.dateRange.from && filters.dateRange.to) {
        params.append("fromDate", filters.dateRange.from.toISOString());
        params.append("toDate", filters.dateRange.to.toISOString());
      }

      // Other filters
      if (filters.cashierIds && filters.cashierIds.length > 0) {
        params.append("cashierIds", filters.cashierIds.join(","));
      }
      if (filters.terminalIds && filters.terminalIds.length > 0) {
        params.append("terminalIds", filters.terminalIds.join(","));
      }
      if (filters.categoryIds && filters.categoryIds.length > 0) {
        params.append("categoryIds", filters.categoryIds.join(","));
      }
      if (filters.paymentMethods && filters.paymentMethods.length > 0) {
        params.append("paymentMethods", filters.paymentMethods.join(","));
      }

      console.log("[HourlyPatternsChart] Fetching data with params:", params.toString());

      const response = await fetch(`/api/analytics/hourly-patterns?${params.toString()}`);

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();

      console.log("Hourly patterns result success:", result.success);

      if (!result.success) {
        console.error("Hourly patterns result error:", result.error);
        throw new Error(result.error || "Failed to fetch hourly patterns");
      }

      setData(result.data);
    } catch (err) {
      console.error("Error fetching hourly patterns:", err);
      setError(err instanceof Error ? err.message : "An error occurred");
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
  }, [filters]);

  const handleRefresh = () => {
    fetchData();
  };

  // Custom tooltip
  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length && payload[0]?.payload) {
      const data = payload[0].payload;
      const hour = parseInt(label) || 0;
      const timeLabel = `${hour.toString().padStart(2, "0")}:00 - ${(hour + 1).toString().padStart(2, "0")}:00`;

      return (
        <div className="bg-white p-3 border border-gray-200 rounded-lg shadow-lg">
          <p className="font-medium text-gray-900">{timeLabel}</p>
          <div className="mt-2 space-y-1">
            <p className="text-sm">
              <span className="text-blue-600">Revenue: </span>
              <span className="font-medium">{formatCurrency(data.revenue || 0)}</span>
            </p>
            <p className="text-sm">
              <span className="text-green-600">Transactions: </span>
              <span className="font-medium">{(data.transactions || 0).toLocaleString()}</span>
            </p>
            <p className="text-sm">
              <span className="text-purple-600">Avg Order Value: </span>
              <span className="font-medium">{formatCurrency(data.averageOrderValue || 0)}</span>
            </p>
          </div>
        </div>
      );
    }
    return null;
  };

  // Format hour for display
  const formatHour = (hour: number) => {
    return `${hour.toString().padStart(2, "0")}:00`;
  };

  // Ensure data is always an array
  const chartData = Array.isArray(data) ? data : [];

  return (
    <BaseChart
      title="Hourly Sales Patterns"
      subtitle="Revenue and transaction patterns by hour of day"
      isLoading={isLoading}
      error={error}
      onRefresh={handleRefresh}
      className={className}
    >
      <ResponsiveContainer width="100%" height={300}>
        <AreaChart data={chartData} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
          <defs>
            <linearGradient id="revenueGradientHourly" x1="0" y1="0" x2="0" y2="1">
              <stop offset="5%" stopColor={CHART_COLORS.primary[0]} stopOpacity={0.8} />
              <stop offset="95%" stopColor={CHART_COLORS.primary[0]} stopOpacity={0.1} />
            </linearGradient>
            <linearGradient id="transactionsGradientHourly" x1="0" y1="0" x2="0" y2="1">
              <stop offset="5%" stopColor={CHART_COLORS.success[0]} stopOpacity={0.8} />
              <stop offset="95%" stopColor={CHART_COLORS.success[0]} stopOpacity={0.1} />
            </linearGradient>
          </defs>
          <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
          <XAxis dataKey="hour" tickFormatter={formatHour} stroke="#666" fontSize={12} />
          <YAxis
            yAxisId="revenue"
            orientation="left"
            tickFormatter={(value) => formatCurrency(value)}
            stroke="#666"
            fontSize={12}
          />
          <YAxis yAxisId="transactions" orientation="right" stroke="#666" fontSize={12} />
          <Tooltip content={<CustomTooltip />} />
          <Legend />
          <Area
            yAxisId="revenue"
            type="monotone"
            dataKey="revenue"
            stroke={CHART_COLORS.primary[0]}
            fill="url(#revenueGradientHourly)"
            strokeWidth={2}
            name="Revenue"
          />
          <Area
            yAxisId="transactions"
            type="monotone"
            dataKey="transactions"
            stroke={CHART_COLORS.success[0]}
            fill="url(#transactionsGradientHourly)"
            strokeWidth={2}
            name="Transactions"
          />
        </AreaChart>
      </ResponsiveContainer>
    </BaseChart>
  );
}
