import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { ShoppingCart } from "lucide-react";
import { Terminal } from "lucide-react";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { Button } from "@/components/ui/button";

export default function Home() {
  return (
    <div className="flex flex-col items-center justify-center min-h-screen p-8 bg-gradient-to-b from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800">
      <main className="flex flex-col items-center justify-center w-full max-w-4xl gap-8 text-center">
        <div className="flex items-center justify-center gap-3">
          <ShoppingCart className="w-12 h-12 text-blue-600" />
          <h1 className="text-5xl font-bold tracking-tight text-gray-900 dark:text-white sm:text-6xl">
            Next POS
          </h1>
        </div>
        <p className="text-xl text-gray-600 dark:text-gray-300">
          A modern point of sale and inventory management system
        </p>

        <div className="w-full max-w-2xl mt-4">
          <Alert className="mb-4">
            <AlertTitle>Welcome to Next POS!</AlertTitle>
            <AlertDescription>
              This is a simple landing page to verify that shadcn/ui components are working
              correctly.
            </AlertDescription>
          </Alert>

          <Alert>
            <Terminal className="h-4 w-4" />
            <AlertTitle>Heads up!</AlertTitle>
            <AlertDescription>You can add components to your app using the cli.</AlertDescription>
          </Alert>

          <Alert variant="destructive" className="mb-4">
            <AlertTitle>Important Notice</AlertTitle>
            <AlertDescription>
              This application is currently in development. Some features may not be available yet.
            </AlertDescription>
          </Alert>

          <Alert variant="success">
            <AlertTitle>Success!</AlertTitle>
            <AlertDescription>
              shadcn/ui components are installed and working correctly.
            </AlertDescription>
          </Alert>
        </div>

        <div className="flex flex-col items-center justify-center gap-4 mt-8 sm:flex-row">
          <Button
            className="px-6 py-3 text-white bg-blue-600 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
            asChild
          >
            <a href="/dashboard">Go to Dashboard</a>
          </Button>

          <AlertDialog>
            <AlertDialogTrigger asChild>
              <Button
                variant="outline"
                className="px-6 py-3 text-blue-600 bg-white border border-blue-600 rounded-md hover:bg-blue-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
              >
                Learn More
              </Button>
            </AlertDialogTrigger>
            <AlertDialogContent>
              <AlertDialogHeader>
                <AlertDialogTitle>Welcome to Next POS</AlertDialogTitle>
                <AlertDialogDescription>
                  This is a modern point of sale and inventory management system built with Next.js
                  and shadcn/ui components.
                </AlertDialogDescription>
              </AlertDialogHeader>
              <AlertDialogFooter>
                <AlertDialogCancel>Cancel</AlertDialogCancel>
                <AlertDialogAction>Continue</AlertDialogAction>
              </AlertDialogFooter>
            </AlertDialogContent>
          </AlertDialog>

          <Button variant="secondary" className="px-6 py-3" asChild>
            <a href="/admin/backup">Database Backup</a>
          </Button>
        </div>
      </main>

      <footer className="mt-16 text-sm text-gray-500 dark:text-gray-400">
        <p>© 2024 Next POS. All rights reserved.</p>
      </footer>
    </div>
  );
}
