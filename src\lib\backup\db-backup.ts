import { exec } from 'child_process';
import fs from 'fs';
import path from 'path';
import util from 'util';
import { PrismaClient } from '@/generated/prisma';
import { addBackupHistoryEntry } from './backup-history';

// Convert exec to Promise-based
const execPromise = util.promisify(exec);

// Default backup directory
const DEFAULT_BACKUP_DIR = path.join(process.cwd(), 'backups');

// Database connection info - should match your Prisma config
const DB_CONFIG = {
  host: 'localhost',
  port: 5432,
  database: 'npos',
  user: 'postgres',
  password: 'admin',
};

// Get the current schema version from the Prisma schema file
export const getCurrentSchemaVersion = (): string => {
  try {
    // Try to read schema version from a version file if it exists
    const versionFilePath = path.join(process.cwd(), 'prisma', 'schema_version.txt');
    if (fs.existsSync(versionFilePath)) {
      return fs.readFileSync(versionFilePath, 'utf8').trim();
    }

    // If no version file exists, calculate a hash based on the schema structure
    const schemaPath = path.join(process.cwd(), 'prisma', 'schema.prisma');
    if (!fs.existsSync(schemaPath)) {
      return 'unknown';
    }

    const schemaContent = fs.readFileSync(schemaPath, 'utf8');

    // Extract model definitions to create a version identifier
    // This is a simple approach - in a production system you might want a more sophisticated versioning strategy
    const modelMatches = schemaContent.match(/model\s+\w+\s+{[^}]+}/g) || [];
    const enumMatches = schemaContent.match(/enum\s+\w+\s+{[^}]+}/g) || [];

    // Count the number of models and enums as a simple version indicator
    const modelCount = modelMatches.length;
    const enumCount = enumMatches.length;

    // Create a simple hash based on the number of models and their names
    const modelNames = modelMatches.map(model => {
      const nameMatch = model.match(/model\s+(\w+)/);
      return nameMatch ? nameMatch[1] : '';
    }).sort().join('-');

    // Create a version string that includes the counts and a hash of the model names
    return `${modelCount}.${enumCount}.${Buffer.from(modelNames).toString('base64').substring(0, 8)}`;
  } catch (error) {
    console.error('Error determining schema version:', error);
    return 'unknown';
  }
};

// Ensure backup directory exists
export const ensureBackupDir = (backupDir = DEFAULT_BACKUP_DIR): string => {
  if (!fs.existsSync(backupDir)) {
    fs.mkdirSync(backupDir, { recursive: true });
  }
  return backupDir;
};

// Generate a filename for the backup
export const generateBackupFilename = (prefix = 'npos-backup'): string => {
  const now = new Date();
  const timestamp = now.toISOString().replace(/[:.]/g, '-');
  return `${prefix}-${timestamp}.sql`;
};

// Check if PostgreSQL tools are available
export const checkPgToolsAvailable = async (): Promise<boolean> => {
  try {
    await execPromise('pg_dump --version');
    return true;
  } catch (error) {
    return false;
  }
};

// Create a backup using Prisma (fallback method)
export const createPrismaBackup = async (
  options: {
    backupDir?: string;
    filename?: string;
    comment?: string;
  } = {}
): Promise<{ filePath: string; size: number; timestamp: Date }> => {
  const backupDir = ensureBackupDir(options.backupDir);
  const filename = options.filename || generateBackupFilename();
  const filePath = path.join(backupDir, filename);

  try {
    // Create a new Prisma client
    const prisma = new PrismaClient();

    // Create a JSON structure to store the data
    const backupData: {
      metadata: {
        timestamp: Date;
        comment: string;
        tables: string[];
        database: string;
        version: string;
        schemaVersion: string;
      };
      data: Record<string, any[]>;
    } = {
      metadata: {
        timestamp: new Date(),
        comment: options.comment || '',
        tables: [],
        database: DB_CONFIG.database,
        version: '1.0',
        schemaVersion: getCurrentSchemaVersion(),
      },
      data: {},
    };

    // Manually define the tables we know exist in our schema
    // This is a simpler approach than trying to query the information_schema
    const knownTables = [
      'User',
      'Session',
      // 'ActivityLog' - Excluded to preserve activity logs during restore
      'Product',
      'Category',
      'Unit',
      'Inventory',
      'Transaction',
      'TransactionItem',
      'Payment',
      'Customer',
      'PurchaseOrder',
      'PurchaseOrderItem',
      'Supplier'
    ];

    // For each table, export the data
    for (const tableName of knownTables) {
      try {
        // Use the Prisma client's dynamic access to get all records
        // This is safer than raw SQL queries
        const records = await (prisma as any)[tableName].findMany();

        if (records && records.length > 0) {
          backupData.metadata.tables.push(tableName);
          backupData.data[tableName] = records;
        }
      } catch (tableError: any) {
        console.warn(`Could not backup table ${tableName}: ${tableError?.message || String(tableError)}`);
        // Skip tables that don't exist or can't be accessed
      }
    }

    // Write the backup to a file
    fs.writeFileSync(filePath, JSON.stringify(backupData, null, 2));

    // Get file stats
    const stats = fs.statSync(filePath);

    // Create metadata file with additional information
    const metadataPath = `${filePath}.json`;
    const metadata = {
      filename,
      timestamp: new Date(),
      size: stats.size,
      comment: options.comment || '',
      method: 'prisma',
      schemaVersion: getCurrentSchemaVersion(),
      excludedTables: ['ActivityLog'],
    };

    fs.writeFileSync(metadataPath, JSON.stringify(metadata, null, 2));

    // Close the Prisma client
    await prisma.$disconnect();

    // Log the successful backup operation
    addBackupHistoryEntry({
      type: 'backup',
      timestamp: new Date().toISOString(),
      filePath,
      fileName: filename,
      method: 'prisma',
      success: true,
      message: 'Backup created successfully using Prisma',
      size: stats.size,
      comment: options.comment,
    });

    return {
      filePath,
      size: stats.size,
      timestamp: new Date(),
    };
  } catch (error: any) {
    console.error('Prisma backup failed:', error);

    // Log the failed backup attempt
    addBackupHistoryEntry({
      type: 'backup',
      timestamp: new Date().toISOString(),
      filePath: filePath,
      fileName: filename,
      method: 'prisma',
      success: false,
      message: `Prisma backup failed: ${error?.message || String(error)}`,
    });

    throw new Error(`Database backup failed (Prisma method): ${error?.message || String(error)}`);
  }
};

// Create a backup of the database
export const createBackup = async (
  options: {
    backupDir?: string;
    filename?: string;
    comment?: string;
    forcePrismaMethod?: boolean;
  } = {}
): Promise<{ filePath: string; size: number; timestamp: Date }> => {
  const backupDir = ensureBackupDir(options.backupDir);
  const filename = options.filename || generateBackupFilename();
  const filePath = path.join(backupDir, filename);

  // Check if PostgreSQL tools are available
  const pgToolsAvailable = options.forcePrismaMethod ? false : await checkPgToolsAvailable();

  if (pgToolsAvailable) {
    // Use pg_dump method
    try {
      // Create pg_dump command with exclusion for ActivityLog table
      const pgDumpCmd = `pg_dump -h ${DB_CONFIG.host} -p ${DB_CONFIG.port} -U ${DB_CONFIG.user} -d ${DB_CONFIG.database} -F c -T "ActivityLog" -f "${filePath}"`;

      // Execute pg_dump
      await execPromise(pgDumpCmd);

      // Get file stats
      const stats = fs.statSync(filePath);

      // Create metadata file with additional information
      const metadataPath = `${filePath}.json`;
      const metadata = {
        filename,
        timestamp: new Date(),
        size: stats.size,
        comment: options.comment || '',
        method: 'pg_dump',
        schemaVersion: getCurrentSchemaVersion(),
        excludedTables: ['ActivityLog'],
      };

      fs.writeFileSync(metadataPath, JSON.stringify(metadata, null, 2));

      // Log the successful backup operation
      addBackupHistoryEntry({
        type: 'backup',
        timestamp: new Date().toISOString(),
        filePath,
        fileName: filename,
        method: 'pg_dump',
        success: true,
        message: 'Backup created successfully using pg_dump',
        size: stats.size,
        comment: options.comment,
      });

      return {
        filePath,
        size: stats.size,
        timestamp: new Date(),
      };
    } catch (error) {
      console.error('pg_dump backup failed:', error);
      console.log('Falling back to Prisma backup method...');

      // Log the failed backup attempt
      addBackupHistoryEntry({
        type: 'backup',
        timestamp: new Date().toISOString(),
        filePath: filePath,
        fileName: filename,
        method: 'pg_dump',
        success: false,
        message: `pg_dump backup failed: ${(error as any)?.message || String(error)}`,
      });

      // If pg_dump fails, try the Prisma method
      return createPrismaBackup(options);
    }
  } else {
    // Use Prisma method
    console.log('PostgreSQL tools not available, using Prisma backup method...');
    return createPrismaBackup(options);
  }
};

// Validate schema compatibility between backup and current schema
export const validateSchemaCompatibility = async (
  backupFilePath: string
): Promise<{ compatible: boolean; backupSchemaVersion: string; currentSchemaVersion: string; message: string }> => {
  const currentSchemaVersion = getCurrentSchemaVersion();
  let backupSchemaVersion = 'unknown';

  // Check if metadata file exists
  const metadataPath = `${backupFilePath}.json`;
  if (fs.existsSync(metadataPath)) {
    try {
      const metadata = JSON.parse(fs.readFileSync(metadataPath, 'utf8'));
      backupSchemaVersion = metadata.schemaVersion || 'unknown';

      // If the backup was created with an older version that didn't track schema version
      if (backupSchemaVersion === 'unknown' && metadata.method) {
        return {
          compatible: false,
          backupSchemaVersion,
          currentSchemaVersion,
          message: 'This backup was created with an older version that did not track schema versions. Proceed with caution.'
        };
      }
    } catch (e) {
      console.warn(`Could not parse metadata for ${backupFilePath}`);
    }
  }

  // If we couldn't determine the backup schema version
  if (backupSchemaVersion === 'unknown') {
    return {
      compatible: false,
      backupSchemaVersion,
      currentSchemaVersion,
      message: 'Could not determine the schema version of this backup. Proceed with caution.'
    };
  }

  // Compare schema versions
  const compatible = backupSchemaVersion === currentSchemaVersion;

  return {
    compatible,
    backupSchemaVersion,
    currentSchemaVersion,
    message: compatible
      ? 'Schema versions match. Safe to restore.'
      : `Schema version mismatch. Backup schema (${backupSchemaVersion}) differs from current schema (${currentSchemaVersion}). Restoring may cause data inconsistencies.`
  };
};

// Restore database from backup
export const restoreBackup = async (
  backupFilePath: string
): Promise<{ success: boolean; message: string; schemaValidation?: any }> => {
  if (!fs.existsSync(backupFilePath)) {
    throw new Error(`Backup file not found: ${backupFilePath}`);
  }

  // Note: Schema validation is now handled separately before calling this function
  // This function assumes the schema has already been validated and is compatible

  // Check if this is a Prisma backup, pg_dump backup, or external upload
  const metadataPath = `${backupFilePath}.json`;
  let isPrismaBackup = false;
  let isExternalUpload = false;

  if (fs.existsSync(metadataPath)) {
    try {
      const metadata = JSON.parse(fs.readFileSync(metadataPath, 'utf8'));
      isPrismaBackup = metadata.method === 'prisma';
      isExternalUpload = metadata.isExternalUpload === true;

      // If this is an external upload and PostgreSQL tools are not available,
      // we'll need to try the Prisma method even if it's not a Prisma backup
      if (isExternalUpload) {
        console.log('This is an external upload. Will check for PostgreSQL tools availability.');
      }
    } catch (e) {
      console.warn(`Could not parse metadata for ${backupFilePath}`);
    }
  } else {
    // Try to determine by file content
    try {
      const content = fs.readFileSync(backupFilePath, 'utf8');
      isPrismaBackup = content.startsWith('{') && content.includes('"metadata":');
    } catch (e) {
      console.warn(`Could not read backup file ${backupFilePath}`);
    }
  }

  // Get schema validation info
  const schemaValidation = await validateSchemaCompatibility(backupFilePath);

  if (isPrismaBackup) {
    // Restore using Prisma method
    const result = await restorePrismaBackup(backupFilePath);
    return { ...result, schemaValidation };
  } else {
    // Check if PostgreSQL tools are available
    const pgToolsAvailable = await checkPgToolsAvailable();

    if (pgToolsAvailable) {
      // Restore using pg_restore
      const result = await restorePgBackup(backupFilePath);
      return { ...result, schemaValidation };
    } else {
      // PostgreSQL tools not available, try to use Prisma method as fallback
      console.log('PostgreSQL tools not available, attempting to use Prisma method as fallback...');

      try {
        // Try to parse the SQL file as if it were a Prisma backup
        const result = await restorePrismaBackup(backupFilePath);
        return { ...result, schemaValidation };
      } catch (error) {
        // If Prisma method fails, throw a more helpful error
        const errorMessage = isExternalUpload
          ? 'This external SQL file cannot be restored without PostgreSQL tools. ' +
            'The system attempted to use the Prisma fallback method, but the file format is not compatible. ' +
            'Please install PostgreSQL tools or use a Prisma-compatible backup format.'
          : 'PostgreSQL tools (pg_restore) are not available and the backup could not be restored using the Prisma method. ' +
            'Please install PostgreSQL tools or convert this backup to a Prisma-compatible format.';

        throw new Error(errorMessage);
      }
    }
  }
};

// Restore database from a pg_dump backup
export const restorePgBackup = async (
  backupFilePath: string
): Promise<{ success: boolean; message: string }> => {
  // Check if PostgreSQL tools are available
  const pgToolsAvailable = await checkPgToolsAvailable();

  if (!pgToolsAvailable) {
    throw new Error('PostgreSQL tools (pg_restore) are not available. Cannot restore this backup format.');
  }

  // First, backup the ActivityLog table
  console.log('Backing up ActivityLog table before restore...');
  const prisma = new PrismaClient();
  const activityLogs = await prisma.activityLog.findMany();

  // Create pg_restore command
  const pgRestoreCmd = `pg_restore -h ${DB_CONFIG.host} -p ${DB_CONFIG.port} -U ${DB_CONFIG.user} -d ${DB_CONFIG.database} -c "${backupFilePath}"`;

  try {
    // Execute pg_restore
    await execPromise(pgRestoreCmd);

    // Restore the ActivityLog table
    console.log(`Restoring ${activityLogs.length} activity logs...`);
    if (activityLogs.length > 0) {
      await prisma.activityLog.createMany({
        data: activityLogs,
        skipDuplicates: true,
      });
    }

    // Close the Prisma client
    await prisma.$disconnect();

    // Log the successful restore operation
    addBackupHistoryEntry({
      type: 'restore',
      timestamp: new Date().toISOString(),
      filePath: backupFilePath,
      fileName: path.basename(backupFilePath),
      method: 'pg_dump',
      success: true,
      message: `Database restored successfully from ${path.basename(backupFilePath)}`,
    });

    return {
      success: true,
      message: `Database restored successfully from ${path.basename(backupFilePath)}`,
    };
  } catch (error: any) {
    console.error('Restore failed:', error);

    // Log the failed restore attempt
    addBackupHistoryEntry({
      type: 'restore',
      timestamp: new Date().toISOString(),
      filePath: backupFilePath,
      fileName: path.basename(backupFilePath),
      method: 'pg_dump',
      success: false,
      message: `Restore failed: ${error?.message || String(error)}`,
    });

    throw new Error(`Database restore failed: ${error?.message || String(error)}`);
  }
};

// Restore database from a Prisma backup
export const restorePrismaBackup = async (
  backupFilePath: string
): Promise<{ success: boolean; message: string }> => {
  try {
    // Read the backup file
    const backupContent = fs.readFileSync(backupFilePath, 'utf8');

    // Try to parse as JSON
    let backupData;
    try {
      backupData = JSON.parse(backupContent);
    } catch (parseError) {
      // If it's not valid JSON, it might be a plain SQL file
      // For now, we can't handle plain SQL files without pg_restore
      throw new Error('The backup file is not in a Prisma-compatible format (not valid JSON).');
    }

    // Validate that it has the expected structure
    if (!backupData.metadata || !backupData.data || !Array.isArray(backupData.metadata.tables)) {
      throw new Error('The backup file is not in a valid Prisma backup format. Missing required metadata or data structure.');
    }

    // Create a new Prisma client
    const prisma = new PrismaClient();

    // First, backup the ActivityLog table
    console.log('Backing up ActivityLog table before restore...');
    const activityLogs = await prisma.activityLog.findMany();

    // For each table in the backup, restore the data
    for (const tableName of backupData.metadata.tables) {
      try {
        // We'll handle ActivityLog separately at the end
        if (tableName === 'ActivityLog') {
          console.log('ActivityLog table will be handled separately');
          continue;
        }

        // Use the Prisma client's dynamic access to clear and insert data
        // This is safer than raw SQL queries

        // First, delete all records from the table
        try {
          await (prisma as any)[tableName].deleteMany({});
        } catch (deleteError: any) {
          console.warn(`Could not clear table ${tableName}: ${deleteError?.message || String(deleteError)}`);
          // Continue anyway, as the table might be empty
        }

        // Get the records for this table
        const records = backupData.data[tableName];

        // If there are records, insert them
        if (records && records.length > 0) {
          // Use createMany to insert all records at once
          try {
            await (prisma as any)[tableName].createMany({
              data: records,
              skipDuplicates: true, // Skip records that would cause unique constraint violations
            });
          } catch (createManyError: any) {
            console.warn(`Could not use createMany for table ${tableName}, falling back to individual inserts: ${createManyError?.message || String(createManyError)}`);

            // If createMany fails, fall back to individual inserts
            for (const record of records) {
              try {
                await (prisma as any)[tableName].create({
                  data: record,
                });
              } catch (insertError: any) {
                console.warn(`Could not insert record into ${tableName}: ${insertError?.message || String(insertError)}`);
                // Continue with next record
              }
            }
          }
        }
      } catch (tableError: any) {
        console.warn(`Could not restore table ${tableName}: ${tableError?.message || String(tableError)}`);
        // Continue with next table
      }
    }

    // Restore the ActivityLog table
    console.log(`Restoring ${activityLogs.length} activity logs...`);
    if (activityLogs.length > 0) {
      await prisma.activityLog.createMany({
        data: activityLogs,
        skipDuplicates: true,
      });
    }

    // Close the Prisma client
    await prisma.$disconnect();

    // Log the successful restore operation
    addBackupHistoryEntry({
      type: 'restore',
      timestamp: new Date().toISOString(),
      filePath: backupFilePath,
      fileName: path.basename(backupFilePath),
      method: 'prisma',
      success: true,
      message: `Database restored successfully from ${path.basename(backupFilePath)} (Prisma method)`,
    });

    return {
      success: true,
      message: `Database restored successfully from ${path.basename(backupFilePath)} (Prisma method)`,
    };
  } catch (error: any) {
    console.error('Prisma restore failed:', error);

    // Log the failed restore attempt
    addBackupHistoryEntry({
      type: 'restore',
      timestamp: new Date().toISOString(),
      filePath: backupFilePath,
      fileName: path.basename(backupFilePath),
      method: 'prisma',
      success: false,
      message: `Prisma restore failed: ${error?.message || String(error)}`,
    });

    throw new Error(`Database restore failed (Prisma method): ${error?.message || String(error)}`);
  }
};

// List all available backups
export const listBackups = (
  backupDir = DEFAULT_BACKUP_DIR
): Array<{
  filename: string;
  path: string;
  timestamp: Date;
  size: number;
  comment?: string;
  schemaVersion?: string;
  currentSchemaVersion?: string;
  schemaCompatible?: boolean;
  method?: string;
}> => {
  ensureBackupDir(backupDir);

  // Get all .sql files in the backup directory
  const files = fs.readdirSync(backupDir)
    .filter(file => file.endsWith('.sql'))
    .map(filename => {
      const filePath = path.join(backupDir, filename);
      const stats = fs.statSync(filePath);

      // Try to read metadata if available
      let metadata: any = {};
      const metadataPath = `${filePath}.json`;
      if (fs.existsSync(metadataPath)) {
        try {
          metadata = JSON.parse(fs.readFileSync(metadataPath, 'utf8'));
        } catch (e) {
          console.warn(`Could not parse metadata for ${filename}`);
        }
      }

      // Get the current schema version for comparison
      const currentSchemaVersion = getCurrentSchemaVersion();

      return {
        filename,
        path: filePath,
        timestamp: metadata.timestamp ? new Date(metadata.timestamp) : stats.mtime,
        size: stats.size,
        comment: metadata.comment,
        schemaVersion: metadata.schemaVersion || 'unknown',
        currentSchemaVersion,
        schemaCompatible: metadata.schemaVersion === currentSchemaVersion,
        method: metadata.method || 'unknown',
      };
    });

  // Sort by timestamp (newest first)
  return files.sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());
};

// Delete a backup
export const deleteBackup = (
  backupFilePath: string
): boolean => {
  if (!fs.existsSync(backupFilePath)) {
    throw new Error(`Backup file not found: ${backupFilePath}`);
  }

  // Delete the backup file
  fs.unlinkSync(backupFilePath);

  // Delete metadata file if it exists
  const metadataPath = `${backupFilePath}.json`;
  if (fs.existsSync(metadataPath)) {
    fs.unlinkSync(metadataPath);
  }

  return true;
};
