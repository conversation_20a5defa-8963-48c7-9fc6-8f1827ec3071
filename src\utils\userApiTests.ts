import { createMockAuthRequest, runApiTest, mockPrisma } from "./testUtils";
import { NextResponse } from "next/server";

// Mock API handlers for User API

// Mock Users API
const getUsers = async (req: Request) => {
  return NextResponse.json({
    users: [
      {
        id: "mock-user-id-1",
        name: "Admin User",
        email: "<EMAIL>",
        role: "SUPER_ADMIN",
        active: true,
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        id: "mock-user-id-2",
        name: "Store Manager",
        email: "<EMAIL>",
        role: "STORE_MANAGER",
        active: true,
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        id: "mock-user-id-3",
        name: "Cashier",
        email: "<EMAIL>",
        role: "CASHIER",
        active: true,
        createdAt: new Date(),
        updatedAt: new Date()
      }
    ],
    pagination: {
      total: 3,
      page: 1,
      limit: 10,
      pages: 1
    }
  });
};

const createUser = async (req: Request) => {
  const body = await req.json();
  return NextResponse.json({
    user: {
      id: "mock-new-user-id",
      name: body.name || "New Test User",
      email: body.email || "<EMAIL>",
      role: body.role || "CASHIER",
      active: body.active !== undefined ? body.active : true,
      createdAt: new Date(),
      updatedAt: new Date()
    }
  });
};

const getUser = async (req: Request, { params }: { params: { id: string } }) => {
  return NextResponse.json({
    user: {
      id: params.id || "mock-user-id",
      name: "Test User",
      email: "<EMAIL>",
      role: "STORE_MANAGER",
      active: true,
      createdAt: new Date(),
      updatedAt: new Date()
    }
  });
};

const updateUser = async (req: Request, { params }: { params: { id: string } }) => {
  const body = await req.json();
  return NextResponse.json({
    user: {
      id: params.id || "mock-user-id",
      name: body.name || "Updated Test User",
      email: body.email || "<EMAIL>",
      role: body.role || "STORE_MANAGER",
      active: body.active !== undefined ? body.active : true,
      createdAt: new Date(),
      updatedAt: new Date()
    }
  });
};

const toggleUserStatus = async (req: Request, { params }: { params: { id: string } }) => {
  const body = await req.json();
  return NextResponse.json({
    user: {
      id: params.id || "mock-user-id",
      name: "Test User",
      email: "<EMAIL>",
      role: "STORE_MANAGER",
      active: body.active !== undefined ? body.active : false,
      createdAt: new Date(),
      updatedAt: new Date()
    },
    message: body.active ? "User activated successfully" : "User deactivated successfully"
  });
};

// Mock Authentication API
const login = async (req: Request) => {
  const body = await req.json();

  if (body.email === "<EMAIL>" && body.password === "password") {
    return NextResponse.json({
      success: true,
      user: {
        id: "mock-user-id-1",
        name: "Admin User",
        email: "<EMAIL>",
        role: "SUPER_ADMIN"
      },
      token: "mock-auth-token"
    });
  } else {
    return NextResponse.json(
      { success: false, message: "Invalid email or password" },
      { status: 401 }
    );
  }
};

const logout = async (req: Request) => {
  return NextResponse.json({
    success: true,
    message: "Logged out successfully"
  });
};

const getProfile = async (req: Request) => {
  return NextResponse.json({
    user: {
      id: "mock-user-id-1",
      name: "Admin User",
      email: "<EMAIL>",
      role: "SUPER_ADMIN",
      active: true,
      createdAt: new Date(),
      updatedAt: new Date()
    }
  });
};

const updateProfile = async (req: Request) => {
  const body = await req.json();
  return NextResponse.json({
    user: {
      id: "mock-user-id-1",
      name: body.name || "Updated Admin User",
      email: "<EMAIL>",
      role: "SUPER_ADMIN",
      active: true,
      createdAt: new Date(),
      updatedAt: new Date()
    },
    message: "Profile updated successfully"
  });
};

const changePassword = async (req: Request) => {
  const body = await req.json();

  if (body.currentPassword === "password") {
    return NextResponse.json({
      success: true,
      message: "Password changed successfully"
    });
  } else {
    return NextResponse.json(
      { success: false, message: "Current password is incorrect" },
      { status: 400 }
    );
  }
};



// Mock Activity Logs API
const getActivityLogs = async (req: Request) => {
  return NextResponse.json({
    logs: [
      {
        id: "mock-log-id-1",
        userId: "mock-user-id-1",
        action: "USER_LOGIN",
        details: "User logged in",
        timestamp: new Date(Date.now() - 3600000),
        user: {
          id: "mock-user-id-1",
          name: "Admin User",
          email: "<EMAIL>"
        }
      },
      {
        id: "mock-log-id-2",
        userId: "mock-user-id-1",
        action: "CREATE_PRODUCT",
        details: "Created product: Test Product",
        timestamp: new Date(Date.now() - 7200000),
        user: {
          id: "mock-user-id-1",
          name: "Admin User",
          email: "<EMAIL>"
        }
      },
      {
        id: "mock-log-id-3",
        userId: "mock-user-id-2",
        action: "UPDATE_STOCK",
        details: "Updated stock for product: Test Product",
        timestamp: new Date(Date.now() - 10800000),
        user: {
          id: "mock-user-id-2",
          name: "Store Manager",
          email: "<EMAIL>"
        }
      }
    ],
    pagination: {
      total: 3,
      page: 1,
      limit: 10,
      pages: 1
    }
  });
};

// User API Tests
export async function runUserTests() {
  const tests = [];

  // User Management Tests
  tests.push(await runApiTest(
    "Get Users List",
    getUsers,
    createMockAuthRequest("http://localhost:3000/api/users", "GET", null, "SUPER_ADMIN")
  ));

  tests.push(await runApiTest(
    "Create New User",
    createUser,
    createMockAuthRequest(
      "http://localhost:3000/api/users",
      "POST",
      {
        name: "New User",
        email: "<EMAIL>",
        password: "password123",
        role: "CASHIER"
      },
      "SUPER_ADMIN"
    )
  ));

  tests.push(await runApiTest(
    "Get User by ID",
    (req) => getUser(req, { params: { id: "mock-user-id" } }),
    createMockAuthRequest("http://localhost:3000/api/users/mock-user-id", "GET", null, "SUPER_ADMIN")
  ));

  tests.push(await runApiTest(
    "Update User",
    (req) => updateUser(req, { params: { id: "mock-user-id" } }),
    createMockAuthRequest(
      "http://localhost:3000/api/users/mock-user-id",
      "PUT",
      {
        name: "Updated User",
        role: "STORE_MANAGER"
      },
      "SUPER_ADMIN"
    )
  ));

  tests.push(await runApiTest(
    "Deactivate User",
    (req) => toggleUserStatus(req, { params: { id: "mock-user-id" } }),
    createMockAuthRequest(
      "http://localhost:3000/api/users/mock-user-id/status",
      "PATCH",
      { active: false },
      "SUPER_ADMIN"
    )
  ));

  tests.push(await runApiTest(
    "Activate User",
    (req) => toggleUserStatus(req, { params: { id: "mock-user-id" } }),
    createMockAuthRequest(
      "http://localhost:3000/api/users/mock-user-id/status",
      "PATCH",
      { active: true },
      "SUPER_ADMIN"
    )
  ));

  // Authentication Tests
  tests.push(await runApiTest(
    "Login Success",
    login,
    createMockAuthRequest(
      "http://localhost:3000/api/auth/login",
      "POST",
      {
        email: "<EMAIL>",
        password: "password"
      }
    )
  ));

  tests.push(await runApiTest(
    "Login Failure",
    login,
    createMockAuthRequest(
      "http://localhost:3000/api/auth/login",
      "POST",
      {
        email: "<EMAIL>",
        password: "wrongpassword"
      }
    )
  ));

  tests.push(await runApiTest(
    "Logout",
    logout,
    createMockAuthRequest("http://localhost:3000/api/auth/logout", "POST")
  ));

  tests.push(await runApiTest(
    "Get User Profile",
    getProfile,
    createMockAuthRequest("http://localhost:3000/api/auth/profile", "GET", null, "SUPER_ADMIN")
  ));

  tests.push(await runApiTest(
    "Update User Profile",
    updateProfile,
    createMockAuthRequest(
      "http://localhost:3000/api/auth/profile",
      "PUT",
      {
        name: "Updated Admin User"
      },
      "SUPER_ADMIN"
    )
  ));

  tests.push(await runApiTest(
    "Change Password Success",
    changePassword,
    createMockAuthRequest(
      "http://localhost:3000/api/auth/change-password",
      "POST",
      {
        currentPassword: "password",
        newPassword: "newpassword123"
      },
      "SUPER_ADMIN"
    )
  ));

  tests.push(await runApiTest(
    "Change Password Failure",
    changePassword,
    createMockAuthRequest(
      "http://localhost:3000/api/auth/change-password",
      "POST",
      {
        currentPassword: "wrongpassword",
        newPassword: "newpassword123"
      },
      "SUPER_ADMIN"
    )
  ));

  // Activity Logs Tests
  tests.push(await runApiTest(
    "Get Activity Logs",
    getActivityLogs,
    createMockAuthRequest("http://localhost:3000/api/activity-logs", "GET", null, "SUPER_ADMIN")
  ));

  return tests;
}

export async function runAllUserTests() {
  return await runUserTests();
}
