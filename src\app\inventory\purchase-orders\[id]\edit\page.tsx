"use client";

import { useState, useEffect } from "react";
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
import { MainLayout } from "@/components/layout/MainLayout";
import { PageHeader } from "@/components/layout/PageHeader";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Calendar } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { <PERSON><PERSON>, AlertDescription } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import { ArrowLeft, CalendarIcon, Loader2, Plus, Trash2, Save } from "lucide-react";
import { format } from "date-fns";
import { cn } from "@/lib/utils";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import Link from "next/link";
import { toast } from "sonner";

interface Supplier {
  id: string;
  name: string;
}

interface Product {
  id: string;
  name: string;
  sku: string;
  purchasePrice?: number;
  category?: {
    name: string;
  };
  unit?: {
    name: string;
  };
}

interface POItem {
  id?: string;
  productId: string;
  quantity: number;
  unitPrice: number;
  subtotal: number;
  product?: Product;
}

interface PurchaseOrder {
  id: string;
  orderDate: string;
  status: string;
  subtotal: number;
  tax: number;
  taxPercentage?: number;
  total: number;
  notes?: string;
  supplier: {
    id: string;
    name: string;
  };
  items: Array<{
    id: string;
    quantity: number;
    unitPrice: number;
    subtotal: number;
    product: {
      id: string;
      name: string;
      sku: string;
      category?: {
        name: string;
      };
      unit?: {
        name: string;
      };
    };
  }>;
}

const purchaseOrderSchema = z.object({
  supplierId: z.string().min(1, { message: "Supplier is required" }),
  orderDate: z.date({ required_error: "Order date is required" }),
  taxPercentage: z.coerce
    .number()
    .min(0, { message: "Tax percentage must be non-negative" })
    .max(100, { message: "Tax percentage cannot exceed 100%" })
    .default(0),
  notes: z.string().optional(),
});

type PurchaseOrderFormValues = z.infer<typeof purchaseOrderSchema>;

export default function EditPurchaseOrderPage() {
  const params = useParams();
  const router = useRouter();
  const id = params.id as string;

  const [purchaseOrder, setPurchaseOrder] = useState<PurchaseOrder | null>(null);
  const [suppliers, setSuppliers] = useState<Supplier[]>([]);
  const [products, setProducts] = useState<Product[]>([]);
  const [items, setItems] = useState<POItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [savingDraft, setSavingDraft] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const form = useForm<PurchaseOrderFormValues>({
    resolver: zodResolver(purchaseOrderSchema),
    defaultValues: {
      supplierId: "",
      orderDate: new Date(),
      taxPercentage: 0,
      notes: "",
    },
  });

  // Fetch purchase order data
  const fetchPurchaseOrder = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch(`/api/purchase-orders/${id}`);
      if (!response.ok) {
        if (response.status === 404) {
          throw new Error("Purchase order not found");
        }
        throw new Error("Failed to fetch purchase order");
      }

      const data = await response.json();
      setPurchaseOrder(data);

      // Check if PO can be edited
      if (!["DRAFT", "PENDING_APPROVAL"].includes(data.status)) {
        setError(
          "This purchase order cannot be edited. Only DRAFT and PENDING_APPROVAL orders can be modified."
        );
        return;
      }

      // Populate form with existing data
      form.reset({
        supplierId: data.supplier.id,
        orderDate: new Date(data.orderDate),
        taxPercentage: data.taxPercentage || 0,
        notes: data.notes || "",
      });

      // Populate items
      const poItems: POItem[] = data.items.map((item: any) => ({
        id: item.id,
        productId: item.product.id,
        quantity: Number(item.quantity),
        unitPrice: Number(item.unitPrice),
        subtotal: Number(item.subtotal),
        product: item.product,
      }));
      setItems(poItems);
    } catch (error) {
      console.error("Error fetching purchase order:", error);
      setError(error instanceof Error ? error.message : "Failed to load purchase order");
    } finally {
      setLoading(false);
    }
  };

  // Fetch suppliers and products
  const fetchData = async () => {
    try {
      const [suppliersRes, productsRes] = await Promise.all([
        fetch("/api/suppliers"),
        fetch("/api/products"),
      ]);

      if (!suppliersRes.ok || !productsRes.ok) {
        throw new Error("Failed to fetch data");
      }

      const [suppliersData, productsData] = await Promise.all([
        suppliersRes.json(),
        productsRes.json(),
      ]);

      setSuppliers(suppliersData.suppliers || []);
      setProducts(productsData.products || []);
    } catch (error) {
      console.error("Error fetching data:", error);
      toast.error("Failed to load suppliers and products");
    }
  };

  useEffect(() => {
    if (id) {
      fetchPurchaseOrder();
      fetchData();
    }
  }, [id]);

  const addItem = () => {
    setItems([
      ...items,
      {
        productId: "",
        quantity: 1,
        unitPrice: 0,
        subtotal: 0,
      },
    ]);
  };

  const removeItem = (index: number) => {
    setItems(items.filter((_, i) => i !== index));
  };

  const updateItem = (index: number, field: keyof POItem, value: any) => {
    const updatedItems = [...items];
    updatedItems[index] = { ...updatedItems[index], [field]: value };

    // If product changed, update unit price with purchase price
    if (field === "productId") {
      const product = products.find((p) => p.id === value);
      if (product) {
        updatedItems[index].product = product;
        updatedItems[index].unitPrice = product.purchasePrice || 0;
      }
    }

    // Recalculate subtotal
    if (field === "quantity" || field === "unitPrice" || field === "productId") {
      updatedItems[index].subtotal = updatedItems[index].quantity * updatedItems[index].unitPrice;
    }

    setItems(updatedItems);
  };

  const calculateTotals = () => {
    const subtotal = items.reduce((sum, item) => sum + item.subtotal, 0);
    const taxPercentage = form.watch("taxPercentage") || 0;
    const taxAmount = subtotal * (taxPercentage / 100);
    const total = subtotal + taxAmount;

    return {
      subtotal,
      taxAmount,
      total,
    };
  };

  const saveDraft = async (data: PurchaseOrderFormValues) => {
    try {
      setSavingDraft(true);
      setError(null);

      const payload = {
        supplierId: data.supplierId,
        orderDate: data.orderDate.toISOString().split("T")[0],
        taxPercentage: Number(data.taxPercentage) || 0,
        notes: data.notes || "",
        items: items
          .filter((item) => item.productId)
          .map((item) => ({
            productId: item.productId,
            quantity: Number(item.quantity) || 0,
            unitPrice: Number(item.unitPrice) || 0,
          })),
        status: "DRAFT", // Explicitly set status to DRAFT
      };

      const response = await fetch(`/api/purchase-orders/${id}`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(payload),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to save draft");
      }

      toast.success("Draft saved successfully");
      router.push(`/inventory/purchase-orders/${id}`);
    } catch (error) {
      console.error("Error saving draft:", error);
      toast.error(error instanceof Error ? error.message : "Failed to save draft");
    } finally {
      setSavingDraft(false);
    }
  };

  const onSubmit = async (data: PurchaseOrderFormValues) => {
    if (items.length === 0) {
      toast.error("Please add at least one item");
      return;
    }

    // Validate all items have required fields
    const invalidItems = items.filter(
      (item) =>
        !item.productId ||
        !item.quantity ||
        item.quantity <= 0 ||
        !item.unitPrice ||
        item.unitPrice <= 0
    );

    if (invalidItems.length > 0) {
      toast.error("Please fill in all item details with valid values");
      return;
    }

    try {
      setSaving(true);
      setError(null);

      const payload = {
        supplierId: data.supplierId,
        orderDate: data.orderDate.toISOString().split("T")[0],
        taxPercentage: Number(data.taxPercentage) || 0,
        notes: data.notes || "",
        items: items.map((item) => ({
          productId: item.productId,
          quantity: Number(item.quantity),
          unitPrice: Number(item.unitPrice),
        })),
      };

      const response = await fetch(`/api/purchase-orders/${id}`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(payload),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to update purchase order");
      }

      toast.success("Purchase order updated successfully");
      router.push(`/inventory/purchase-orders/${id}`);
    } catch (error) {
      console.error("Error updating purchase order:", error);
      toast.error(error instanceof Error ? error.message : "Failed to update purchase order");
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <MainLayout>
        <div className="flex justify-center items-center p-12">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
          <span className="ml-2">Loading purchase order...</span>
        </div>
      </MainLayout>
    );
  }

  if (error || !purchaseOrder) {
    return (
      <MainLayout>
        <PageHeader
          title="Edit Purchase Order"
          description="Modify purchase order details"
          actions={
            <Button variant="outline" asChild>
              <Link href="/inventory/purchase-orders">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Purchase Orders
              </Link>
            </Button>
          }
        />
        <Alert className="mt-6">
          <AlertDescription>{error || "Purchase order not found"}</AlertDescription>
        </Alert>
      </MainLayout>
    );
  }

  const { subtotal, taxAmount, total } = calculateTotals();

  return (
    <MainLayout>
      <PageHeader
        title={`Edit Purchase Order #${purchaseOrder.id.slice(-8).toUpperCase()}`}
        description={`Editing purchase order for ${purchaseOrder.supplier.name}`}
        actions={
          <div className="flex gap-2">
            <Button variant="outline" asChild>
              <Link href={`/inventory/purchase-orders/${id}`}>
                <ArrowLeft className="h-4 w-4 mr-2" />
                Cancel
              </Link>
            </Button>
            <Button
              variant="secondary"
              onClick={() => form.handleSubmit(saveDraft)()}
              disabled={savingDraft || saving}
            >
              {savingDraft ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Saving Draft...
                </>
              ) : (
                <>
                  <Save className="h-4 w-4 mr-2" />
                  Save as Draft
                </>
              )}
            </Button>
            <Button type="submit" form="purchase-order-form" disabled={saving || savingDraft}>
              {saving ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Saving...
                </>
              ) : (
                <>
                  <Save className="h-4 w-4 mr-2" />
                  Save Changes
                </>
              )}
            </Button>
          </div>
        }
      />

      <div className="space-y-6">
        {/* Status Badge */}
        <div className="flex items-center gap-2">
          <span className="text-sm font-medium text-muted-foreground">Current Status:</span>
          <Badge variant="outline">{purchaseOrder.status.replace("_", " ")}</Badge>
        </div>

        <Card>
          <CardHeader>
            <CardTitle>Purchase Order Details</CardTitle>
          </CardHeader>
          <CardContent>
            <Form {...form}>
              <form
                onSubmit={form.handleSubmit(onSubmit)}
                className="space-y-6"
                id="purchase-order-form"
              >
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <FormField
                    control={form.control}
                    name="supplierId"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Supplier</FormLabel>
                        <Select onValueChange={field.onChange} value={field.value}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select supplier" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {suppliers.map((supplier) => (
                              <SelectItem key={supplier.id} value={supplier.id}>
                                {supplier.name}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="orderDate"
                    render={({ field }) => (
                      <FormItem className="flex flex-col">
                        <FormLabel>Order Date</FormLabel>
                        <Popover>
                          <PopoverTrigger asChild>
                            <FormControl>
                              <Button
                                variant={"outline"}
                                className={cn(
                                  "w-full pl-3 text-left font-normal",
                                  !field.value && "text-muted-foreground"
                                )}
                              >
                                {field.value ? (
                                  format(field.value, "PPP")
                                ) : (
                                  <span>Pick a date</span>
                                )}
                                <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                              </Button>
                            </FormControl>
                          </PopoverTrigger>
                          <PopoverContent className="w-auto p-0" align="start">
                            <Calendar
                              mode="single"
                              selected={field.value}
                              onSelect={field.onChange}
                              disabled={(date) =>
                                date > new Date() || date < new Date("1900-01-01")
                              }
                              initialFocus
                            />
                          </PopoverContent>
                        </Popover>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="taxPercentage"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Tax Percentage (%)</FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            min="0"
                            max="100"
                            step="0.01"
                            placeholder="0"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="notes"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Notes (Optional)</FormLabel>
                        <FormControl>
                          <Textarea
                            placeholder="Additional notes..."
                            className="resize-none"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </form>
            </Form>
          </CardContent>
        </Card>

        {/* Items */}
        <Card>
          <CardHeader>
            <div className="flex justify-between items-center">
              <CardTitle>Items</CardTitle>
              <Button type="button" onClick={addItem} size="sm">
                <Plus className="h-4 w-4 mr-2" />
                Add Item
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            {items.length === 0 ? (
              <div className="text-center p-8 text-muted-foreground">
                <p>No items added yet. Click "Add Item" to get started.</p>
              </div>
            ) : (
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Product</TableHead>
                    <TableHead>Quantity</TableHead>
                    <TableHead>Unit Price</TableHead>
                    <TableHead>Subtotal</TableHead>
                    <TableHead className="w-[50px]"></TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {items.map((item, index) => (
                    <TableRow key={index}>
                      <TableCell>
                        <Select
                          value={item.productId}
                          onValueChange={(value) => updateItem(index, "productId", value)}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="Select product" />
                          </SelectTrigger>
                          <SelectContent>
                            {products.map((product) => (
                              <SelectItem key={product.id} value={product.id}>
                                {product.name} ({product.sku})
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </TableCell>
                      <TableCell>
                        <Input
                          type="number"
                          min="1"
                          value={item.quantity}
                          onChange={(e) =>
                            updateItem(index, "quantity", parseInt(e.target.value) || 0)
                          }
                          className="w-20"
                        />
                      </TableCell>
                      <TableCell>
                        <Input
                          type="number"
                          min="0"
                          step="0.01"
                          value={item.unitPrice}
                          onChange={(e) =>
                            updateItem(index, "unitPrice", parseFloat(e.target.value) || 0)
                          }
                          className="w-32"
                        />
                      </TableCell>
                      <TableCell>Rp {item.subtotal.toLocaleString("id-ID")}</TableCell>
                      <TableCell>
                        <Button variant="ghost" size="sm" onClick={() => removeItem(index)}>
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            )}
          </CardContent>
        </Card>

        {/* Totals */}
        <Card>
          <CardHeader>
            <CardTitle>Order Summary</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="flex justify-between">
                <span>Subtotal:</span>
                <span>Rp {subtotal.toLocaleString("id-ID")}</span>
              </div>
              <div className="flex justify-between">
                <span>Tax ({form.watch("taxPercentage") || 0}%):</span>
                <span>Rp {taxAmount.toLocaleString("id-ID")}</span>
              </div>
              <div className="flex justify-between font-semibold text-lg border-t pt-2">
                <span>Total:</span>
                <span>Rp {total.toLocaleString("id-ID")}</span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </MainLayout>
  );
}
