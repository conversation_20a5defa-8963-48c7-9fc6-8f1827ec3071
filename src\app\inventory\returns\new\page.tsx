"use client";

import { useState, useEffect } from "react";
import { MainLayout } from "@/components/layout/MainLayout";
import { PageHeader } from "@/components/layout/PageHeader";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Search, Plus, Minus, ArrowLeft, Save } from "lucide-react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { toast } from "sonner";
import { z } from "zod";

interface TransactionItem {
  id: string;
  productId: string;
  quantity: number;
  unitPrice: number;
  subtotal: number;
  product: {
    id: string;
    name: string;
    sku: string;
  } | null;
}

interface Transaction {
  id: string;
  transactionDate: string;
  total: number;
  status: string;
  customer: {
    id: string;
    name: string;
  } | null;
  items: TransactionItem[];
}

interface ReturnItem {
  productId: string;
  quantity: number;
  unitPrice: number;
  subtotal: number;
  maxQuantity: number;
  productName: string;
  productSku: string;
}

const createReturnSchema = z.object({
  transactionId: z.string().min(1, "Transaction is required"),
  customerId: z.string().nullable(), // Allow null for walk-in customers
  reason: z.string().min(1, "Reason is required"),
  notes: z.string().optional(),
  items: z
    .array(
      z.object({
        productId: z.string(),
        quantity: z.number().positive("Quantity must be greater than 0"),
        unitPrice: z.number().positive("Unit price must be greater than 0"),
        subtotal: z.number().positive("Subtotal must be greater than 0"),
      })
    )
    .min(1, "At least one item is required"),
});

export default function NewReturnPage() {
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedTransaction, setSelectedTransaction] = useState<Transaction | null>(null);
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [searchingTransactions, setSearchingTransactions] = useState(false);

  const [formData, setFormData] = useState({
    reason: "",
    notes: "",
  });

  const [returnItems, setReturnItems] = useState<ReturnItem[]>([]);

  const searchTransactions = async (search: string) => {
    if (!search.trim()) {
      setTransactions([]);
      return;
    }

    try {
      setSearchingTransactions(true);
      const response = await fetch(
        `/api/transactions?search=${encodeURIComponent(search)}&status=COMPLETED&limit=10`
      );
      if (!response.ok) {
        throw new Error("Failed to search transactions");
      }
      const data = await response.json();
      setTransactions(data.transactions || []);
    } catch (error) {
      console.error("Error searching transactions:", error);
      toast.error("Failed to search transactions");
    } finally {
      setSearchingTransactions(false);
    }
  };

  useEffect(() => {
    const timeoutId = setTimeout(() => {
      searchTransactions(searchTerm);
    }, 300);

    return () => clearTimeout(timeoutId);
  }, [searchTerm]);

  const handleTransactionSelect = (transaction: Transaction) => {
    setSelectedTransaction(transaction);
    setTransactions([]);
    setSearchTerm("");

    // Initialize return items with zero quantities
    const items: ReturnItem[] = transaction.items.map((item) => ({
      productId: item.productId,
      quantity: 0,
      unitPrice: Number(item.unitPrice), // Ensure it's a number
      subtotal: 0,
      maxQuantity: Number(item.quantity), // Ensure it's a number
      productName: item.product?.name || "Unknown Product",
      productSku: item.product?.sku || "N/A",
    }));
    setReturnItems(items);
  };

  const updateReturnItemQuantity = (productId: string, quantity: number) => {
    setReturnItems((prev) =>
      prev.map((item) => {
        if (item.productId === productId) {
          const newQuantity = Math.max(0, Math.min(Number(quantity), Number(item.maxQuantity)));
          return {
            ...item,
            quantity: newQuantity,
            subtotal: Number((newQuantity * Number(item.unitPrice)).toFixed(2)), // Ensure proper number calculation
          };
        }
        return item;
      })
    );
  };

  const getReturnTotal = () => {
    return returnItems.reduce((sum, item) => sum + item.subtotal, 0);
  };

  const getSelectedItemsCount = () => {
    return returnItems.filter((item) => item.quantity > 0).length;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!selectedTransaction) {
      toast.error("Please select a transaction");
      return;
    }

    const selectedItems = returnItems.filter((item) => item.quantity > 0);
    if (selectedItems.length === 0) {
      toast.error("Please select at least one item to return");
      return;
    }

    // Additional validation for form fields
    if (!formData.reason.trim()) {
      toast.error("Reason for return is required");
      return;
    }

    try {
      setLoading(true);

      const returnData = {
        transactionId: selectedTransaction.id,
        customerId: selectedTransaction.customer?.id || null,
        reason: formData.reason,
        notes: formData.notes,
        items: selectedItems.map((item) => ({
          productId: item.productId,
          quantity: Number(item.quantity), // Ensure it's a number
          unitPrice: Number(item.unitPrice), // Ensure it's a number
          subtotal: Number(item.subtotal), // Ensure it's a number
        })),
      };

      // Debug logging to identify the exact data being validated
      console.log("=== RETURN DATA DEBUG (Updated) ===");
      console.log("Full returnData:", JSON.stringify(returnData, null, 2));
      console.log(
        "Customer ID type:",
        typeof returnData.customerId,
        "Value:",
        returnData.customerId
      );
      console.log("Items data types:");
      returnData.items.forEach((item, index) => {
        console.log(`Item ${index}:`, {
          productId: `${item.productId} (${typeof item.productId})`,
          quantity: `${item.quantity} (${typeof item.quantity})`,
          unitPrice: `${item.unitPrice} (${typeof item.unitPrice})`,
          subtotal: `${item.subtotal} (${typeof item.subtotal})`,
        });
      });
      console.log("=== END DEBUG ===");

      // Validate data
      createReturnSchema.parse(returnData);

      const response = await fetch("/api/returns", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(returnData),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || "Failed to create return");
      }

      const result = await response.json();
      toast.success("Return created successfully");
      router.push(`/inventory/returns/${result.id}`);
    } catch (error) {
      console.error("Error creating return:", error);
      if (error instanceof z.ZodError) {
        console.log("=== ZOD ERROR DETAILS ===");
        console.log("Full error:", error);
        console.log("Error details:", error.errors);
        error.errors.forEach((err, index) => {
          console.log(`Error ${index}:`, {
            path: err.path,
            message: err.message,
            code: err.code,
            received: (err as any).received,
            expected: (err as any).expected,
          });
        });
        console.log("=== END ZOD ERROR ===");

        // Provide specific error messages for validation failures
        const errorMessages = error.errors.map((err) => {
          if (err.path.includes("reason")) {
            return "Reason for return is required";
          }
          if (err.path.includes("items")) {
            if (err.message.includes("At least one item")) {
              return "Please select at least one item to return";
            }
            // More specific error for items array issues
            if (err.path.length > 1) {
              const itemIndex = err.path[1];
              const fieldName = err.path[2];
              return `Item ${Number(itemIndex) + 1}: ${fieldName} - ${err.message}`;
            }
            return "Please check the return quantities";
          }
          if (err.path.includes("transactionId")) {
            return "Please select a transaction";
          }
          return err.message;
        });
        toast.error(errorMessages[0] || "Please fill in all required fields");
      } else {
        toast.error(error instanceof Error ? error.message : "Failed to create return");
      }
    } finally {
      setLoading(false);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("id-ID", {
      style: "currency",
      currency: "IDR",
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("id-ID", {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  };

  return (
    <MainLayout>
      <div className="space-y-6">
        <PageHeader title="Create New Return" description="Process a customer return or exchange">
          <Link href="/inventory/returns">
            <Button variant="outline">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Returns
            </Button>
          </Link>
        </PageHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Transaction Selection */}
          <Card>
            <CardHeader>
              <CardTitle>Select Transaction</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {!selectedTransaction ? (
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="transaction-search">Search Transaction</Label>
                    <div className="relative">
                      <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                      <Input
                        id="transaction-search"
                        placeholder="Search by transaction ID or customer name..."
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        className="pl-10"
                      />
                    </div>
                  </div>

                  {searchingTransactions && (
                    <div className="text-center py-4">
                      <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary mx-auto"></div>
                    </div>
                  )}

                  {transactions.length > 0 && (
                    <div className="space-y-2">
                      <Label>Select a transaction:</Label>
                      {transactions.map((transaction) => (
                        <div
                          key={transaction.id}
                          className="p-4 border rounded-lg cursor-pointer hover:bg-accent"
                          onClick={() => handleTransactionSelect(transaction)}
                        >
                          <div className="flex justify-between items-start">
                            <div>
                              <div className="font-medium">
                                Transaction #{transaction.id.slice(-8)}
                              </div>
                              <div className="text-xs text-muted-foreground font-mono">
                                Full ID: {transaction.id}
                              </div>
                              <div className="text-sm text-muted-foreground">
                                Customer: {transaction.customer?.name || "Walk-in Customer"}
                              </div>
                              <div className="text-sm text-muted-foreground">
                                Date: {formatDate(transaction.transactionDate)}
                              </div>
                            </div>
                            <div className="text-right">
                              <div className="font-medium">{formatCurrency(transaction.total)}</div>
                              <Badge variant="secondary">{transaction.items.length} items</Badge>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              ) : (
                <div className="space-y-4">
                  <div className="flex justify-between items-start p-4 bg-accent rounded-lg">
                    <div>
                      <div className="font-medium">
                        Transaction #{selectedTransaction.id.slice(-8)}
                      </div>
                      <div className="text-xs text-muted-foreground font-mono">
                        Full ID: {selectedTransaction.id}
                      </div>
                      <div className="text-sm text-muted-foreground">
                        Customer: {selectedTransaction.customer?.name || "Walk-in Customer"}
                      </div>
                      <div className="text-sm text-muted-foreground">
                        Date: {formatDate(selectedTransaction.transactionDate)}
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="font-medium">{formatCurrency(selectedTransaction.total)}</div>
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={() => {
                          setSelectedTransaction(null);
                          setReturnItems([]);
                        }}
                      >
                        Change
                      </Button>
                    </div>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Return Details */}
          {selectedTransaction && (
            <>
              <Card>
                <CardHeader>
                  <CardTitle>Return Details</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <Label htmlFor="reason">Reason for Return *</Label>
                    <Input
                      id="reason"
                      value={formData.reason}
                      onChange={(e) => setFormData((prev) => ({ ...prev, reason: e.target.value }))}
                      placeholder="e.g., Defective product, Wrong size, Customer changed mind"
                      required
                    />
                  </div>
                  <div>
                    <Label htmlFor="notes">Additional Notes</Label>
                    <Textarea
                      id="notes"
                      value={formData.notes}
                      onChange={(e) => setFormData((prev) => ({ ...prev, notes: e.target.value }))}
                      placeholder="Any additional information about the return..."
                      rows={3}
                    />
                  </div>
                </CardContent>
              </Card>

              {/* Return Items */}
              <Card>
                <CardHeader>
                  <CardTitle>
                    Return Items ({getSelectedItemsCount()} of {returnItems.length} selected)
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Product</TableHead>
                        <TableHead>SKU</TableHead>
                        <TableHead>Unit Price</TableHead>
                        <TableHead>Max Qty</TableHead>
                        <TableHead>Return Qty</TableHead>
                        <TableHead>Subtotal</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {returnItems.map((item) => (
                        <TableRow key={item.productId}>
                          <TableCell>{item.productName}</TableCell>
                          <TableCell className="font-mono text-sm">{item.productSku}</TableCell>
                          <TableCell>{formatCurrency(item.unitPrice)}</TableCell>
                          <TableCell>{item.maxQuantity}</TableCell>
                          <TableCell>
                            <div className="flex items-center gap-2">
                              <Button
                                type="button"
                                variant="outline"
                                size="sm"
                                onClick={() =>
                                  updateReturnItemQuantity(item.productId, item.quantity - 1)
                                }
                                disabled={item.quantity <= 0}
                              >
                                <Minus className="h-3 w-3" />
                              </Button>
                              <Input
                                type="number"
                                value={item.quantity}
                                onChange={(e) =>
                                  updateReturnItemQuantity(
                                    item.productId,
                                    parseInt(e.target.value) || 0
                                  )
                                }
                                className="w-20 text-center"
                                min="0"
                                max={item.maxQuantity}
                              />
                              <Button
                                type="button"
                                variant="outline"
                                size="sm"
                                onClick={() =>
                                  updateReturnItemQuantity(item.productId, item.quantity + 1)
                                }
                                disabled={item.quantity >= item.maxQuantity}
                              >
                                <Plus className="h-3 w-3" />
                              </Button>
                            </div>
                          </TableCell>
                          <TableCell>{formatCurrency(item.subtotal)}</TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>

                  <div className="mt-4 pt-4 border-t">
                    <div className="flex justify-between items-center">
                      <span className="font-medium">Total Return Amount:</span>
                      <span className="text-lg font-bold">{formatCurrency(getReturnTotal())}</span>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Submit Button */}
              <div className="flex justify-end">
                <Button type="submit" disabled={loading || getSelectedItemsCount() === 0}>
                  {loading ? (
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  ) : (
                    <Save className="h-4 w-4 mr-2" />
                  )}
                  Create Return
                </Button>
              </div>
            </>
          )}
        </form>
      </div>
    </MainLayout>
  );
}
