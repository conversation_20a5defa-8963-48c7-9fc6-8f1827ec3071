import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import { verifyAuthToken } from "@/lib/auth-utils";

// GET /api/suppliers/[id]/products - Get all products for a supplier
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication
    const auth = await verifyAuthToken(request);

    if (!auth.authenticated) {
      return NextResponse.json(
        { error: auth.error },
        { status: auth.status }
      );
    }

    const supplierId = params.id;

    // Verify supplier exists
    const supplier = await prisma.supplier.findUnique({
      where: { id: supplierId },
      select: { 
        id: true, 
        name: true, 
        contactPerson: true,
        phone: true,
        email: true,
        address: true 
      }
    });

    if (!supplier) {
      return NextResponse.json(
        { error: "Supplier not found" },
        { status: 404 }
      );
    }

    // Get query parameters for filtering
    const url = new URL(request.url);
    const activeOnly = url.searchParams.get("active") === "true";
    const preferredOnly = url.searchParams.get("preferred") === "true";
    const search = url.searchParams.get("search") || "";
    const page = parseInt(url.searchParams.get("page") || "1");
    const limit = parseInt(url.searchParams.get("limit") || "20");
    const skip = (page - 1) * limit;

    // Build filter conditions
    const where: any = { supplierId };
    
    if (activeOnly) {
      where.isActive = true;
    }
    
    if (preferredOnly) {
      where.isPreferred = true;
    }

    // Add product search filter
    if (search) {
      where.product = {
        OR: [
          { name: { contains: search, mode: "insensitive" } },
          { sku: { contains: search, mode: "insensitive" } },
          { barcode: { contains: search, mode: "insensitive" } },
        ]
      };
    }

    // Get supplier products with pagination
    const [supplierProducts, total] = await Promise.all([
      prisma.productSupplier.findMany({
        where,
        include: {
          product: {
            include: {
              category: {
                select: {
                  id: true,
                  name: true,
                }
              },
              unit: {
                select: {
                  id: true,
                  name: true,
                  abbreviation: true,
                }
              },
              storeStock: {
                select: {
                  quantity: true,
                  minThreshold: true,
                  maxThreshold: true,
                }
              },
              warehouseStock: {
                select: {
                  quantity: true,
                  minThreshold: true,
                  maxThreshold: true,
                }
              }
            }
          }
        },
        skip,
        take: limit,
        orderBy: [
          { isPreferred: 'desc' }, // Preferred products first
          { product: { name: 'asc' } }, // Then alphabetically
        ],
      }),
      prisma.productSupplier.count({ where }),
    ]);

    // Calculate summary statistics
    const stats = {
      totalProducts: total,
      activeProducts: supplierProducts.filter(sp => sp.isActive).length,
      preferredProducts: supplierProducts.filter(sp => sp.isPreferred).length,
      averagePurchasePrice: supplierProducts.length > 0 
        ? supplierProducts.reduce((sum, sp) => sum + Number(sp.purchasePrice), 0) / supplierProducts.length
        : 0,
      totalInventoryValue: supplierProducts.reduce((sum, sp) => {
        const storeQty = sp.product.storeStock?.quantity || 0;
        const warehouseQty = sp.product.warehouseStock?.quantity || 0;
        const totalQty = Number(storeQty) + Number(warehouseQty);
        return sum + (totalQty * Number(sp.purchasePrice));
      }, 0)
    };

    return NextResponse.json({
      supplier,
      products: supplierProducts,
      stats,
      pagination: {
        total,
        page,
        limit,
        pages: Math.ceil(total / limit),
      },
    });
  } catch (error) {
    console.error("Error fetching supplier products:", error);
    return NextResponse.json(
      { error: "Failed to fetch supplier products", message: (error as Error).message },
      { status: 500 }
    );
  }
}

// POST /api/suppliers/[id]/products - Add multiple products to a supplier (bulk operation)
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication
    const auth = await verifyAuthToken(request);

    if (!auth.authenticated) {
      return NextResponse.json(
        { error: auth.error },
        { status: auth.status }
      );
    }

    // Check permissions
    const hasPermission = ["SUPER_ADMIN", "WAREHOUSE_ADMIN"].includes(auth.user.role);
    if (!hasPermission) {
      return NextResponse.json(
        { error: "Unauthorized - Insufficient permissions" },
        { status: 403 }
      );
    }

    const supplierId = params.id;

    // Verify supplier exists
    const supplier = await prisma.supplier.findUnique({
      where: { id: supplierId },
      select: { id: true, name: true }
    });

    if (!supplier) {
      return NextResponse.json(
        { error: "Supplier not found" },
        { status: 404 }
      );
    }

    // Get request body
    const body = await request.json();
    const { products } = body;

    if (!Array.isArray(products) || products.length === 0) {
      return NextResponse.json(
        { error: "Products array is required and must not be empty" },
        { status: 400 }
      );
    }

    // Validate each product entry
    const validProducts = [];
    const errors = [];

    for (let i = 0; i < products.length; i++) {
      const product = products[i];
      
      // Basic validation
      if (!product.productId || !product.purchasePrice) {
        errors.push(`Product ${i + 1}: productId and purchasePrice are required`);
        continue;
      }

      if (typeof product.purchasePrice !== 'number' || product.purchasePrice <= 0) {
        errors.push(`Product ${i + 1}: purchasePrice must be a positive number`);
        continue;
      }

      // Check if product exists
      const existingProduct = await prisma.product.findUnique({
        where: { id: product.productId },
        select: { id: true, name: true }
      });

      if (!existingProduct) {
        errors.push(`Product ${i + 1}: Product not found`);
        continue;
      }

      // Check if relationship already exists
      const existingRelationship = await prisma.productSupplier.findUnique({
        where: {
          productId_supplierId: {
            productId: product.productId,
            supplierId
          }
        }
      });

      if (existingRelationship) {
        errors.push(`Product ${i + 1}: Relationship already exists for ${existingProduct.name}`);
        continue;
      }

      validProducts.push({
        productId: product.productId,
        supplierId,
        supplierProductCode: product.supplierProductCode || null,
        supplierProductName: product.supplierProductName || null,
        purchasePrice: product.purchasePrice,
        minimumOrderQuantity: product.minimumOrderQuantity || null,
        leadTimeDays: product.leadTimeDays || null,
        isPreferred: product.isPreferred || false,
        isActive: product.isActive !== false, // Default to true
        notes: product.notes || null,
      });
    }

    if (errors.length > 0) {
      return NextResponse.json(
        { error: "Validation failed", issues: errors },
        { status: 400 }
      );
    }

    // Create all product-supplier relationships in a transaction
    const createdRelationships = await prisma.$transaction(async (tx) => {
      const results = [];
      
      for (const productData of validProducts) {
        // If this product is set as preferred, unset other preferred suppliers for this product
        if (productData.isPreferred) {
          await tx.productSupplier.updateMany({
            where: {
              productId: productData.productId,
              isPreferred: true
            },
            data: {
              isPreferred: false
            }
          });
        }

        const created = await tx.productSupplier.create({
          data: productData,
          include: {
            product: {
              select: {
                id: true,
                name: true,
                sku: true,
              }
            }
          }
        });

        results.push(created);
      }

      return results;
    });

    // Log activity
    await prisma.activityLog.create({
      data: {
        userId: auth.user.id,
        action: "BULK_ADD_SUPPLIER_PRODUCTS",
        details: `Added ${createdRelationships.length} products to supplier ${supplier.name}`,
      },
    });

    return NextResponse.json({
      message: `Successfully added ${createdRelationships.length} products to supplier`,
      relationships: createdRelationships,
      count: createdRelationships.length
    }, { status: 201 });
  } catch (error) {
    console.error("Error adding products to supplier:", error);
    return NextResponse.json(
      { error: "Failed to add products to supplier", message: (error as Error).message },
      { status: 500 }
    );
  }
}
