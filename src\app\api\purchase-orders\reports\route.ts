import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { verifyAuthToken } from '@/lib/auth-utils';

export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const auth = await verifyAuthToken(request);
    if (!auth.authenticated) {
      return NextResponse.json({ error: auth.error }, { status: auth.status });
    }

    // Only allow SUPER_ADMIN and WAREHOUSE_ADMIN to view reports
    if (!["SUPER_ADMIN", "WAREHOUSE_ADMIN"].includes(auth.user.role)) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    // Parse query parameters
    const searchParams = request.nextUrl.searchParams;
    const reportType = searchParams.get("type") || "pending";
    const supplierId = searchParams.get("supplierId");
    const startDate = searchParams.get("startDate");
    const endDate = searchParams.get("endDate");
    const minAmount = searchParams.get("minAmount");
    const maxAmount = searchParams.get("maxAmount");

    // Generate the requested report
    switch (reportType) {
      case "pending":
        return await generatePendingPOReport(supplierId, startDate, endDate, minAmount, maxAmount);
      case "received":
        return await generateReceivedPOReport(supplierId, startDate, endDate, minAmount, maxAmount);
      case "overdue":
        return await generateOverduePOReport(supplierId, startDate, endDate, minAmount, maxAmount);
      default:
        return NextResponse.json(
          { error: "Invalid report type. Valid types: pending, received, overdue" },
          { status: 400 }
        );
    }
  } catch (error) {
    console.error("Error generating purchase order report:", error);
    return NextResponse.json(
      { error: "Failed to generate purchase order report", message: (error as Error).message },
      { status: 500 }
    );
  }
}

// Generate pending POs report (awaiting approval or ordering)
async function generatePendingPOReport(
  supplierId: string | null,
  startDate: string | null,
  endDate: string | null,
  minAmount: string | null,
  maxAmount: string | null
) {
  // Build where clause
  const where: any = {
    status: {
      in: ['DRAFT', 'PENDING_APPROVAL', 'APPROVED', 'ORDERED']
    }
  };

  if (supplierId) {
    where.supplierId = supplierId;
  }

  if (startDate || endDate) {
    where.orderDate = {};
    if (startDate) where.orderDate.gte = new Date(startDate);
    if (endDate) where.orderDate.lte = new Date(endDate);
  }

  if (minAmount || maxAmount) {
    where.total = {};
    if (minAmount) where.total.gte = parseFloat(minAmount);
    if (maxAmount) where.total.lte = parseFloat(maxAmount);
  }

  // Fetch pending POs
  const pendingPOs = await prisma.purchaseOrder.findMany({
    where,
    include: {
      supplier: {
        select: {
          id: true,
          name: true,
          contactPerson: true,
          phone: true,
          email: true,
        },
      },
      createdBy: {
        select: {
          id: true,
          name: true,
          email: true,
        },
      },
      approvedBy: {
        select: {
          id: true,
          name: true,
          email: true,
        },
      },
      items: {
        include: {
          product: {
            select: {
              id: true,
              name: true,
              sku: true,
            },
          },
        },
      },
    },
    orderBy: {
      orderDate: 'asc',
    },
  });

  // Calculate summary statistics
  const totalPOs = pendingPOs.length;
  const totalValue = pendingPOs.reduce((sum, po) => sum + Number(po.total), 0);
  const avgValue = totalPOs > 0 ? totalValue / totalPOs : 0;

  // Group by status
  const statusBreakdown = pendingPOs.reduce((acc, po) => {
    acc[po.status] = (acc[po.status] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);

  // Group by supplier
  const supplierBreakdown = pendingPOs.reduce((acc, po) => {
    const supplierName = po.supplier.name;
    if (!acc[supplierName]) {
      acc[supplierName] = { count: 0, totalValue: 0 };
    }
    acc[supplierName].count += 1;
    acc[supplierName].totalValue += Number(po.total);
    return acc;
  }, {} as Record<string, { count: number; totalValue: number }>);

  return NextResponse.json({
    reportType: "pending",
    generatedAt: new Date(),
    summary: {
      totalPOs,
      totalValue,
      avgValue,
      statusBreakdown,
      supplierBreakdown,
    },
    purchaseOrders: pendingPOs.map(po => ({
      id: po.id,
      orderDate: po.orderDate,
      supplier: po.supplier,
      status: po.status,
      total: Number(po.total),
      itemCount: po.items.length,
      createdBy: po.createdBy,
      approvedBy: po.approvedBy,
      approvedAt: po.approvedAt,
      daysPending: Math.floor((new Date().getTime() - new Date(po.orderDate).getTime()) / (1000 * 60 * 60 * 24)),
    })),
  });
}

// Generate received POs report (completed deliveries)
async function generateReceivedPOReport(
  supplierId: string | null,
  startDate: string | null,
  endDate: string | null,
  minAmount: string | null,
  maxAmount: string | null
) {
  // Build where clause
  const where: any = {
    status: {
      in: ['PARTIALLY_RECEIVED', 'RECEIVED']
    }
  };

  if (supplierId) {
    where.supplierId = supplierId;
  }

  if (startDate || endDate) {
    where.receivedAt = {};
    if (startDate) where.receivedAt.gte = new Date(startDate);
    if (endDate) where.receivedAt.lte = new Date(endDate);
  }

  if (minAmount || maxAmount) {
    where.total = {};
    if (minAmount) where.total.gte = parseFloat(minAmount);
    if (maxAmount) where.total.lte = parseFloat(maxAmount);
  }

  // Fetch received POs
  const receivedPOs = await prisma.purchaseOrder.findMany({
    where,
    include: {
      supplier: {
        select: {
          id: true,
          name: true,
          contactPerson: true,
          phone: true,
          email: true,
        },
      },
      createdBy: {
        select: {
          id: true,
          name: true,
          email: true,
        },
      },
      items: {
        include: {
          product: {
            select: {
              id: true,
              name: true,
              sku: true,
            },
          },
        },
      },
      receivings: {
        include: {
          receivedBy: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
        },
      },
    },
    orderBy: {
      receivedAt: 'desc',
    },
  });

  // Calculate summary statistics
  const totalPOs = receivedPOs.length;
  const totalValue = receivedPOs.reduce((sum, po) => sum + Number(po.total), 0);
  const avgValue = totalPOs > 0 ? totalValue / totalPOs : 0;

  // Calculate fulfillment metrics
  const fullyReceived = receivedPOs.filter(po => po.status === 'RECEIVED').length;
  const partiallyReceived = receivedPOs.filter(po => po.status === 'PARTIALLY_RECEIVED').length;

  // Group by supplier
  const supplierBreakdown = receivedPOs.reduce((acc, po) => {
    const supplierName = po.supplier.name;
    if (!acc[supplierName]) {
      acc[supplierName] = { count: 0, totalValue: 0 };
    }
    acc[supplierName].count += 1;
    acc[supplierName].totalValue += Number(po.total);
    return acc;
  }, {} as Record<string, { count: number; totalValue: number }>);

  return NextResponse.json({
    reportType: "received",
    generatedAt: new Date(),
    summary: {
      totalPOs,
      totalValue,
      avgValue,
      fullyReceived,
      partiallyReceived,
      fulfillmentRate: totalPOs > 0 ? (fullyReceived / totalPOs) * 100 : 0,
      supplierBreakdown,
    },
    purchaseOrders: receivedPOs.map(po => ({
      id: po.id,
      orderDate: po.orderDate,
      receivedAt: po.receivedAt,
      supplier: po.supplier,
      status: po.status,
      total: Number(po.total),
      itemCount: po.items.length,
      createdBy: po.createdBy,
      receivingCount: po.receivings.length,
      lastReceiving: po.receivings[po.receivings.length - 1],
      fulfillmentTime: po.receivedAt && po.orderDate 
        ? Math.floor((new Date(po.receivedAt).getTime() - new Date(po.orderDate).getTime()) / (1000 * 60 * 60 * 24))
        : null,
    })),
  });
}

// Generate overdue POs report (past expected delivery dates)
async function generateOverduePOReport(
  supplierId: string | null,
  startDate: string | null,
  endDate: string | null,
  minAmount: string | null,
  maxAmount: string | null
) {
  // Calculate overdue date (POs ordered more than 7 days ago that haven't been received)
  const overdueDate = new Date();
  overdueDate.setDate(overdueDate.getDate() - 7);

  // Build where clause
  const where: any = {
    status: {
      in: ['ORDERED', 'PARTIALLY_RECEIVED']
    },
    orderDate: {
      lt: overdueDate
    }
  };

  if (supplierId) {
    where.supplierId = supplierId;
  }

  if (startDate || endDate) {
    if (startDate) where.orderDate.gte = new Date(startDate);
    if (endDate) where.orderDate.lte = new Date(endDate);
  }

  if (minAmount || maxAmount) {
    where.total = {};
    if (minAmount) where.total.gte = parseFloat(minAmount);
    if (maxAmount) where.total.lte = parseFloat(maxAmount);
  }

  // Fetch overdue POs
  const overduePOs = await prisma.purchaseOrder.findMany({
    where,
    include: {
      supplier: {
        select: {
          id: true,
          name: true,
          contactPerson: true,
          phone: true,
          email: true,
        },
      },
      createdBy: {
        select: {
          id: true,
          name: true,
          email: true,
        },
      },
      items: {
        include: {
          product: {
            select: {
              id: true,
              name: true,
              sku: true,
            },
          },
        },
      },
    },
    orderBy: {
      orderDate: 'asc',
    },
  });

  // Calculate summary statistics
  const totalPOs = overduePOs.length;
  const totalValue = overduePOs.reduce((sum, po) => sum + Number(po.total), 0);
  const avgValue = totalPOs > 0 ? totalValue / totalPOs : 0;

  // Calculate overdue metrics
  const avgDaysOverdue = totalPOs > 0
    ? overduePOs.reduce((sum, po) => {
        const daysOverdue = Math.floor((new Date().getTime() - new Date(po.orderDate).getTime()) / (1000 * 60 * 60 * 24)) - 7;
        return sum + Math.max(0, daysOverdue);
      }, 0) / totalPOs
    : 0;

  // Group by supplier
  const supplierBreakdown = overduePOs.reduce((acc, po) => {
    const supplierName = po.supplier.name;
    if (!acc[supplierName]) {
      acc[supplierName] = { count: 0, totalValue: 0, avgDaysOverdue: 0 };
    }
    acc[supplierName].count += 1;
    acc[supplierName].totalValue += Number(po.total);

    const daysOverdue = Math.floor((new Date().getTime() - new Date(po.orderDate).getTime()) / (1000 * 60 * 60 * 24)) - 7;
    acc[supplierName].avgDaysOverdue = (acc[supplierName].avgDaysOverdue * (acc[supplierName].count - 1) + Math.max(0, daysOverdue)) / acc[supplierName].count;

    return acc;
  }, {} as Record<string, { count: number; totalValue: number; avgDaysOverdue: number }>);

  // Group by severity (days overdue)
  const severityBreakdown = overduePOs.reduce((acc, po) => {
    const daysOverdue = Math.floor((new Date().getTime() - new Date(po.orderDate).getTime()) / (1000 * 60 * 60 * 24)) - 7;
    const severity = daysOverdue <= 3 ? 'mild' : daysOverdue <= 7 ? 'moderate' : 'severe';
    acc[severity] = (acc[severity] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);

  return NextResponse.json({
    reportType: "overdue",
    generatedAt: new Date(),
    summary: {
      totalPOs,
      totalValue,
      avgValue,
      avgDaysOverdue,
      severityBreakdown,
      supplierBreakdown,
    },
    purchaseOrders: overduePOs.map(po => {
      const daysOverdue = Math.floor((new Date().getTime() - new Date(po.orderDate).getTime()) / (1000 * 60 * 60 * 24)) - 7;
      const severity = daysOverdue <= 3 ? 'mild' : daysOverdue <= 7 ? 'moderate' : 'severe';

      return {
        id: po.id,
        orderDate: po.orderDate,
        supplier: po.supplier,
        status: po.status,
        total: Number(po.total),
        itemCount: po.items.length,
        createdBy: po.createdBy,
        daysOverdue: Math.max(0, daysOverdue),
        severity,
      };
    }),
  });
}
