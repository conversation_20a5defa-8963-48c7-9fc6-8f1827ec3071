"use client";

import { MainLayout } from "@/components/layout/MainLayout";
import { PageHeader } from "@/components/layout/PageHeader";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { LatestActivityLogs } from "@/components/dashboard/LatestActivityLogs";
import { LatestProducts } from "@/components/dashboard/LatestProducts";
import { LatestTransactions } from "@/components/dashboard/LatestTransactions";
import { ProductCount } from "@/components/dashboard/ProductCount";
import { TotalRevenueCard, TotalSalesCard, useSalesStats } from "@/components/dashboard/SalesStats";
import { TodaySalesCard, TodayRevenueCard, useTodayStats } from "@/components/dashboard/TodayStats";
import { ActiveUsersCard } from "@/components/dashboard/ActiveUsersCard";
import { Alert, AlertTitle, AlertDescription } from "@/components/ui/alert";
import { useClientAuth } from "@/hooks/use-client-auth";
import { formatCurrency } from "@/lib/utils";
import { TrendingUp, TrendingDown, BarChart, PieChart, Beaker } from "lucide-react";
import { DashboardSalesTrendsChart } from "@/components/dashboard/DashboardSalesTrendsChart";
import { DailySalesTargetChart } from "@/components/dashboard/DailySalesTargetChart";

export default function DashboardPage() {
  const { user } = useClientAuth();
  const isDeveloper = user?.role === "DEVELOPER";
  const { stats, isLoading } = useSalesStats();
  const { stats: todayStats } = useTodayStats();

  return (
    <MainLayout>
      <PageHeader title="Dashboard" />

      {isDeveloper && (
        <Alert className="mb-6 border-blue-500 bg-blue-50 text-blue-800 dark:bg-blue-900 dark:text-blue-100">
          <Beaker className="h-4 w-4" />
          <AlertTitle>Developer Mode</AlertTitle>
          <AlertDescription>
            You are logged in with a Developer account. You only have access to the Development
            section. Navigate to the API Tests page to access development tools.
          </AlertDescription>
        </Alert>
      )}

      {!isDeveloper ? (
        <>
          <div className="grid gap-4 md:grid-cols-3 lg:grid-cols-6">
            {/* Total Revenue Card */}
            <TotalRevenueCard stats={stats} />

            {/* Today's Revenue Card */}
            <TodayRevenueCard stats={todayStats} />

            {/* Total Sales Card */}
            <TotalSalesCard stats={stats} />

            {/* Today's Sales Card */}
            <TodaySalesCard stats={todayStats} />

            {/* Total Products Card */}
            <ProductCount />

            {/* Active Users Card */}
            <ActiveUsersCard />
          </div>

          <div className="mt-6 grid gap-6 md:grid-cols-2">
            {/* Sales Trends Chart */}
            <DashboardSalesTrendsChart className="col-span-1" />
            {/* Daily Sales Target */}
            <DailySalesTargetChart className="col-span-1" />
            </Card>
          </div>

          {/* Latest Activity Logs, Latest Products, and Latest Transactions */}
          <div className="mt-6 grid gap-6 md:grid-cols-3">
            <LatestActivityLogs />
            <LatestProducts />
            <LatestTransactions />
          </div>
        </>
      ) : (
        <Card className="mt-6">
          <CardHeader>
            <CardTitle className="flex items-center">
              <Beaker className="mr-2 h-5 w-5" />
              Developer Tools
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="mb-4">
              As a Developer, you have restricted access to the application. You can only access the
              Development section which contains API testing tools and other developer utilities.
            </p>
            <Button
              onClick={() => (window.location.href = "/tests")}
              className="bg-black text-white hover:bg-gray-800"
            >
              Go to API Tests
            </Button>
          </CardContent>
        </Card>
      )}
    </MainLayout>
  );
}
