import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/auth";
import { verifyAuthToken } from "@/lib/auth-utils";
import { format, subDays, startOfDay, endOfDay } from "date-fns";

export async function GET(request: NextRequest) {
  try {
    console.log("[Daily Transaction Count API] Starting data fetch...");

    // Check authentication
    const auth = await verifyAuthToken(request);
    if (!auth.authenticated) {
      return NextResponse.json(
        { error: auth.error },
        { status: auth.status }
      );
    }

    const { user } = auth;
    const userRole = user.role;

    // Check if user has analytics access
    const hasAccess = ["SUPER_ADMIN", "FINANCE_ADMIN"].includes(userRole);
    if (!hasAccess) {
      console.log("[Daily Transaction Count API] User does not have access:", userRole);
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    const { searchParams } = new URL(request.url);
    const dateRange = searchParams.get("dateRange") || "30d";
    const cashierIds = searchParams.get("cashierIds")?.split(",").filter(Boolean);
    const terminalIds = searchParams.get("terminalIds")?.split(",").filter(Boolean);
    const categoryIds = searchParams.get("categoryIds")?.split(",").filter(Boolean);
    const paymentMethodsFilter = searchParams.get("paymentMethods")?.split(",").filter(Boolean);

    // Custom date range parameters
    const customFromDate = searchParams.get("fromDate");
    const customToDate = searchParams.get("toDate");

    console.log("[DailyTransactionCountAPI] Query params:", {
      dateRange,
      cashierIds,
      terminalIds,
      categoryIds,
      paymentMethodsFilter,
      customFromDate,
      customToDate
    });

    // Calculate date range - prioritize custom dates
    let fromDate: Date;
    let toDate: Date;

    if (customFromDate && customToDate) {
      fromDate = startOfDay(new Date(customFromDate));
      toDate = endOfDay(new Date(customToDate));
      console.log("[DailyTransactionCountAPI] Using custom date range:", { fromDate, toDate });
    } else {
      toDate = endOfDay(new Date());
      switch (dateRange) {
        case "7d":
          fromDate = startOfDay(subDays(new Date(), 7));
          break;
        case "30d":
          fromDate = startOfDay(subDays(new Date(), 30));
          break;
        case "90d":
          fromDate = startOfDay(subDays(new Date(), 90));
          break;
        case "1y":
          fromDate = startOfDay(subDays(new Date(), 365));
          break;
        default:
          fromDate = startOfDay(subDays(new Date(), 30));
      }
      console.log("[DailyTransactionCountAPI] Using preset date range:", { dateRange, fromDate, toDate });
    }

    // Build where clause
    const whereClause: any = {
      createdAt: {
        gte: fromDate,
        lte: toDate,
      },
      status: {
        not: "VOIDED",
      },
      paymentStatus: "PAID", // Only count paid transactions
    };

    // Add cashier filter if specified
    if (cashierIds && cashierIds.length > 0) {
      whereClause.cashierId = {
        in: cashierIds,
      };
    }

    // Add terminal filter if specified
    if (terminalIds && terminalIds.length > 0) {
      whereClause.OR = [
        // Direct terminal association
        {
          terminalId: {
            in: terminalIds,
          },
        },
        // Indirect terminal association through drawer session
        {
          drawerSession: {
            terminalId: {
              in: terminalIds,
            },
          },
        },
      ];
    }

    // Add payment method filter if specified
    if (paymentMethodsFilter && paymentMethodsFilter.length > 0) {
      whereClause.paymentMethod = {
        in: paymentMethodsFilter,
      };
    }

    // Add category filter if specified (requires joining with transaction items)
    if (categoryIds && categoryIds.length > 0) {
      whereClause.items = {
        some: {
          product: {
            categoryId: {
              in: categoryIds,
            },
          },
        },
      };
    }

    console.log("[DailyTransactionCountAPI] Where clause:", JSON.stringify(whereClause, null, 2));

    // Fetch transactions for counting
    const transactions = await prisma.transaction.findMany({
      where: whereClause,
      select: {
        id: true,
        createdAt: true,
      },
      orderBy: {
        createdAt: "asc",
      },
    });

    console.log("[DailyTransactionCountAPI] Found transactions:", transactions.length);

    // Group transactions by date
    const dailyData = new Map<string, number>();

    transactions.forEach((transaction) => {
      const date = format(transaction.createdAt, "yyyy-MM-dd");
      dailyData.set(date, (dailyData.get(date) || 0) + 1);
    });

    // Convert to array and fill missing dates with 0
    const result = [];
    const currentDate = new Date(fromDate);
    
    while (currentDate <= toDate) {
      const dateStr = format(currentDate, "yyyy-MM-dd");
      result.push({
        date: dateStr,
        count: dailyData.get(dateStr) || 0,
      });
      currentDate.setDate(currentDate.getDate() + 1);
    }

    console.log("[DailyTransactionCountAPI] Returning data points:", result.length);

    return NextResponse.json({
      data: result,
      success: true,
      timestamp: new Date().toISOString(),
    });

  } catch (error) {
    console.error("Error fetching daily transaction count:", error);
    return NextResponse.json(
      {
        error: "Failed to fetch daily transaction count data",
        success: false,
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}