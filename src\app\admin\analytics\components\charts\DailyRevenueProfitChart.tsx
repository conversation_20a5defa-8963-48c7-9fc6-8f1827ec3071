"use client";

import { useState, useEffect } from "react";
import { BaseChart } from "@/components/charts/BaseChart";
import { <PERSON><PERSON><PERSON>, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from "recharts";
import { formatChartDate, formatCurrency, CHART_COLORS } from "@/lib/analytics/chartUtils";
import { AnalyticsFilters } from "@/lib/types/analytics";

interface DailyRevenueProfitData {
  date: string;
  revenue: number;
  profit: number;
}

interface DailyRevenueProfitChartProps {
  filters: AnalyticsFilters;
  className?: string;
}

export function DailyRevenueProfitChart({ filters, className }: DailyRevenueProfitChartProps) {
  const [data, setData] = useState<DailyRevenueProfitData[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchData = async () => {
    try {
      setIsLoading(true);
      setError(null);

      const params = new URLSearchParams();
      
      if (filters.dateRange.from) {
        params.append("from", filters.dateRange.from.toISOString());
      }
      if (filters.dateRange.to) {
        params.append("to", filters.dateRange.to.toISOString());
      }
      if (filters.cashierIds?.length) {
        params.append("cashierIds", filters.cashierIds.join(","));
      }
      if (filters.categoryIds?.length) {
        params.append("categoryIds", filters.categoryIds.join(","));
      }
      if (filters.paymentMethods?.length) {
        params.append("paymentMethods", filters.paymentMethods.join(","));
      }
      if (filters.terminalIds?.length) {
        params.append("terminalIds", filters.terminalIds.join(","));
      }

      const response = await fetch(`/api/analytics/daily-revenue-profit?${params}`);
      
      if (!response.ok) {
        throw new Error(`Failed to fetch daily revenue profit data: ${response.statusText}`);
      }

      const result = await response.json();
      
      if (!result.success) {
        throw new Error(result.error || "Failed to fetch daily revenue profit data");
      }

      setData(result.data || []);
    } catch (err) {
      console.error("Error fetching daily revenue profit data:", err);
      setError(err instanceof Error ? err.message : "An error occurred");
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
  }, [filters]);

  const handleRefresh = () => {
    fetchData();
  };

  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload;
      return (
        <div className="bg-white p-3 border border-gray-200 rounded-lg shadow-lg">
          <p className="font-medium text-gray-900 mb-2">{formatChartDate(data.date)}</p>
          <p className="text-sm text-gray-600 mb-1">
            Revenue: {formatCurrency(data.revenue)}
          </p>
          <p className="text-sm text-gray-600">
            Profit: {formatCurrency(data.profit)}
          </p>
        </div>
      );
    }
    return null;
  };

  const totalRevenue = data.reduce((sum, item) => sum + item.revenue, 0);
  const totalProfit = data.reduce((sum, item) => sum + item.profit, 0);

  return (
    <BaseChart
      title="Daily Revenue vs Profit"
      subtitle={`Revenue: ${formatCurrency(totalRevenue)} | Profit: ${formatCurrency(totalProfit)}`}
      isLoading={isLoading}
      error={error}
      onRefresh={handleRefresh}
      className={className}
    >
      <ResponsiveContainer width="100%" height={300}>
        <BarChart data={data} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
          <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
          <XAxis
            dataKey="date"
            tickFormatter={(value) => formatChartDate(value)}
            stroke="#666"
            fontSize={12}
          />
          <YAxis
            stroke="#666"
            fontSize={12}
            tickFormatter={(value) => formatCurrency(value)}
          />
          <Tooltip content={<CustomTooltip />} />
          <Bar 
            dataKey="revenue" 
            fill={CHART_COLORS.primary[0]}
            radius={[4, 4, 0, 0]}
            name="Revenue"
          />
          <Bar 
            dataKey="profit" 
            fill={CHART_COLORS.success[0]}
            radius={[4, 4, 0, 0]}
            name="Profit"
          />
        </BarChart>
      </ResponsiveContainer>
    </BaseChart>
  );
}
