import { NextRequest, NextResponse } from "next/server";
import { z } from "zod";
import { PrismaClient } from "@/generated/prisma";
import { verifyAuthToken } from "@/lib/auth-utils";

const prisma = new PrismaClient();

const resolveDiscrepancySchema = z.object({
  resolutionStatus: z.enum(["INVESTIGATING", "RESOLVED", "WRITTEN_OFF", "ESCALATED"]),
  resolutionNotes: z.string().min(1, "Resolution notes are required"),
  discrepancyCategory: z.enum([
    "COUNTING_ERROR",
    "SYSTEM_ERROR", 
    "THEFT_SUSPECTED",
    "CASH_SHORTAGE",
    "CASH_SURPLUS",
    "REGISTER_ERROR",
    "TRAINING_ERROR",
    "PROCEDURAL_ERROR",
    "UNKNOWN",
    "OTHER"
  ]).optional(),
});

export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Check authentication
    const auth = await verifyAuthToken(request);
    if (!auth.authenticated) {
      return NextResponse.json({ error: auth.error }, { status: auth.status });
    }

    // Only allow SUPER_ADMIN and FINANCE_ADMIN to resolve discrepancies
    if (!["SUPER_ADMIN", "FINANCE_ADMIN"].includes(auth.user.role)) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    const { id } = await params;
    const body = await request.json();
    const validatedData = resolveDiscrepancySchema.parse(body);

    // Get the cash reconciliation record
    const reconciliation = await prisma.cashReconciliation.findUnique({
      where: { id },
      include: {
        user: {
          select: {
            id: true,
            name: true,
          },
        },
        auditAlerts: {
          where: {
            isResolved: false,
          },
        },
      },
    });

    if (!reconciliation) {
      return NextResponse.json(
        { error: "Cash reconciliation record not found" },
        { status: 404 }
      );
    }

    // Update the reconciliation record
    const updatedReconciliation = await prisma.cashReconciliation.update({
      where: { id },
      data: {
        resolutionStatus: validatedData.resolutionStatus,
        resolutionNotes: validatedData.resolutionNotes,
        discrepancyCategory: validatedData.discrepancyCategory || reconciliation.discrepancyCategory,
        investigatedBy: auth.user.id,
        resolvedAt: ["RESOLVED", "WRITTEN_OFF", "CLOSED"].includes(validatedData.resolutionStatus) 
          ? new Date() 
          : null,
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
          },
        },
        investigator: {
          select: {
            id: true,
            name: true,
          },
        },
        auditAlerts: true,
      },
    });

    // Resolve related audit alerts if the discrepancy is resolved
    if (["RESOLVED", "WRITTEN_OFF", "CLOSED"].includes(validatedData.resolutionStatus)) {
      await prisma.cashAuditAlert.updateMany({
        where: {
          cashReconciliationId: id,
          isResolved: false,
        },
        data: {
          isResolved: true,
          resolvedBy: auth.user.id,
          resolvedAt: new Date(),
        },
      });
    }

    // Create new alert if escalated
    if (validatedData.resolutionStatus === "ESCALATED") {
      await prisma.cashAuditAlert.create({
        data: {
          cashReconciliationId: id,
          alertType: "COMPLIANCE_VIOLATION",
          severity: "HIGH",
          message: `Discrepancy escalated for further investigation: ${validatedData.resolutionNotes}`,
        },
      });
    }

    // Log the resolution activity
    await prisma.activityLog.create({
      data: {
        userId: auth.user.id,
        action: "RESOLVE_CASH_DISCREPANCY",
        details: `Updated cash reconciliation status to ${validatedData.resolutionStatus} for ${reconciliation.user.name}. Discrepancy: Rp ${Math.abs(Number(reconciliation.discrepancy)).toLocaleString("id-ID")}`,
      },
    });

    return NextResponse.json({
      reconciliation: {
        id: updatedReconciliation.id,
        businessDate: updatedReconciliation.businessDate,
        cashier: updatedReconciliation.user.name,
        discrepancy: Number(updatedReconciliation.discrepancy),
        discrepancyCategory: updatedReconciliation.discrepancyCategory,
        resolutionStatus: updatedReconciliation.resolutionStatus,
        resolutionNotes: updatedReconciliation.resolutionNotes,
        investigator: updatedReconciliation.investigator?.name,
        resolvedAt: updatedReconciliation.resolvedAt,
        alertCount: updatedReconciliation.auditAlerts.filter(alert => !alert.isResolved).length,
      },
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Validation error", details: error.errors },
        { status: 400 }
      );
    }

    console.error("Error resolving cash discrepancy:", error);
    return NextResponse.json(
      { error: "Failed to resolve discrepancy", message: (error as Error).message },
      { status: 500 }
    );
  }
}

// GET endpoint to fetch reconciliation details
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Check authentication
    const auth = await verifyAuthToken(request);
    if (!auth.authenticated) {
      return NextResponse.json({ error: auth.error }, { status: auth.status });
    }

    // Only allow SUPER_ADMIN and FINANCE_ADMIN to view detailed reconciliation data
    if (!["SUPER_ADMIN", "FINANCE_ADMIN"].includes(auth.user.role)) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    const { id } = await params;

    const reconciliation = await prisma.cashReconciliation.findUnique({
      where: { id },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        investigator: {
          select: {
            id: true,
            name: true,
          },
        },
        auditAlerts: {
          orderBy: {
            createdAt: "desc",
          },
          include: {
            resolver: {
              select: {
                id: true,
                name: true,
              },
            },
          },
        },
      },
    });

    if (!reconciliation) {
      return NextResponse.json(
        { error: "Cash reconciliation record not found" },
        { status: 404 }
      );
    }

    return NextResponse.json({
      reconciliation: {
        id: reconciliation.id,
        businessDate: reconciliation.businessDate,
        cashier: {
          id: reconciliation.user.id,
          name: reconciliation.user.name,
          email: reconciliation.user.email,
        },
        openingBalance: Number(reconciliation.openingBalance),
        expectedAmount: Number(reconciliation.expectedAmount),
        actualAmount: Number(reconciliation.actualAmount),
        discrepancy: Number(reconciliation.discrepancy),
        notes: reconciliation.notes,
        status: reconciliation.status,
        discrepancyCategory: reconciliation.discrepancyCategory,
        resolutionStatus: reconciliation.resolutionStatus,
        resolutionNotes: reconciliation.resolutionNotes,
        investigator: reconciliation.investigator,
        resolvedAt: reconciliation.resolvedAt,
        createdAt: reconciliation.createdAt,
        updatedAt: reconciliation.updatedAt,
        auditAlerts: reconciliation.auditAlerts.map(alert => ({
          id: alert.id,
          alertType: alert.alertType,
          severity: alert.severity,
          message: alert.message,
          threshold: alert.threshold ? Number(alert.threshold) : null,
          actualValue: alert.actualValue ? Number(alert.actualValue) : null,
          isResolved: alert.isResolved,
          resolver: alert.resolver,
          resolvedAt: alert.resolvedAt,
          createdAt: alert.createdAt,
        })),
      },
    });
  } catch (error) {
    console.error("Error fetching cash reconciliation details:", error);
    return NextResponse.json(
      { error: "Failed to fetch reconciliation details", message: (error as Error).message },
      { status: 500 }
    );
  }
}
