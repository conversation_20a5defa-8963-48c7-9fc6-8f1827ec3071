import { NextRequest, NextResponse } from "next/server";
import { PrismaClient } from "@/generated/prisma";
import bcrypt from "bcryptjs";

const prisma = new PrismaClient();

/**
 * API endpoint to create an admin account during initial setup
 * This endpoint bypasses normal authentication requirements
 * It should only be used during the initial system setup
 */
export async function POST(request: NextRequest) {
  try {
    // Get request body
    const body = await request.json();

    // Validate required fields
    if (!body.name || !body.email || !body.password) {
      return NextResponse.json(
        { error: "Missing required fields" },
        { status: 400 }
      );
    }

    // Check if email already exists
    const existingUser = await prisma.user.findUnique({
      where: { email: body.email },
    });

    if (existingUser) {
      return NextResponse.json(
        { error: "Email already in use" },
        { status: 400 }
      );
    }

    // Hash password
    const hashedPassword = await bcrypt.hash(body.password, 10);

    // Create user as SUPER_ADMIN
    const user = await prisma.user.create({
      data: {
        name: body.name,
        email: body.email,
        password: hashedPassword,
        role: "SUPER_ADMIN", // Always create as SUPER_ADMIN
        active: true,
      },
      select: {
        id: true,
        name: true,
        email: true,
        role: true,
        active: true,
        createdAt: true,
      },
    });

    // Create activity log
    await prisma.activityLog.create({
      data: {
        userId: user.id,
        action: "CREATE_USER",
        details: "Created Super Admin account via setup API",
      },
    });

    return NextResponse.json({
      success: true,
      message: "Super Admin account created successfully",
      user
    }, { status: 201 });
  } catch (error) {
    console.error("Error creating admin account:", error);
    return NextResponse.json(
      {
        success: false,
        message: "Failed to create admin account",
        error: error.message
      },
      { status: 500 }
    );
  }
}
