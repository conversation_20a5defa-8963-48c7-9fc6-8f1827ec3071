import { clsx, type ClassValue } from "clsx"
import { twMerge } from "tailwind-merge"
import { format, addDays } from "date-fns"
import { toZonedTime, zonedTimeToUtc } from 'date-fns-tz'

// Default timezone for the application (GMT+7 - Asia/Bangkok)
export const DEFAULT_TIMEZONE = 'Asia/Bangkok'

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

/**
 * Format a number as currency (IDR)
 * Handles both JavaScript numbers and Prisma Decimal types
 */
export function formatCurrency(amount: number | string | any): string {
  // Convert to number if it's a string or Decimal type
  const numericAmount = typeof amount === 'number' ? amount : Number(amount);

  return new Intl.NumberFormat('id-ID', {
    style: 'currency',
    currency: 'IDR',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(numericAmount);
}

/**
 * Format a date to a readable string
 * @param date The date to format
 * @param showTime Whether to include time in the formatted string
 */
export function formatDate(date: Date | string, showTime: boolean = true): string {
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  return format(dateObj, showTime ? "dd MMM yyyy, HH:mm" : "dd MMM yyyy");
}

/**
 * Convert a date to the application's timezone (GMT+7)
 * @param date The date to convert
 * @returns The date in the application's timezone
 */
export function toAppTimezone(date: Date | string): Date {
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  return toZonedTime(dateObj, DEFAULT_TIMEZONE);
}

/**
 * Process a date range for filtering, ensuring proper timezone handling
 * and inclusive end date (23:59:59.999)
 *
 * @param startDate The start date (can be string or Date object)
 * @param endDate The end date (can be string or Date object)
 * @returns Object with processed start and end dates
 */
export function processDateRange(startDate: string | Date | null, endDate: string | Date | null) {
  let start: Date | null = null;
  let end: Date | null = null;

  if (startDate) {
    // First convert the date to the app timezone
    const startInAppTz = toZonedTime(new Date(startDate), DEFAULT_TIMEZONE);
    // Then set to beginning of the day (00:00:00) while preserving timezone
    start = new Date(startInAppTz);
    start.setHours(0, 0, 0, 0);
  }

  if (endDate) {
    // First convert the date to the app timezone
    const endInAppTz = toZonedTime(new Date(endDate), DEFAULT_TIMEZONE);
    // Then set to end of the day (23:59:59.999) while preserving timezone
    end = new Date(endInAppTz);
    end.setHours(23, 59, 59, 999);
  }

  return { start, end };
}
