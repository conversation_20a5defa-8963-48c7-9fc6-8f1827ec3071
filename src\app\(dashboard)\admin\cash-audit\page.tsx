"use client";

import { useState, useEffect } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { Calendar } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { DateRangePicker } from "@/components/ui/date-range-picker";

import {
  CalendarIcon,
  AlertTriangle,
  TrendingUp,
  TrendingDown,
  Users,
  DollarSign,
  Loader2,
  Settings,
  Eye,
  HelpCircle,
  ChevronDown,
  ChevronRight,
} from "lucide-react";
import { format, startOfMonth, endOfMonth, startOfWeek, endOfWeek, subDays } from "date-fns";
import { cn } from "@/lib/utils";
import { DateR<PERSON><PERSON> } from "react-day-picker";
import { PageHeader } from "@/components/layout/PageHeader";
import { MainLayout } from "@/components/layout/MainLayout";
import { toast } from "sonner";
import { ReconciliationResolutionDialog } from "@/components/cash-audit/ReconciliationResolutionDialog";

interface DailyReportData {
  date: string;
  summary: {
    totalReconciliations: number;
    totalDiscrepancy: number;
    totalSurplus: number;
    totalShortage: number;
    reconciliationsWithDiscrepancies: number;
    discrepancyRate: number;
    pendingResolutions: number;
    activeAlerts: number;
  };
  discrepancyByCategory: Record<string, number>;
  cashierPerformance: Array<{
    name: string;
    totalSessions: number;
    totalDiscrepancy: number;
    discrepancyCount: number;
  }>;
  reconciliations: Array<{
    id: string;
    businessDate: string;
    cashier: string;
    discrepancy: number;
    discrepancyCategory: string | null;
    resolutionStatus: string;
    alertCount: number;
    notes: string | null;
    resolutionNotes: string | null;
  }>;
}

export default function CashAuditPage() {
  const today = new Date();
  const [dateRange, setDateRange] = useState<DateRange>({ from: today, to: today });
  const [activeQuickFilter, setActiveQuickFilter] = useState<string>("today");
  const [dailyReport, setDailyReport] = useState<DailyReportData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState("overview");
  const [selectedReconciliation, setSelectedReconciliation] = useState<any>(null);
  const [resolutionDialogOpen, setResolutionDialogOpen] = useState(false);
  const [expandedCashiers, setExpandedCashiers] = useState<Set<string>>(new Set());
  const [cashierDetails, setCashierDetails] = useState<Record<string, any[]>>({});
  const [expandedCategories, setExpandedCategories] = useState<Set<string>>(new Set());
  const [categoryDetails, setCategoryDetails] = useState<Record<string, any[]>>({});

  useEffect(() => {
    fetchDailyReport();
  }, [dateRange]);

  // Quick filter functions
  const setQuickFilter = (filterType: string) => {
    const today = new Date();
    let newRange: DateRange;

    switch (filterType) {
      case "today":
        newRange = { from: today, to: today };
        break;
      case "this-week":
        newRange = {
          from: startOfWeek(today, { weekStartsOn: 1 }),
          to: endOfWeek(today, { weekStartsOn: 1 }),
        };
        break;
      case "last-7-days":
        newRange = { from: subDays(today, 6), to: today };
        break;
      case "this-month":
        newRange = { from: startOfMonth(today), to: endOfMonth(today) };
        break;
      default:
        newRange = { from: today, to: today };
    }

    setDateRange(newRange);
    setActiveQuickFilter(filterType);
    // Clear expanded states when changing date range
    setExpandedCashiers(new Set());
    setExpandedCategories(new Set());
    setCashierDetails({});
    setCategoryDetails({});
  };

  const resetFilter = () => {
    setQuickFilter("today");
  };

  const fetchDailyReport = async () => {
    try {
      setLoading(true);
      setError(null);
      if (!dateRange?.from) {
        throw new Error("Invalid date range");
      }

      const startDate = format(dateRange.from, "yyyy-MM-dd");
      const endDate = format(dateRange.to || dateRange.from, "yyyy-MM-dd");

      const response = await fetch(
        `/api/cash-audit/daily-report?startDate=${startDate}&endDate=${endDate}`
      );

      if (!response.ok) {
        if (response.status === 401) {
          setError(
            "You are not authorized to view cash audit reports. Please log in with appropriate permissions."
          );
          return;
        } else if (response.status === 403) {
          setError(
            "You don't have permission to access cash audit data. Contact your administrator."
          );
          return;
        } else {
          const errorData = await response.json().catch(() => ({}));
          setError(errorData.error || "Failed to fetch daily report");
          return;
        }
      }

      const data = await response.json();
      setDailyReport(data);
    } catch (error: any) {
      console.error("Error fetching daily report:", error);
      setError("Unable to load cash audit data. Please check your connection and try again.");
    } finally {
      setLoading(false);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("id-ID", {
      style: "currency",
      currency: "IDR",
      minimumFractionDigits: 0,
    }).format(amount);
  };

  const getDiscrepancyColor = (discrepancy: number) => {
    if (discrepancy === 0) return "text-green-600";
    if (discrepancy > 0) return "text-blue-600";
    return "text-red-600";
  };

  const getResolutionStatusBadge = (status: string) => {
    const variants: Record<string, "default" | "secondary" | "destructive" | "outline"> = {
      PENDING: "destructive",
      INVESTIGATING: "secondary",
      RESOLVED: "default",
      WRITTEN_OFF: "outline",
      ESCALATED: "destructive",
    };

    return <Badge variant={variants[status] || "outline"}>{status.replace("_", " ")}</Badge>;
  };

  const handleResolveReconciliation = (reconciliation: any) => {
    setSelectedReconciliation({
      id: reconciliation.id,
      businessDate: reconciliation.businessDate,
      cashier: reconciliation.cashier,
      discrepancy: reconciliation.discrepancy,
      discrepancyCategory: reconciliation.discrepancyCategory,
      resolutionStatus: reconciliation.resolutionStatus,
      resolutionNotes: reconciliation.resolutionNotes,
      notes: reconciliation.notes, // Original transaction notes for context
    });
    setResolutionDialogOpen(true);
  };

  const handleResolutionComplete = () => {
    // Refresh the data after resolution
    fetchDailyReport();
    setSelectedReconciliation(null);
  };

  const calculatePerformanceScore = (cashier: any) => {
    const accuracyRate =
      cashier.totalSessions > 0
        ? ((cashier.totalSessions - cashier.discrepancyCount) / cashier.totalSessions) * 100
        : 100;

    // Penalty for large discrepancies
    const avgDiscrepancy =
      cashier.discrepancyCount > 0
        ? Math.abs(cashier.totalDiscrepancy) / cashier.discrepancyCount
        : 0;

    let score = accuracyRate;

    // Apply penalties for large average discrepancies
    if (avgDiscrepancy > 100000) score -= 10; // Large discrepancies
    if (avgDiscrepancy > 50000) score -= 5; // Medium discrepancies

    score = Math.max(0, Math.min(100, score));

    if (score >= 95) return { score, grade: "A" };
    if (score >= 85) return { score, grade: "B" };
    if (score >= 75) return { score, grade: "C" };
    if (score >= 65) return { score, grade: "D" };
    return { score, grade: "F" };
  };

  const toggleCashierExpansion = async (cashierName: string) => {
    const newExpanded = new Set(expandedCashiers);

    if (expandedCashiers.has(cashierName)) {
      newExpanded.delete(cashierName);
    } else {
      newExpanded.add(cashierName);

      // Fetch detailed reconciliations for this cashier if not already loaded
      if (!cashierDetails[cashierName]) {
        try {
          const startDate = format(dateRange.from!, "yyyy-MM-dd");
          const endDate = format(dateRange.to || dateRange.from!, "yyyy-MM-dd");

          const response = await fetch(
            `/api/cash-audit/cashier-details?cashier=${encodeURIComponent(cashierName)}&startDate=${startDate}&endDate=${endDate}`
          );

          if (response.ok) {
            const data = await response.json();
            setCashierDetails((prev) => ({
              ...prev,
              [cashierName]: data.reconciliations || [],
            }));
          }
        } catch (error) {
          console.error("Error fetching cashier details:", error);
        }
      }
    }

    setExpandedCashiers(newExpanded);
  };

  const toggleCategoryExpansion = async (category: string) => {
    const newExpanded = new Set(expandedCategories);

    if (expandedCategories.has(category)) {
      newExpanded.delete(category);
    } else {
      newExpanded.add(category);

      // Fetch detailed reconciliations for this category if not already loaded
      if (!categoryDetails[category]) {
        try {
          const startDate = format(dateRange.from!, "yyyy-MM-dd");
          const endDate = format(dateRange.to || dateRange.from!, "yyyy-MM-dd");

          const response = await fetch(
            `/api/cash-audit/category-details?category=${encodeURIComponent(category)}&startDate=${startDate}&endDate=${endDate}`
          );

          if (response.ok) {
            const data = await response.json();
            setCategoryDetails((prev) => ({
              ...prev,
              [category]: data.reconciliations || [],
            }));
          }
        } catch (error) {
          console.error("Error fetching category details:", error);
        }
      }
    }

    setExpandedCategories(newExpanded);
  };

  const calculateCategoryStats = (reconciliations: any[]) => {
    const totalAmount = reconciliations.reduce((sum, rec) => sum + Math.abs(rec.discrepancy), 0);
    const avgAmount = reconciliations.length > 0 ? totalAmount / reconciliations.length : 0;
    const resolvedCount = reconciliations.filter(
      (rec) => rec.resolutionStatus === "RESOLVED"
    ).length;
    const resolutionRate =
      reconciliations.length > 0 ? (resolvedCount / reconciliations.length) * 100 : 0;

    return { totalAmount, avgAmount, resolutionRate, resolvedCount };
  };

  if (loading) {
    return (
      <MainLayout>
        <PageHeader title="Cash Audit System" />
        <div className="flex items-center justify-center h-64">
          <div className="flex items-center space-x-2">
            <Loader2 className="h-6 w-6 animate-spin" />
            <span>Loading cash audit report...</span>
          </div>
        </div>
      </MainLayout>
    );
  }

  if (error) {
    return (
      <MainLayout>
        <PageHeader title="Cash Audit System" />
        <div className="flex items-center justify-center h-64">
          <Card className="w-full max-w-md">
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-destructive">
                <AlertTriangle className="h-5 w-5" />
                Unable to Load Data
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <p className="text-muted-foreground">{error}</p>
              <Button onClick={fetchDailyReport} className="w-full">
                Try Again
              </Button>
            </CardContent>
          </Card>
        </div>
      </MainLayout>
    );
  }

  return (
    <MainLayout>
      <div className="flex flex-col gap-4">
        <div className="flex items-center justify-between">
          <PageHeader title="Cash Audit System" />

          <div className="flex items-center gap-2">
            <DateRangePicker
              date={dateRange}
              onDateChange={(range) => {
                if (range) {
                  setDateRange(range);
                  setActiveQuickFilter(""); // Clear active quick filter when manually selecting
                  // Clear expanded states when changing date range
                  setExpandedCashiers(new Set());
                  setExpandedCategories(new Set());
                  setCashierDetails({});
                  setCategoryDetails({});
                }
              }}
              placeholder="Select date range"
            />
          </div>
        </div>
        {/* Quick Filter Buttons */}
        <div className="flex items-center gap-2 flex-wrap">
          <span className="text-sm font-medium text-muted-foreground">Quick filters:</span>
          <Button
            variant={activeQuickFilter === "today" ? "default" : "outline"}
            size="sm"
            onClick={() => setQuickFilter("today")}
          >
            Today
          </Button>
          <Button
            variant={activeQuickFilter === "this-week" ? "default" : "outline"}
            size="sm"
            onClick={() => setQuickFilter("this-week")}
          >
            This Week
          </Button>
          <Button
            variant={activeQuickFilter === "last-7-days" ? "default" : "outline"}
            size="sm"
            onClick={() => setQuickFilter("last-7-days")}
          >
            Last 7 Days
          </Button>
          <Button
            variant={activeQuickFilter === "this-month" ? "default" : "outline"}
            size="sm"
            onClick={() => setQuickFilter("this-month")}
          >
            This Month
          </Button>
          <Button variant="ghost" size="sm" onClick={resetFilter} className="text-muted-foreground">
            Reset
          </Button>
        </div>
        {/* Date Range Display */}
        <div className="text-sm text-muted-foreground mb-4">
          Showing data for:{" "}
          <span className="font-medium">
            {dateRange?.from && dateRange?.to && dateRange.from.getTime() !== dateRange.to.getTime()
              ? `${format(dateRange.from, "MMM dd, yyyy")} - ${format(dateRange.to, "MMM dd, yyyy")}`
              : dateRange?.from
                ? format(dateRange.from, "MMM dd, yyyy")
                : "No date selected"}
          </span>
        </div>
      </div>

      {!dailyReport ? (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5 text-muted-foreground" />
              No Data Available
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <p className="text-muted-foreground">
              No cash reconciliation data found for the selected date range.
            </p>
            <div className="space-y-2">
              <p className="text-sm text-muted-foreground">
                Cash audit data is generated when cashiers close their drawers with discrepancies.
                To see data here:
              </p>
              <ul className="text-sm text-muted-foreground space-y-1 ml-4">
                <li>• Cashiers need to close cash drawers with discrepancies</li>
                <li>• Or use the test data generator to create sample data</li>
                <li>• Select a different date that may have reconciliation data</li>
              </ul>
            </div>
            <div className="flex gap-2">
              <Button asChild variant="outline">
                <a href="/admin/test-data">Generate Test Data</a>
              </Button>
              <Button asChild variant="outline">
                <a href="/pos">Go to POS System</a>
              </Button>
            </div>
          </CardContent>
        </Card>
      ) : dailyReport.summary.totalReconciliations === 0 ? (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5 text-muted-foreground" />
              No Reconciliations Found
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <p className="text-muted-foreground">
              No cash reconciliations found for the selected date range.
            </p>
            <p className="text-sm text-muted-foreground">
              This could mean that all cash drawers were closed without discrepancies on this date,
              or no cash drawer sessions were completed.
            </p>
          </CardContent>
        </Card>
      ) : (
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="reconciliations">Reconciliations</TabsTrigger>
            <TabsTrigger value="performance">Performance</TabsTrigger>
            <TabsTrigger value="categories">Categories</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-6">
            {/* Summary Cards */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <div className="flex items-center gap-2">
                    <CardTitle className="text-sm font-medium">Total Sessions</CardTitle>
                    <Popover>
                      <PopoverTrigger asChild>
                        <Button variant="ghost" size="sm" className="h-4 w-4 p-0">
                          <HelpCircle className="h-3 w-3 text-muted-foreground" />
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent className="w-80" align="start">
                        <div className="space-y-2">
                          <h4 className="font-medium">Cash Drawer Sessions</h4>
                          <p className="text-sm text-muted-foreground">
                            Total number of cash drawer closing sessions for this date.
                          </p>
                          <div className="text-xs space-y-1">
                            <p>
                              • <strong>Each session</strong> represents one cashier closing their
                              drawer
                            </p>
                            <p>
                              • <strong>With discrepancies</strong> shows sessions where actual cash
                              ≠ expected cash
                            </p>
                            <p>
                              • <strong>Perfect sessions</strong> have zero discrepancy (actual =
                              expected)
                            </p>
                            <p>
                              • <strong>Multiple sessions</strong> can occur per cashier per day
                            </p>
                          </div>
                        </div>
                      </PopoverContent>
                    </Popover>
                  </div>
                  <Users className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">
                    {dailyReport.summary.totalReconciliations}
                  </div>
                  <p className="text-xs text-muted-foreground">
                    {dailyReport.summary.reconciliationsWithDiscrepancies} with discrepancies
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <div className="flex items-center gap-2">
                    <CardTitle className="text-sm font-medium">Accuracy Rate</CardTitle>
                    <Popover>
                      <PopoverTrigger asChild>
                        <Button variant="ghost" size="sm" className="h-4 w-4 p-0">
                          <HelpCircle className="h-3 w-3 text-muted-foreground" />
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent className="w-80" align="start">
                        <div className="space-y-2">
                          <h4 className="font-medium">Cash Handling Accuracy</h4>
                          <p className="text-sm text-muted-foreground">
                            Accuracy Rate = 100% - Discrepancy Rate
                          </p>
                          <div className="text-xs space-y-1">
                            <p>
                              • <strong>100%</strong> means all cash drawer sessions had perfect
                              counts
                            </p>
                            <p>
                              • <strong>Lower percentage</strong> indicates more sessions with
                              discrepancies
                            </p>
                            <p>
                              • <strong>Discrepancy Rate</strong> = (Sessions with discrepancies ÷
                              Total sessions) × 100
                            </p>
                            <p>
                              • <strong>Target</strong> should be 95%+ for good cash handling
                              practices
                            </p>
                          </div>
                        </div>
                      </PopoverContent>
                    </Popover>
                  </div>
                  <TrendingUp className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">
                    {(100 - dailyReport.summary.discrepancyRate).toFixed(1)}%
                  </div>
                  <p className="text-xs text-muted-foreground">
                    {dailyReport.summary.discrepancyRate.toFixed(1)}% discrepancy rate
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <div className="flex items-center gap-2">
                    <CardTitle className="text-sm font-medium">Net Discrepancy</CardTitle>
                    <Popover>
                      <PopoverTrigger asChild>
                        <Button variant="ghost" size="sm" className="h-4 w-4 p-0">
                          <HelpCircle className="h-3 w-3 text-muted-foreground" />
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent className="w-80" align="start">
                        <div className="space-y-2">
                          <h4 className="font-medium">Net Discrepancy Calculation</h4>
                          <p className="text-sm text-muted-foreground">
                            Net Discrepancy = Total Surplus - Total Shortage
                          </p>
                          <div className="text-xs space-y-1">
                            <p>
                              • <strong>Positive value:</strong> More cash than expected (surplus)
                            </p>
                            <p>
                              • <strong>Negative value:</strong> Less cash than expected (shortage)
                            </p>
                            <p>
                              • <strong>Zero:</strong> Perfect balance across all reconciliations
                            </p>
                          </div>
                        </div>
                      </PopoverContent>
                    </Popover>
                  </div>
                  <DollarSign className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div
                    className={cn(
                      "text-2xl font-bold",
                      getDiscrepancyColor(dailyReport.summary.totalDiscrepancy)
                    )}
                  >
                    {formatCurrency(dailyReport.summary.totalDiscrepancy)}
                  </div>
                  <p className="text-xs text-muted-foreground">
                    +{formatCurrency(dailyReport.summary.totalSurplus)} / -
                    {formatCurrency(dailyReport.summary.totalShortage)}
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <div className="flex items-center gap-2">
                    <CardTitle className="text-sm font-medium">Pending Issues</CardTitle>
                    <Popover>
                      <PopoverTrigger asChild>
                        <Button variant="ghost" size="sm" className="h-4 w-4 p-0">
                          <HelpCircle className="h-3 w-3 text-muted-foreground" />
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent className="w-80" align="start">
                        <div className="space-y-2">
                          <h4 className="font-medium">Pending Issues</h4>
                          <p className="text-sm text-muted-foreground">
                            Cash reconciliations that require investigation or resolution.
                          </p>
                          <div className="text-xs space-y-1">
                            <p>
                              <strong>Follow-up Actions:</strong>
                            </p>
                            <p>• Go to "Reconciliations" tab</p>
                            <p>• Click "Resolve" button on pending items</p>
                            <p>• Investigate the discrepancy cause</p>
                            <p>• Update status and add resolution notes</p>
                            <p>• Escalate if needed for further investigation</p>
                          </div>
                        </div>
                      </PopoverContent>
                    </Popover>
                  </div>
                  <AlertTriangle className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-orange-600">
                    {dailyReport.summary.pendingResolutions}
                  </div>
                  <p className="text-xs text-muted-foreground">
                    {dailyReport.summary.activeAlerts} active alerts
                  </p>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="reconciliations" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Daily Reconciliations</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {dailyReport.reconciliations.length === 0 ? (
                    <p className="text-center text-muted-foreground py-8">
                      No reconciliations found for this date.
                    </p>
                  ) : (
                    dailyReport.reconciliations.map((rec) => (
                      <div
                        key={rec.id}
                        className="flex items-center justify-between p-4 border rounded-lg"
                      >
                        <div className="flex-1 space-y-1">
                          <div className="font-medium">{rec.cashier}</div>
                          <div className="text-sm text-muted-foreground">
                            {format(new Date(rec.businessDate), "PPP")}
                          </div>
                          {rec.discrepancyCategory && (
                            <div className="text-xs text-muted-foreground">
                              Category: {rec.discrepancyCategory.replace(/_/g, " ")}
                            </div>
                          )}
                          {rec.resolutionNotes && (
                            <div className="text-xs text-muted-foreground">
                              <span className="inline-flex items-center bg-blue-50 border border-blue-200 px-2 py-1 rounded">
                                <span className="font-medium text-blue-700">Resolution:</span>
                                <span className="text-blue-600 ml-2">{rec.resolutionNotes}</span>
                              </span>
                            </div>
                          )}
                        </div>

                        <div className="flex items-center gap-4">
                          <div className="text-right space-y-1">
                            <div
                              className={cn("font-medium", getDiscrepancyColor(rec.discrepancy))}
                            >
                              {formatCurrency(rec.discrepancy)}
                            </div>
                            <div className="flex items-center gap-2">
                              {getResolutionStatusBadge(rec.resolutionStatus)}
                              {rec.alertCount > 0 && (
                                <Badge variant="destructive" className="text-xs">
                                  {rec.alertCount} alerts
                                </Badge>
                              )}
                            </div>
                          </div>

                          <div className="flex items-center gap-2">
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleResolveReconciliation(rec)}
                              disabled={rec.resolutionStatus === "RESOLVED"}
                            >
                              <Settings className="h-4 w-4 mr-1" />
                              {rec.resolutionStatus === "PENDING" ? "Resolve" : "Update"}
                            </Button>
                          </div>
                        </div>
                      </div>
                    ))
                  )}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="performance" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Cashier Performance</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {dailyReport.cashierPerformance.map((cashier, index) => {
                    const performance = calculatePerformanceScore(cashier);
                    const isExpanded = expandedCashiers.has(cashier.name);
                    const details = cashierDetails[cashier.name] || [];

                    return (
                      <div key={index} className="border rounded-lg">
                        <div
                          className="flex items-center justify-between p-4 cursor-pointer hover:bg-muted/50 transition-colors"
                          onClick={() => toggleCashierExpansion(cashier.name)}
                        >
                          <div className="flex items-center gap-3">
                            {isExpanded ? (
                              <ChevronDown className="h-4 w-4 text-muted-foreground" />
                            ) : (
                              <ChevronRight className="h-4 w-4 text-muted-foreground" />
                            )}
                            <div className="space-y-1">
                              <div className="font-medium">{cashier.name}</div>
                              <div className="text-sm text-muted-foreground">
                                {cashier.totalSessions} sessions • {cashier.discrepancyCount}{" "}
                                discrepancies
                              </div>
                            </div>
                          </div>

                          <div className="flex items-center gap-4">
                            <div className="text-right space-y-1">
                              <div className="flex items-center gap-2">
                                <Badge
                                  variant={
                                    performance.grade === "A"
                                      ? "default"
                                      : performance.grade === "B"
                                        ? "secondary"
                                        : performance.grade === "C"
                                          ? "outline"
                                          : "destructive"
                                  }
                                >
                                  Grade {performance.grade}
                                </Badge>
                                <span className="text-sm font-medium">
                                  {performance.score.toFixed(1)}%
                                </span>
                              </div>
                              <div
                                className={cn(
                                  "text-sm",
                                  getDiscrepancyColor(cashier.totalDiscrepancy)
                                )}
                              >
                                {formatCurrency(cashier.totalDiscrepancy)}
                              </div>
                            </div>
                          </div>
                        </div>

                        {isExpanded && (
                          <div className="border-t bg-muted/20 p-4">
                            <h4 className="font-medium mb-3">Detailed History</h4>
                            {details.length === 0 ? (
                              <p className="text-sm text-muted-foreground">
                                Loading detailed history...
                              </p>
                            ) : (
                              <div className="space-y-2">
                                {details.map((detail: any, idx: number) => (
                                  <div
                                    key={idx}
                                    className="flex items-center justify-between p-3 bg-background rounded border"
                                  >
                                    <div className="space-y-1">
                                      <div className="text-sm font-medium">
                                        {format(new Date(detail.businessDate), "MMM dd, yyyy")}
                                      </div>
                                      {detail.discrepancyCategory && (
                                        <div className="text-xs text-muted-foreground">
                                          {detail.discrepancyCategory.replace(/_/g, " ")}
                                        </div>
                                      )}
                                    </div>
                                    <div className="flex items-center gap-3">
                                      <div
                                        className={cn(
                                          "text-sm font-medium",
                                          getDiscrepancyColor(detail.discrepancy)
                                        )}
                                      >
                                        {formatCurrency(detail.discrepancy)}
                                      </div>
                                      {getResolutionStatusBadge(detail.resolutionStatus)}
                                    </div>
                                  </div>
                                ))}
                              </div>
                            )}
                          </div>
                        )}
                      </div>
                    );
                  })}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="categories" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Discrepancy Categories</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {Object.entries(dailyReport.discrepancyByCategory).map(([category, count]) => {
                    const isExpanded = expandedCategories.has(category);
                    const details = categoryDetails[category] || [];
                    const stats = details.length > 0 ? calculateCategoryStats(details) : null;

                    return (
                      <div key={category} className="border rounded-lg">
                        <div
                          className="flex items-center justify-between p-4 cursor-pointer hover:bg-muted/50 transition-colors"
                          onClick={() => toggleCategoryExpansion(category)}
                        >
                          <div className="flex items-center gap-3">
                            {isExpanded ? (
                              <ChevronDown className="h-4 w-4 text-muted-foreground" />
                            ) : (
                              <ChevronRight className="h-4 w-4 text-muted-foreground" />
                            )}
                            <div className="space-y-1">
                              <div className="font-medium">
                                {category
                                  .replace(/_/g, " ")
                                  .toLowerCase()
                                  .replace(/\b\w/g, (l) => l.toUpperCase())}
                              </div>
                              {stats && (
                                <div className="text-sm text-muted-foreground">
                                  Avg: {formatCurrency(stats.avgAmount)} • Resolution:{" "}
                                  {stats.resolutionRate.toFixed(1)}%
                                </div>
                              )}
                            </div>
                          </div>

                          <div className="flex items-center gap-3">
                            <Badge variant="outline">{count} occurrences</Badge>
                            {stats && (
                              <div className="text-right">
                                <div className="text-sm font-medium">
                                  {formatCurrency(stats.totalAmount)}
                                </div>
                                <div className="text-xs text-muted-foreground">
                                  {stats.resolvedCount}/{details.length} resolved
                                </div>
                              </div>
                            )}
                          </div>
                        </div>

                        {isExpanded && (
                          <div className="border-t bg-muted/20 p-4">
                            <div className="flex items-center justify-between mb-3">
                              <h4 className="font-medium">Category Breakdown</h4>
                              {stats && (
                                <div className="text-sm text-muted-foreground">
                                  Total Impact: {formatCurrency(stats.totalAmount)}
                                </div>
                              )}
                            </div>

                            {details.length === 0 ? (
                              <p className="text-sm text-muted-foreground">
                                Loading category details...
                              </p>
                            ) : (
                              <div className="space-y-2">
                                {details.map((detail: any, idx: number) => (
                                  <div
                                    key={idx}
                                    className="flex items-center justify-between p-3 bg-background rounded border"
                                  >
                                    <div className="space-y-1">
                                      <div className="text-sm font-medium">{detail.cashier}</div>
                                      <div className="text-xs text-muted-foreground">
                                        {format(new Date(detail.businessDate), "MMM dd, yyyy")}
                                      </div>
                                    </div>
                                    <div className="flex items-center gap-3">
                                      <div
                                        className={cn(
                                          "text-sm font-medium",
                                          getDiscrepancyColor(detail.discrepancy)
                                        )}
                                      >
                                        {formatCurrency(detail.discrepancy)}
                                      </div>
                                      {getResolutionStatusBadge(detail.resolutionStatus)}
                                      <Button
                                        variant="ghost"
                                        size="sm"
                                        onClick={(e) => {
                                          e.stopPropagation();
                                          handleResolveReconciliation(detail);
                                        }}
                                        disabled={detail.resolutionStatus === "RESOLVED"}
                                      >
                                        <Settings className="h-3 w-3" />
                                      </Button>
                                    </div>
                                  </div>
                                ))}
                              </div>
                            )}
                          </div>
                        )}
                      </div>
                    );
                  })}

                  {Object.keys(dailyReport.discrepancyByCategory).length === 0 && (
                    <p className="text-center text-muted-foreground py-8">
                      No categorized discrepancies found for this date.
                    </p>
                  )}
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      )}

      <ReconciliationResolutionDialog
        reconciliation={selectedReconciliation}
        open={resolutionDialogOpen}
        onOpenChange={setResolutionDialogOpen}
        onResolved={handleResolutionComplete}
      />
    </MainLayout>
  );
}
