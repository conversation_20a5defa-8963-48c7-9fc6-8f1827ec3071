"use client";

import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Edit, Eye, Power, Trash } from "lucide-react";
import { formatCurrency } from "@/lib/utils";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import Link from "next/link";

export interface ProductCardProps {
  product: {
    id: string;
    name: string;
    sku: string;
    barcode?: string | null;
    basePrice: number;
    friendPrice?: number | null;
    familyPrice?: number | null;
    imageUrl?: string | null;
    active: boolean;
    category?: {
      id: string;
      name: string;
    } | null;
    unit: {
      id: string;
      name: string;
      abbreviation: string;
    };
    storeStock?: {
      quantity: number;
      minThreshold: number;
    } | null;
  };
  onDelete?: (id: string) => Promise<void> | void;
  onToggleActive?: (id: string, active: boolean) => Promise<void> | void;
}

export function ProductCard({ product, onDelete, onToggleActive }: ProductCardProps) {
  const [isDeleting, setIsDeleting] = useState(false);
  const [isToggling, setIsToggling] = useState(false);

  const handleDelete = async () => {
    if (!onDelete) return;

    setIsDeleting(true);
    try {
      await onDelete(product.id);
    } catch (error) {
      console.error("Error deleting product:", error);
    } finally {
      setIsDeleting(false);
    }
  };

  const handleToggleActive = async () => {
    if (!onToggleActive) return;

    setIsToggling(true);
    try {
      await onToggleActive(product.id, !product.active);
    } catch (error) {
      console.error("Error toggling product active state:", error);
    } finally {
      setIsToggling(false);
    }
  };

  // Get stock status
  const getStockStatus = () => {
    if (!product.storeStock) return { label: "No Stock", variant: "outline" as const };

    const quantity = Number(product.storeStock.quantity);
    const threshold = Number(product.storeStock.minThreshold);

    if (quantity <= 0) return { label: "Out of Stock", variant: "destructive" as const };
    if (quantity < threshold) return { label: "Low Stock", variant: "secondary" as const };
    return { label: "In Stock", variant: "default" as const };
  };

  const stockStatus = getStockStatus();

  return (
    <Card className={`h-full ${!product.active ? "opacity-70" : ""}`}>
      <CardHeader className="pb-3">
        <div className="flex justify-between items-start">
          <CardTitle className="text-lg font-semibold truncate" title={product.name}>
            {product.name}
          </CardTitle>
          <Badge variant={product.active ? "default" : "secondary"}>
            {product.active ? "Active" : "Inactive"}
          </Badge>
        </div>
        <div className="text-sm text-muted-foreground">SKU: {product.sku}</div>
        {product.barcode && (
          <div className="text-sm text-muted-foreground">Barcode: {product.barcode}</div>
        )}
      </CardHeader>
      <CardContent className="pb-2">
        <div className="grid gap-2">
          <div className="flex justify-between">
            <span className="text-sm font-medium">Base Price:</span>
            <span className="text-sm">{formatCurrency(Number(product.basePrice))}</span>
          </div>
          {product.friendPrice && (
            <div className="flex justify-between">
              <span className="text-sm font-medium">Friend Price:</span>
              <span className="text-sm">{formatCurrency(Number(product.friendPrice))}</span>
            </div>
          )}
          {product.familyPrice && (
            <div className="flex justify-between">
              <span className="text-sm font-medium">Family Price:</span>
              <span className="text-sm">{formatCurrency(Number(product.familyPrice))}</span>
            </div>
          )}
          <div className="flex justify-between">
            <span className="text-sm font-medium">Category:</span>
            <span className="text-sm">{product.category?.name || "Uncategorized"}</span>
          </div>
          <div className="flex justify-between">
            <span className="text-sm font-medium">Unit:</span>
            <span className="text-sm">
              {product.unit.name} ({product.unit.abbreviation})
            </span>
          </div>
          <div className="flex justify-between items-center mt-2">
            <span className="text-sm font-medium">Stock:</span>
            <Badge variant={stockStatus.variant}>{stockStatus.label}</Badge>
          </div>
        </div>
      </CardContent>
      <CardFooter className="pt-2">
        <div className="flex justify-between w-full gap-2">
          <Button variant="outline" size="sm" asChild>
            <Link href={`/inventory/products/${product.id}`}>
              <Eye className="h-4 w-4 mr-1" />
              View
            </Link>
          </Button>
          <Button variant="outline" size="sm" asChild>
            <Link href={`/inventory/products/edit/${product.id}`}>
              <Edit className="h-4 w-4 mr-1" />
              Edit
            </Link>
          </Button>
          {onToggleActive && (
            <Button variant="outline" size="sm" onClick={handleToggleActive} disabled={isToggling}>
              <Power className="h-4 w-4 mr-1" />
              {product.active ? "Deactivate" : "Activate"}
            </Button>
          )}
          {onDelete && (
            <AlertDialog>
              <AlertDialogTrigger asChild>
                <Button variant="destructive" size="sm">
                  <Trash className="h-4 w-4 mr-1" />
                  Delete
                </Button>
              </AlertDialogTrigger>
              <AlertDialogContent>
                <AlertDialogHeader>
                  <AlertDialogTitle>Delete Product</AlertDialogTitle>
                  <AlertDialogDescription>
                    Are you sure you want to delete this product? This action cannot be undone.
                  </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter>
                  <AlertDialogCancel>Cancel</AlertDialogCancel>
                  <AlertDialogAction onClick={handleDelete} disabled={isDeleting}>
                    Delete
                  </AlertDialogAction>
                </AlertDialogFooter>
              </AlertDialogContent>
            </AlertDialog>
          )}
        </div>
      </CardFooter>
    </Card>
  );
}
