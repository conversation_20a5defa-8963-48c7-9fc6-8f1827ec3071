import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/auth";
import { z } from "zod";

// Helper function to verify the authentication token
async function verifyAuthToken(request: NextRequest) {
  // Get the session token from cookies
  const token = request.cookies.get("session-token");

  // Check if token exists
  if (!token) {
    console.log("[API] /api/products/temporary-prices - No session token found");
    return {
      authenticated: false,
      error: "Unauthorized - No session token",
      status: 403,
      user: null
    };
  }

  try {
    // Verify the token
    const response = await fetch(`${process.env.NEXTAUTH_URL}/api/auth/session`, {
      headers: {
        cookie: `session-token=${token.value}`,
      },
    });

    if (!response.ok) {
      console.log("[API] /api/products/temporary-prices - Invalid session token");
      return {
        authenticated: false,
        error: "Unauthorized - Invalid session token",
        status: 403,
        user: null
      };
    }

    const session = await response.json();

    if (!session.user) {
      console.log("[API] /api/products/temporary-prices - No user in session");
      return {
        authenticated: false,
        error: "Unauthorized - No user in session",
        status: 403,
        user: null
      };
    }

    return {
      authenticated: true,
      error: null,
      status: 200,
      user: session.user
    };
  } catch (error) {
    console.error("[API] /api/products/temporary-prices - Error verifying token:", error);
    return {
      authenticated: false,
      error: "Unauthorized - Error verifying token",
      status: 403,
      user: null
    };
  }
}

// Schema for temporary price creation
const temporaryPriceSchema = z.object({
  productIds: z.array(z.string()).min(1, { message: "At least one product must be selected" }),
  value: z.number().positive({ message: "Value must be positive" }),
  type: z.enum(["FIXED", "PERCENTAGE"]),
  startDate: z.string().refine((date) => !isNaN(Date.parse(date)), {
    message: "Start date must be a valid date",
  }),
  endDate: z.string().refine((date) => !isNaN(Date.parse(date)), {
    message: "End date must be a valid date",
  }),
}).refine(
  (data) => {
    const startDate = new Date(data.startDate);
    const endDate = new Date(data.endDate);
    return startDate < endDate;
  },
  {
    message: "End date must be after start date",
    path: ["endDate"],
  }
);

// GET /api/products/temporary-prices - Get all products with temporary prices
export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const auth = await verifyAuthToken(request);

    if (!auth.authenticated) {
      return NextResponse.json(
        { error: auth.error },
        { status: auth.status }
      );
    }

    // Get all temporary prices with product details
    const temporaryPrices = await prisma.temporaryPrice.findMany({
      include: {
        product: {
          include: {
            category: true,
            unit: true,
          },
        },
        user: {
          select: {
            id: true,
            name: true,
          },
        },
      },
    });

    // Check for expired temporary prices and remove them
    const now = new Date();
    const expiredPrices = temporaryPrices.filter(price => new Date(price.endDate) < now);
    
    if (expiredPrices.length > 0) {
      // Remove expired temporary prices
      await prisma.temporaryPrice.deleteMany({
        where: {
          id: {
            in: expiredPrices.map(price => price.id),
          },
        },
      });

      // Log activity
      await prisma.activityLog.create({
        data: {
          userId: auth.user.id,
          action: "REMOVE_EXPIRED_TEMPORARY_PRICES",
          details: `Removed ${expiredPrices.length} expired temporary prices`,
        },
      });

      // Filter out expired prices from the response
      const validPrices = temporaryPrices.filter(price => new Date(price.endDate) >= now);
      
      return NextResponse.json({
        temporaryPrices: validPrices,
        expiredRemoved: expiredPrices.length,
      });
    }

    return NextResponse.json({ temporaryPrices });
  } catch (error) {
    console.error("[API] GET /api/products/temporary-prices - Error:", error);
    return NextResponse.json(
      { error: "Failed to fetch temporary prices" },
      { status: 500 }
    );
  }
}

// POST /api/products/temporary-prices - Create temporary prices for multiple products
export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const auth = await verifyAuthToken(request);

    if (!auth.authenticated) {
      return NextResponse.json(
        { error: auth.error },
        { status: auth.status }
      );
    }

    // Check if user has permission to update products
    const hasPermission = ["SUPER_ADMIN", "WAREHOUSE_ADMIN", "DEVELOPER"].includes(auth.user.role);
    if (!hasPermission) {
      return NextResponse.json(
        { error: "Unauthorized - Insufficient permissions" },
        { status: 403 }
      );
    }

    // Get request body
    const body = await request.json();

    // Validate temporary price data
    const validationResult = temporaryPriceSchema.safeParse(body);
    if (!validationResult.success) {
      return NextResponse.json(
        { error: "Validation failed", issues: validationResult.error.issues },
        { status: 400 }
      );
    }

    const { productIds, value, type, startDate, endDate } = validationResult.data;

    // Get products
    const products = await prisma.product.findMany({
      where: { id: { in: productIds } },
    });

    if (products.length !== productIds.length) {
      return NextResponse.json(
        { error: "Some products were not found" },
        { status: 400 }
      );
    }

    // Check if any products already have temporary prices
    const existingTemporaryPrices = await prisma.temporaryPrice.findMany({
      where: { productId: { in: productIds } },
      select: { productId: true },
    });

    if (existingTemporaryPrices.length > 0) {
      // Get product details for the ones with existing temporary prices
      const productsWithExistingPrices = await prisma.product.findMany({
        where: { id: { in: existingTemporaryPrices.map(tp => tp.productId) } },
        select: { id: true, name: true, sku: true },
      });

      return NextResponse.json(
        { 
          error: "Some products already have temporary prices", 
          products: productsWithExistingPrices 
        },
        { status: 400 }
      );
    }

    // Create temporary prices for all products
    const createdTemporaryPrices = [];
    const productDetails = [];

    for (const product of products) {
      const temporaryPrice = await prisma.temporaryPrice.create({
        data: {
          productId: product.id,
          value,
          type,
          startDate: new Date(startDate),
          endDate: new Date(endDate),
          createdBy: auth.user.id,
        },
        include: {
          product: true,
        },
      });

      createdTemporaryPrices.push(temporaryPrice);
      productDetails.push(`${product.name} (${product.sku})`);
    }

    // Log activity
    await prisma.activityLog.create({
      data: {
        userId: auth.user.id,
        action: "CREATE_TEMPORARY_PRICES",
        details: `Created temporary prices for ${products.length} products: ${type} ${value} from ${startDate} to ${endDate}`,
      },
    });

    return NextResponse.json({
      success: true,
      message: `Successfully created temporary prices for ${products.length} products`,
      temporaryPrices: createdTemporaryPrices,
    });
  } catch (error) {
    console.error("[API] POST /api/products/temporary-prices - Error:", error);
    return NextResponse.json(
      { error: "Failed to create temporary prices" },
      { status: 500 }
    );
  }
}
