import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/auth';
import { verifyAuthToken } from '@/lib/auth-utils';
import { transformToTransactionVolume } from '@/lib/analytics/dataTransformers';

export async function GET(request: NextRequest) {
  try {
    // Verify authentication using JWT token from cookies
    const authResult = await verifyAuthToken(request);

    if (!authResult.authenticated || !authResult.user) {
      console.log("[Transaction Volume API] Authentication failed:", authResult.error);
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { user } = authResult;
    const userRole = user.role;

    // Check if user has analytics access
    const hasAccess = ["SUPER_ADMIN", "FINANCE_ADMIN"].includes(userRole);
    if (!hasAccess) {
      console.log("[Transaction Volume API] User does not have access:", userRole);
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    const { searchParams } = new URL(request.url);
    const from = searchParams.get('from');
    const to = searchParams.get('to');
    const cashierIds = searchParams.get('cashierIds')?.split(',').filter(Boolean);
    const terminalIds = searchParams.get('terminalIds')?.split(',').filter(Boolean);
    const categoryIds = searchParams.get('categoryIds')?.split(',').filter(Boolean);
    const paymentMethods = searchParams.get('paymentMethods')?.split(',').filter(Boolean);

    // Build where clause
    const where: any = {
      status: {
        not: "VOIDED",
      },
    };

    if (from || to) {
      where.createdAt = {};
      if (from) where.createdAt.gte = new Date(from);
      if (to) where.createdAt.lte = new Date(to);
    }

    if (cashierIds?.length) {
      where.cashierId = { in: cashierIds };
    }

    if (terminalIds?.length) {
      where.OR = [
        { terminalId: { in: terminalIds } },
        { drawerSession: { terminalId: { in: terminalIds } } }
      ];
    }

    if (paymentMethods?.length) {
      where.paymentMethod = { in: paymentMethods };
    }

    if (categoryIds?.length) {
      where.items = {
        some: {
          product: {
            categoryId: { in: categoryIds }
          }
        }
      };
    }

    console.log('[Analytics] Fetching transaction volume with filters:', where);

    console.log('[Transaction Volume API] Starting data fetch...');

    // For now, return mock data to test the API structure
    const mockTransactionVolume = [
      { range: '< Rp 50K', count: 15, percentage: 30 },
      { range: 'Rp 50K-100K', count: 12, percentage: 24 },
      { range: 'Rp 100K-250K', count: 10, percentage: 20 },
      { range: 'Rp 250K-500K', count: 8, percentage: 16 },
      { range: '> Rp 500K', count: 5, percentage: 10 }
    ];

    console.log('[Transaction Volume API] Returning mock data:', mockTransactionVolume.length, 'ranges');

    return NextResponse.json({
      success: true,
      data: mockTransactionVolume,
      timestamp: new Date().toISOString(),
    });

  } catch (error) {
    console.error('[Analytics] Error fetching transaction volume:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to fetch transaction volume data',
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}
