import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/auth";
import { jwtVerify } from "jose";
import * as XLSX from 'xlsx';
import { parse } from 'json2csv';
import { PDFDocument, StandardFonts, rgb } from 'pdf-lib';

// Helper function to verify the authentication token
async function verifyAuthToken(request: NextRequest) {
  // Get the session token from cookies
  const token = request.cookies.get("session-token");

  // Check if token exists
  if (!token) {
    console.log("[API] /api/products/template - No session token found");
    return {
      authenticated: false,
      error: "Unauthorized - No session token",
      status: 403,
      user: null
    };
  }

  // Verify the token
  try {
    const { payload } = await jwtVerify(
      token.value,
      new TextEncoder().encode(process.env.NEXTAUTH_SECRET || "fallback-secret")
    );

    return {
      authenticated: true,
      user: {
        id: payload.id as string,
        name: payload.name as string,
        email: payload.email as string,
        role: payload.role as string
      }
    };
  } catch (error) {
    console.error("[API] /api/products/template - JWT verification failed:", error);
    return {
      authenticated: false,
      error: "Unauthorized - Invalid token",
      status: 403,
      user: null
    };
  }
}

export async function GET(request: NextRequest) {
  try {
    // Verify authentication
    const auth = await verifyAuthToken(request);
    if (!auth.authenticated) {
      return NextResponse.json({ error: auth.error }, { status: auth.status });
    }

    // Get format from query params (default to csv)
    const searchParams = request.nextUrl.searchParams;
    const format = searchParams.get("format") || "csv";

    // Fetch categories, units, and suppliers for reference
    const [categories, units, suppliers] = await Promise.all([
      prisma.category.findMany({ select: { id: true, name: true } }),
      prisma.unit.findMany({ select: { id: true, name: true, abbreviation: true } }),
      prisma.supplier.findMany({ select: { id: true, name: true } }),
    ]);

    // Create template data with example product
    const templateData = [
      {
        name: "Example Product",
        description: "This is an example product description",
        sku: "EXAMPLE-001",
        barcode: "1234567890123",
        categoryId: categories.length > 0 ? categories[0].id : "",
        categoryName: categories.length > 0 ? categories[0].name : "Example Category",
        unitId: units.length > 0 ? units[0].id : "",
        unitName: units.length > 0 ? units[0].name : "Piece",
        unitAbbreviation: units.length > 0 ? units[0].abbreviation : "pc",
        supplierId: suppliers.length > 0 ? suppliers[0].id : "",
        supplierName: suppliers.length > 0 ? suppliers[0].name : "Example Supplier",
        basePrice: "10.00",
        purchasePrice: "7.50",
        optionalPrice1: "9.00",
        optionalPrice2: "8.50",
        expiryDate: new Date(Date.now() + 90 * 24 * 60 * 60 * 1000).toISOString(),
        imageUrl: "",
        active: "true",
        stockQuantity: "100",
        minThreshold: "10",
      }
    ];

    // Create reference sheets data
    const categoriesData = categories.map(cat => ({ id: cat.id, name: cat.name }));
    const unitsData = units.map(unit => ({
      id: unit.id,
      name: unit.name,
      abbreviation: unit.abbreviation
    }));
    const suppliersData = suppliers.map(sup => ({ id: sup.id, name: sup.name }));

    // Generate file based on requested format
    if (format === "xlsx") {
      // Create Excel workbook with multiple sheets
      const workbook = XLSX.utils.book_new();

      // Add template sheet
      const templateSheet = XLSX.utils.json_to_sheet(templateData);
      XLSX.utils.book_append_sheet(workbook, templateSheet, "Template");

      // Add reference sheets
      if (categories.length > 0) {
        const categoriesSheet = XLSX.utils.json_to_sheet(categoriesData);
        XLSX.utils.book_append_sheet(workbook, categoriesSheet, "Categories");
      }

      if (units.length > 0) {
        const unitsSheet = XLSX.utils.json_to_sheet(unitsData);
        XLSX.utils.book_append_sheet(workbook, unitsSheet, "Units");
      }

      if (suppliers.length > 0) {
        const suppliersSheet = XLSX.utils.json_to_sheet(suppliersData);
        XLSX.utils.book_append_sheet(workbook, suppliersSheet, "Suppliers");
      }

      // Add instructions sheet
      const instructions = [
        { field: "name", required: "Yes", description: "Product name (required)" },
        { field: "description", required: "No", description: "Product description (optional)" },
        { field: "sku", required: "Yes", description: "Stock Keeping Unit - unique identifier (required)" },
        { field: "barcode", required: "No", description: "Product barcode (optional)" },
        { field: "categoryId", required: "No", description: "Category ID - see Categories sheet (optional)" },
        { field: "unitId", required: "Yes", description: "Unit ID - see Units sheet (required)" },
        { field: "supplierId", required: "No", description: "Supplier ID - see Suppliers sheet (optional)" },
        { field: "basePrice", required: "Yes", description: "Base price - numeric value (required)" },
        { field: "purchasePrice", required: "No", description: "Purchase price - numeric value (optional)" },
        { field: "optionalPrice1", required: "No", description: "Optional price 1 - numeric value (optional)" },
        { field: "optionalPrice2", required: "No", description: "Optional price 2 - numeric value (optional)" },
        { field: "expiryDate", required: "No", description: "Expiry date in ISO format (optional)" },
        { field: "imageUrl", required: "No", description: "URL to product image (optional)" },
        { field: "active", required: "No", description: "Product status - true or false (defaults to true)" },
        { field: "stockQuantity", required: "No", description: "Initial stock quantity - numeric value (optional)" },
        { field: "minThreshold", required: "No", description: "Minimum stock threshold - numeric value (optional)" },
      ];

      const instructionsSheet = XLSX.utils.json_to_sheet(instructions);
      XLSX.utils.book_append_sheet(workbook, instructionsSheet, "Instructions");

      // Convert to buffer
      const buffer = XLSX.write(workbook, { type: "buffer", bookType: "xlsx" });

      // Return as downloadable file
      return new NextResponse(buffer, {
        headers: {
          "Content-Type": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
          "Content-Disposition": `attachment; filename="product_import_template.xlsx"`,
        },
      });
    } else if (format === "pdf") {
      // Create PDF template
      const pdfBuffer = await generateTemplatePdf(templateData, instructions, categoriesData, unitsData, suppliersData);

      // Return as downloadable file
      return new NextResponse(pdfBuffer, {
        headers: {
          "Content-Type": "application/pdf",
          "Content-Disposition": `attachment; filename="product_import_template.pdf"`,
        },
      });
    } else {
      // CSV format - simpler, just the template
      const csvData = parse(templateData, {
        fields: Object.keys(templateData[0] || {})
      });

      // Return as downloadable file
      return new NextResponse(csvData, {
        headers: {
          "Content-Type": "text/csv",
          "Content-Disposition": `attachment; filename="product_import_template.csv"`,
        },
      });
    }
  } catch (error) {
    console.error("Error generating template:", error);
    return NextResponse.json(
      { error: "Failed to generate template", message: (error as Error).message },
      { status: 500 }
    );
  }
}

// Helper function to generate PDF template
async function generateTemplatePdf(
  templateData: any[],
  instructions: any[],
  categoriesData: any[],
  unitsData: any[],
  suppliersData: any[]
) {
  // Create a new PDF document
  const pdfDoc = await PDFDocument.create();

  // Embed the standard font
  const font = await pdfDoc.embedFont(StandardFonts.Helvetica);
  const boldFont = await pdfDoc.embedFont(StandardFonts.HelveticaBold);
  const italicFont = await pdfDoc.embedFont(StandardFonts.HelveticaOblique);

  // Set page margins
  const margin = 50;

  // Add title page
  const titlePage = pdfDoc.addPage();
  const { width, height } = titlePage.getSize();

  titlePage.drawText('Product Import Template', {
    x: 50,
    y: height - 100,
    size: 24,
    font: boldFont,
    color: rgb(0, 0, 0),
  });

  titlePage.drawText('This document provides a template and instructions for importing products.', {
    x: 50,
    y: height - 150,
    size: 12,
    font,
    color: rgb(0, 0, 0),
  });

  titlePage.drawText('Contents:', {
    x: 50,
    y: height - 200,
    size: 14,
    font: boldFont,
    color: rgb(0, 0, 0),
  });

  const contentItems = [
    '1. Instructions',
    '2. Template Example',
    '3. Categories Reference',
    '4. Units Reference',
    '5. Suppliers Reference',
  ];

  contentItems.forEach((item, index) => {
    titlePage.drawText(item, {
      x: 70,
      y: height - 230 - (index * 20),
      size: 12,
      font,
      color: rgb(0, 0, 0),
    });
  });

  titlePage.drawText('Generated on: ' + new Date().toLocaleDateString(), {
    x: 50,
    y: 50,
    size: 10,
    font: italicFont,
    color: rgb(0.5, 0.5, 0.5),
  });

  // Add instructions page
  let instructionsPage = pdfDoc.addPage();

  instructionsPage.drawText('Instructions', {
    x: margin,
    y: height - margin,
    size: 18,
    font: boldFont,
    color: rgb(0, 0, 0),
  });

  instructionsPage.drawText('The following fields are available for product import:', {
    x: margin,
    y: height - margin - 40,
    size: 12,
    font,
    color: rgb(0, 0, 0),
  });

  // Draw table header
  const tableX = margin;
  let tableY = height - margin - 70;
  const colWidths = [100, 80, 300];
  const rowHeight = 25;

  // Draw header
  instructionsPage.drawRectangle({
    x: tableX,
    y: tableY - rowHeight,
    width: colWidths.reduce((a, b) => a + b, 0),
    height: rowHeight,
    color: rgb(0.9, 0.9, 0.9),
    borderColor: rgb(0, 0, 0),
    borderWidth: 1,
  });

  instructionsPage.drawText('Field', {
    x: tableX + 5,
    y: tableY - 15,
    size: 10,
    font: boldFont,
    color: rgb(0, 0, 0),
  });

  instructionsPage.drawText('Required', {
    x: tableX + colWidths[0] + 5,
    y: tableY - 15,
    size: 10,
    font: boldFont,
    color: rgb(0, 0, 0),
  });

  instructionsPage.drawText('Description', {
    x: tableX + colWidths[0] + colWidths[1] + 5,
    y: tableY - 15,
    size: 10,
    font: boldFont,
    color: rgb(0, 0, 0),
  });

  tableY -= rowHeight;

  // Draw rows
  instructions.forEach((instruction, i) => {
    const isEven = i % 2 === 0;

    // Draw row background
    instructionsPage.drawRectangle({
      x: tableX,
      y: tableY - rowHeight,
      width: colWidths.reduce((a, b) => a + b, 0),
      height: rowHeight,
      color: isEven ? rgb(1, 1, 1) : rgb(0.95, 0.95, 0.95),
      borderColor: rgb(0, 0, 0),
      borderWidth: 1,
    });

    // Draw field name
    instructionsPage.drawText(instruction.field, {
      x: tableX + 5,
      y: tableY - 15,
      size: 9,
      font,
      color: rgb(0, 0, 0),
    });

    // Draw required
    instructionsPage.drawText(instruction.required, {
      x: tableX + colWidths[0] + 5,
      y: tableY - 15,
      size: 9,
      font,
      color: instruction.required === 'Yes' ? rgb(0.8, 0, 0) : rgb(0, 0, 0),
    });

    // Draw description
    instructionsPage.drawText(instruction.description, {
      x: tableX + colWidths[0] + colWidths[1] + 5,
      y: tableY - 15,
      size: 9,
      font,
      color: rgb(0, 0, 0),
    });

    tableY -= rowHeight;

    // Add a new page if needed
    if (tableY < margin + rowHeight && i < instructions.length - 1) {
      instructionsPage = pdfDoc.addPage();
      tableY = height - margin - 30;

      instructionsPage.drawText('Instructions (continued)', {
        x: margin,
        y: height - margin,
        size: 18,
        font: boldFont,
        color: rgb(0, 0, 0),
      });
    }
  });

  // Add template example page
  const templatePage = pdfDoc.addPage();

  templatePage.drawText('Template Example', {
    x: margin,
    y: height - margin,
    size: 18,
    font: boldFont,
    color: rgb(0, 0, 0),
  });

  templatePage.drawText('Below is an example of a product entry:', {
    x: margin,
    y: height - margin - 40,
    size: 12,
    font,
    color: rgb(0, 0, 0),
  });

  // Draw example data
  let exampleY = height - margin - 70;
  const exampleData = templateData[0];

  Object.entries(exampleData).forEach(([key, value], index) => {
    const displayValue = value !== null && value !== undefined ? value.toString() : '';

    templatePage.drawText(`${key}: `, {
      x: margin,
      y: exampleY - (index * 20),
      size: 10,
      font: boldFont,
      color: rgb(0, 0, 0),
    });

    templatePage.drawText(displayValue, {
      x: margin + 120,
      y: exampleY - (index * 20),
      size: 10,
      font,
      color: rgb(0, 0, 0),
    });
  });

  // Add reference pages if there's data
  if (categoriesData.length > 0) {
    addReferencePage(pdfDoc, 'Categories Reference', categoriesData, ['id', 'name']);
  }

  if (unitsData.length > 0) {
    addReferencePage(pdfDoc, 'Units Reference', unitsData, ['id', 'name', 'abbreviation']);
  }

  if (suppliersData.length > 0) {
    addReferencePage(pdfDoc, 'Suppliers Reference', suppliersData, ['id', 'name']);
  }

  // Add page numbers
  const pageCount = pdfDoc.getPageCount();
  for (let i = 0; i < pageCount; i++) {
    const page = pdfDoc.getPage(i);
    page.drawText(`Page ${i + 1} of ${pageCount}`, {
      x: page.getWidth() - margin - 100,
      y: margin / 2,
      size: 8,
      font,
      color: rgb(0.5, 0.5, 0.5),
    });
  }

  // Serialize the PDFDocument to bytes
  const pdfBytes = await pdfDoc.save();

  return pdfBytes;
}

// Helper function to add a reference page
async function addReferencePage(pdfDoc: PDFDocument, title: string, data: any[], columns: string[]) {
  // Create a new page
  let currentPage = pdfDoc.addPage();
  const { width, height } = currentPage.getSize();
  const margin = 50;
  const font = await pdfDoc.embedFont(StandardFonts.Helvetica);
  const boldFont = await pdfDoc.embedFont(StandardFonts.HelveticaBold);

  // Add title
  currentPage.drawText(title, {
    x: margin,
    y: height - margin,
    size: 18,
    font: boldFont,
    color: rgb(0, 0, 0),
  });

  // Calculate column widths
  const tableWidth = width - (margin * 2);
  const colWidths = columns.map(() => tableWidth / columns.length);

  // Draw table header
  let tableY = height - margin - 40;
  const rowHeight = 25;

  // Function to draw header on a page
  const drawHeader = (page: any) => {
    // Draw header background
    page.drawRectangle({
      x: margin,
      y: tableY - rowHeight,
      width: tableWidth,
      height: rowHeight,
      color: rgb(0.9, 0.9, 0.9),
      borderColor: rgb(0, 0, 0),
      borderWidth: 1,
    });

    // Draw header text
    let headerX = margin;
    columns.forEach((col, i) => {
      page.drawText(col.charAt(0).toUpperCase() + col.slice(1), {
        x: headerX + 5,
        y: tableY - 15,
        size: 10,
        font: boldFont,
        color: rgb(0, 0, 0),
      });
      headerX += colWidths[i];
    });
  };

  // Draw initial header
  drawHeader(currentPage);
  tableY -= rowHeight;

  // Process each data row
  for (let rowIndex = 0; rowIndex < data.length; rowIndex++) {
    const item = data[rowIndex];
    const isEven = rowIndex % 2 === 0;

    // Check if we need a new page
    if (tableY < margin + rowHeight && rowIndex < data.length - 1) {
      // Create a new page
      currentPage = pdfDoc.addPage();
      tableY = height - margin - 40;

      // Add title to new page
      currentPage.drawText(`${title} (continued)`, {
        x: margin,
        y: height - margin,
        size: 18,
        font: boldFont,
        color: rgb(0, 0, 0),
      });

      // Draw header on new page
      drawHeader(currentPage);
      tableY -= rowHeight;
    }

    // Draw row background
    currentPage.drawRectangle({
      x: margin,
      y: tableY - rowHeight,
      width: tableWidth,
      height: rowHeight,
      color: isEven ? rgb(1, 1, 1) : rgb(0.95, 0.95, 0.95),
      borderColor: rgb(0, 0, 0),
      borderWidth: 1,
    });

    // Draw row data
    let colX = margin;
    columns.forEach((col, colIndex) => {
      const value = item[col]?.toString() || '';

      currentPage.drawText(value, {
        x: colX + 5,
        y: tableY - 15,
        size: 9,
        font,
        color: rgb(0, 0, 0),
      });

      colX += colWidths[colIndex];
    });

    // Move to next row
    tableY -= rowHeight;
  }
}
