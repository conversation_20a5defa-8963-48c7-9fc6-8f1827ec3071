"use client";

import { useState, useEffect, memo } from "react";
import { useRouter } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import { LogOut, Clock } from "lucide-react";
import { useClientAuth } from "@/hooks/use-client-auth";
import { useStoreInfo } from "@/contexts/store-info-context";

interface POSHeaderProps {
  user: {
    id: string;
    name: string;
    email: string;
    role: string;
  };
}

export const POSHeader = memo(function POSHeader({ user }: POSHeaderProps) {
  const router = useRouter();
  const { logout } = useClientAuth();
  const { storeInfo, isLoading: storeLoading } = useStoreInfo();
  const [currentTime, setCurrentTime] = useState(new Date());
  const [drawerStatus, setDrawerStatus] = useState<"OPEN" | "NO DRAWER">("NO DRAWER");

  // Update clock every second
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  // Check drawer status
  useEffect(() => {
    const checkDrawerStatus = async () => {
      try {
        const response = await fetch("/api/drawer-sessions/current");
        if (response.ok) {
          const data = await response.json();
          if (data.session) {
            setDrawerStatus("OPEN");
          } else {
            setDrawerStatus("NO DRAWER");
          }
        } else {
          setDrawerStatus("NO DRAWER");
        }
      } catch (error) {
        console.error("POSHeader: Error checking drawer status:", error);
        setDrawerStatus("NO DRAWER");
      }
    };

    checkDrawerStatus();

    // Check drawer status every 30 seconds as fallback
    const drawerTimer = setInterval(checkDrawerStatus, 30000);
    return () => clearInterval(drawerTimer);
  }, []);

  // Listen for drawer session events for immediate synchronization
  useEffect(() => {
    const handleDrawerSessionCreated = (event: CustomEvent) => {
      console.log("POSHeader: Drawer session created event received", event.detail);
      setDrawerStatus("OPEN");
    };

    const handleDrawerSessionClosed = (event: CustomEvent) => {
      console.log("POSHeader: Drawer session closed event received", event.detail);
      setDrawerStatus("NO DRAWER");
    };

    console.log("POSHeader: Adding drawer session event listeners");
    window.addEventListener("drawerSessionCreated", handleDrawerSessionCreated as EventListener);
    window.addEventListener("drawerSessionClosed", handleDrawerSessionClosed as EventListener);

    return () => {
      console.log("POSHeader: Removing drawer session event listeners");
      window.removeEventListener(
        "drawerSessionCreated",
        handleDrawerSessionCreated as EventListener
      );
      window.removeEventListener("drawerSessionClosed", handleDrawerSessionClosed as EventListener);
    };
  }, []);

  // Format time as DD/MM/YYYY HH:mm:ss (GMT+7)
  const formatTime = (date: Date) => {
    // Add 7 hours for GMT+7 (Jakarta time)
    const jakartaTime = new Date(date.getTime() + 7 * 60 * 60 * 1000);

    const day = jakartaTime.getUTCDate().toString().padStart(2, "0");
    const month = (jakartaTime.getUTCMonth() + 1).toString().padStart(2, "0");
    const year = jakartaTime.getUTCFullYear();
    const hours = jakartaTime.getUTCHours().toString().padStart(2, "0");
    const minutes = jakartaTime.getUTCMinutes().toString().padStart(2, "0");
    const seconds = jakartaTime.getUTCSeconds().toString().padStart(2, "0");

    return `${day}/${month}/${year} ${hours}:${minutes}:${seconds}`;
  };

  const handleLogout = async () => {
    try {
      await logout();
      router.push("/login");
    } catch (error) {
      console.error("Logout error:", error);
    }
  };

  return (
    <header className="bg-white border-b border-gray-200 px-6 py-4">
      <div className="flex items-center justify-between">
        {/* Left side - Title and Cashier Info */}
        <div className="flex items-center space-x-6">
          <h1 className="text-2xl font-bold text-gray-900">
            {storeLoading ? (
              <div className="h-8 w-48 bg-gray-200 animate-pulse rounded"></div>
            ) : (
              storeInfo?.storeName || "Point of Sale"
            )}
          </h1>
          <div className="text-sm text-gray-600">
            Logged in as: <span className="font-large text-gray-900">{user.name}</span>
          </div>
        </div>

        {/* Right side - Clock, Drawer Status, Logout */}
        <div className="flex items-center space-x-6">
          {/* Live Clock */}
          <div className="flex items-center space-x-2 text-sm text-gray-600">
            <Clock className="h-4 w-4" />
            <span className="font-mono">{formatTime(currentTime)}</span>
          </div>

          {/* Drawer Status */}
          <div className="text-sm">
            <span className="text-gray-600">Drawer: </span>
            <span
              className={`font-medium ${
                drawerStatus === "OPEN" ? "text-green-600" : "text-red-600"
              }`}
            >
              {drawerStatus}
            </span>
          </div>

          {/* Logout Button */}
          <Button
            variant="outline"
            size="sm"
            onClick={handleLogout}
            className="flex items-center space-x-2"
          >
            <LogOut className="h-4 w-4" />
            <span>Logout</span>
          </Button>
        </div>
      </div>
    </header>
  );
});
