import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import { verifyAuthToken } from "@/lib/auth-utils";

// GET /api/analytics/suppliers - Get supplier analytics and performance metrics
export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const auth = await verifyAuthToken(request);

    if (!auth.authenticated) {
      return NextResponse.json(
        { error: auth.error },
        { status: auth.status }
      );
    }

    // Check permissions
    const hasPermission = ["SUPER_ADMIN", "WAREHOUSE_ADMIN", "FINANCE_ADMIN"].includes(auth.user.role);
    if (!hasPermission) {
      return NextResponse.json(
        { error: "Unauthorized - Insufficient permissions" },
        { status: 403 }
      );
    }

    // Get query parameters
    const url = new URL(request.url);
    const supplierId = url.searchParams.get("supplierId");
    const startDate = url.searchParams.get("startDate");
    const endDate = url.searchParams.get("endDate");
    const includeInactive = url.searchParams.get("includeInactive") === "true";

    // Set default date range (last 90 days)
    const defaultEndDate = new Date();
    const defaultStartDate = new Date();
    defaultStartDate.setDate(defaultStartDate.getDate() - 90);

    const dateFilter = {
      gte: startDate ? new Date(startDate) : defaultStartDate,
      lte: endDate ? new Date(endDate) : defaultEndDate,
    };

    // Build supplier filter
    const supplierFilter: any = {};
    if (supplierId) {
      supplierFilter.id = supplierId;
    }

    // Get supplier performance data
    const suppliers = await prisma.supplier.findMany({
      where: supplierFilter,
      include: {
        productSuppliers: {
          where: includeInactive ? {} : { isActive: true },
          include: {
            product: {
              select: {
                id: true,
                name: true,
                sku: true,
              }
            },
            purchaseOrderItems: {
              where: {
                purchaseOrder: {
                  orderDate: dateFilter,
                  status: { in: ['APPROVED', 'RECEIVED', 'PARTIALLY_RECEIVED'] }
                }
              },
              include: {
                purchaseOrder: {
                  select: {
                    id: true,
                    orderDate: true,
                    status: true,
                    total: true,
                  }
                }
              }
            },
            stockBatches: {
              where: {
                receivedDate: dateFilter
              },
              select: {
                id: true,
                quantity: true,
                remainingQuantity: true,
                purchasePrice: true,
                receivedDate: true,
                expiryDate: true,
                status: true,
              }
            }
          }
        },
        purchaseOrders: {
          where: {
            orderDate: dateFilter,
            status: { in: ['APPROVED', 'RECEIVED', 'PARTIALLY_RECEIVED'] }
          },
          select: {
            id: true,
            orderDate: true,
            status: true,
            total: true,
            subtotal: true,
            receivedAt: true,
          }
        }
      }
    });

    // Calculate analytics for each supplier
    const supplierAnalytics = suppliers.map(supplier => {
      const productCount = supplier.productSuppliers.length;
      const activeProductCount = supplier.productSuppliers.filter(ps => ps.isActive).length;
      const preferredProductCount = supplier.productSuppliers.filter(ps => ps.isPreferred).length;

      // Purchase order metrics
      const totalPurchaseOrders = supplier.purchaseOrders.length;
      const totalPurchaseValue = supplier.purchaseOrders.reduce((sum, po) => sum + Number(po.total), 0);
      const averageOrderValue = totalPurchaseOrders > 0 ? totalPurchaseValue / totalPurchaseOrders : 0;

      // Delivery performance
      const receivedOrders = supplier.purchaseOrders.filter(po => po.status === 'RECEIVED' && po.receivedAt);
      const averageDeliveryTime = receivedOrders.length > 0 
        ? receivedOrders.reduce((sum, po) => {
            const deliveryDays = Math.ceil((po.receivedAt!.getTime() - po.orderDate.getTime()) / (1000 * 60 * 60 * 24));
            return sum + deliveryDays;
          }, 0) / receivedOrders.length
        : 0;

      // Stock batch metrics
      const allBatches = supplier.productSuppliers.flatMap(ps => ps.stockBatches);
      const totalBatchesReceived = allBatches.length;
      const totalQuantityReceived = allBatches.reduce((sum, batch) => sum + Number(batch.quantity), 0);
      const totalValueReceived = allBatches.reduce((sum, batch) => sum + (Number(batch.quantity) * Number(batch.purchasePrice)), 0);
      
      // Quality metrics (based on remaining vs received quantities)
      const totalRemaining = allBatches.reduce((sum, batch) => sum + Number(batch.remainingQuantity), 0);
      const sellThroughRate = totalQuantityReceived > 0 ? ((totalQuantityReceived - totalRemaining) / totalQuantityReceived) * 100 : 0;

      // Expiry analysis
      const expiredBatches = allBatches.filter(batch => batch.expiryDate && batch.expiryDate < new Date());
      const expiryLossValue = expiredBatches.reduce((sum, batch) => sum + (Number(batch.remainingQuantity) * Number(batch.purchasePrice)), 0);

      // Price competitiveness (average purchase price vs market)
      const averagePurchasePrice = supplier.productSuppliers.length > 0
        ? supplier.productSuppliers.reduce((sum, ps) => sum + Number(ps.purchasePrice), 0) / supplier.productSuppliers.length
        : 0;

      // Recent activity
      const recentOrders = supplier.purchaseOrders
        .filter(po => {
          const thirtyDaysAgo = new Date();
          thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
          return po.orderDate >= thirtyDaysAgo;
        }).length;

      return {
        supplier: {
          id: supplier.id,
          name: supplier.name,
          contactPerson: supplier.contactPerson,
          phone: supplier.phone,
          email: supplier.email,
          address: supplier.address,
        },
        metrics: {
          productCount,
          activeProductCount,
          preferredProductCount,
          totalPurchaseOrders,
          totalPurchaseValue,
          averageOrderValue,
          averageDeliveryTime,
          totalBatchesReceived,
          totalQuantityReceived,
          totalValueReceived,
          sellThroughRate,
          expiryLossValue,
          averagePurchasePrice,
          recentOrders,
        },
        performance: {
          deliveryScore: averageDeliveryTime > 0 ? Math.max(0, 100 - (averageDeliveryTime - 7) * 5) : 100, // Penalty for delays beyond 7 days
          qualityScore: Math.min(100, sellThroughRate), // Based on sell-through rate
          priceScore: 85, // Placeholder - would need market data for comparison
          overallScore: 0, // Will be calculated below
        }
      };
    });

    // Calculate overall performance scores
    supplierAnalytics.forEach(analytics => {
      const { deliveryScore, qualityScore, priceScore } = analytics.performance;
      analytics.performance.overallScore = Math.round((deliveryScore * 0.4 + qualityScore * 0.4 + priceScore * 0.2));
    });

    // Sort by overall performance score
    supplierAnalytics.sort((a, b) => b.performance.overallScore - a.performance.overallScore);

    // Calculate summary statistics
    const summary = {
      totalSuppliers: supplierAnalytics.length,
      totalPurchaseValue: supplierAnalytics.reduce((sum, s) => sum + s.metrics.totalPurchaseValue, 0),
      totalPurchaseOrders: supplierAnalytics.reduce((sum, s) => sum + s.metrics.totalPurchaseOrders, 0),
      averageDeliveryTime: supplierAnalytics.length > 0 
        ? supplierAnalytics.reduce((sum, s) => sum + s.metrics.averageDeliveryTime, 0) / supplierAnalytics.length 
        : 0,
      averagePerformanceScore: supplierAnalytics.length > 0
        ? supplierAnalytics.reduce((sum, s) => sum + s.performance.overallScore, 0) / supplierAnalytics.length
        : 0,
      topPerformers: supplierAnalytics.slice(0, 5), // Top 5 performers
      underPerformers: supplierAnalytics.filter(s => s.performance.overallScore < 70), // Score below 70
    };

    return NextResponse.json({
      summary,
      suppliers: supplierAnalytics,
      dateRange: {
        startDate: dateFilter.gte,
        endDate: dateFilter.lte,
      }
    });
  } catch (error) {
    console.error("Error fetching supplier analytics:", error);
    return NextResponse.json(
      { error: "Failed to fetch supplier analytics", message: (error as Error).message },
      { status: 500 }
    );
  }
}
