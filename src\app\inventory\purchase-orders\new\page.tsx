"use client";

import { useState, useEffect } from "react";
import { use<PERSON>out<PERSON> } from "next/navigation";
import { MainLayout } from "@/components/layout/MainLayout";
import { PageHeader } from "@/components/layout/PageHeader";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Calendar } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { <PERSON><PERSON>, <PERSON>ertDescription } from "@/components/ui/alert";
import { ArrowLeft, Plus, Trash2, Loader2, Package, Calendar as CalendarIcon } from "lucide-react";
import Link from "next/link";
import { toast } from "sonner";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { format } from "date-fns";
import { cn } from "@/lib/utils";

interface Supplier {
  id: string;
  name: string;
  contactPerson?: string;
  phone?: string;
  email?: string;
}

interface Product {
  id: string;
  name: string;
  sku: string;
  purchasePrice?: number;
  category?: {
    name: string;
  };
  unit?: {
    name: string;
  };
}

interface POItem {
  productId: string;
  quantity: number;
  unitPrice: number;
  subtotal: number;
  product?: Product;
}

const purchaseOrderSchema = z.object({
  supplierId: z.string().min(1, { message: "Supplier is required" }),
  orderDate: z.date({ required_error: "Order date is required" }),
  taxPercentage: z.coerce
    .number()
    .min(0, { message: "Tax percentage must be non-negative" })
    .max(100, { message: "Tax percentage cannot exceed 100%" })
    .default(0),
  notes: z.string().optional(),
});

type PurchaseOrderFormValues = z.infer<typeof purchaseOrderSchema>;

export default function NewPurchaseOrderPage() {
  const router = useRouter();
  const [suppliers, setSuppliers] = useState<Supplier[]>([]);
  const [products, setProducts] = useState<Product[]>([]);
  const [items, setItems] = useState<POItem[]>([]);
  const [loading, setLoading] = useState(false);
  const [savingDraft, setSavingDraft] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const form = useForm<PurchaseOrderFormValues>({
    resolver: zodResolver(purchaseOrderSchema),
    defaultValues: {
      supplierId: "",
      orderDate: new Date(),
      taxPercentage: 0,
      notes: "",
    },
  });

  // Fetch suppliers and products
  useEffect(() => {
    const fetchData = async () => {
      try {
        const [suppliersRes, productsRes] = await Promise.all([
          fetch("/api/suppliers"),
          fetch("/api/products"),
        ]);

        if (!suppliersRes.ok || !productsRes.ok) {
          throw new Error("Failed to fetch data");
        }

        const [suppliersData, productsData] = await Promise.all([
          suppliersRes.json(),
          productsRes.json(),
        ]);

        setSuppliers(suppliersData.suppliers || []);
        setProducts(productsData.products || []);
      } catch (error) {
        console.error("Error fetching data:", error);
        toast.error("Failed to load suppliers and products");
      }
    };

    fetchData();
  }, []);

  const addItem = () => {
    setItems([
      ...items,
      {
        productId: "",
        quantity: 1,
        unitPrice: 0,
        subtotal: 0,
      },
    ]);
  };

  const removeItem = (index: number) => {
    setItems(items.filter((_, i) => i !== index));
  };

  const updateItem = (index: number, field: keyof POItem, value: any) => {
    const updatedItems = [...items];
    updatedItems[index] = { ...updatedItems[index], [field]: value };

    // If product changed, update unit price with purchase price
    if (field === "productId") {
      const product = products.find((p) => p.id === value);
      if (product) {
        updatedItems[index].product = product;
        updatedItems[index].unitPrice = product.purchasePrice || 0;
      }
    }

    // Recalculate subtotal
    if (field === "quantity" || field === "unitPrice" || field === "productId") {
      updatedItems[index].subtotal = updatedItems[index].quantity * updatedItems[index].unitPrice;
    }

    setItems(updatedItems);
  };

  const calculateTotals = () => {
    const subtotal = items.reduce((sum, item) => sum + item.subtotal, 0);
    const taxPercentage = form.getValues("taxPercentage") || 0;
    const taxAmount = subtotal * (taxPercentage / 100);
    const total = subtotal + taxAmount;
    return { subtotal, taxPercentage, taxAmount, total };
  };

  const saveDraft = async (data: PurchaseOrderFormValues) => {
    try {
      setSavingDraft(true);
      setError(null);

      const { taxAmount } = calculateTotals();

      const payload = {
        supplierId: data.supplierId,
        orderDate: data.orderDate.toISOString().split("T")[0],
        tax: Number(taxAmount),
        taxPercentage: Number(data.taxPercentage) || 0,
        notes: data.notes || "",
        items: items
          .filter((item) => item.productId)
          .map((item) => ({
            productId: item.productId,
            quantity: Number(item.quantity) || 0,
            unitPrice: Number(item.unitPrice) || 0,
          })),
        isDraft: true,
      };

      const response = await fetch("/api/purchase-orders", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(payload),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to save draft");
      }

      const result = await response.json();
      toast.success("Draft saved successfully");
      router.push(`/inventory/purchase-orders/${result.id}`);
    } catch (error) {
      console.error("Error saving draft:", error);
      toast.error(error instanceof Error ? error.message : "Failed to save draft");
    } finally {
      setSavingDraft(false);
    }
  };

  const onSubmit = async (data: PurchaseOrderFormValues) => {
    console.log("Form submitted with data:", data);
    console.log("Items:", items);

    if (items.length === 0) {
      toast.error("Please add at least one item");
      return;
    }

    // Validate all items have required fields
    const invalidItems = items.filter(
      (item) =>
        !item.productId ||
        !item.quantity ||
        item.quantity <= 0 ||
        !item.unitPrice ||
        item.unitPrice <= 0
    );

    console.log("Invalid items:", invalidItems);

    if (invalidItems.length > 0) {
      toast.error("Please fill in all item details with valid values");
      return;
    }

    try {
      setLoading(true);
      setError(null);

      const { taxAmount } = calculateTotals();

      const payload = {
        supplierId: data.supplierId,
        orderDate: data.orderDate.toISOString().split("T")[0], // Send date as YYYY-MM-DD string
        tax: Number(taxAmount), // Send calculated tax amount to API
        taxPercentage: Number(data.taxPercentage) || 0, // Also send percentage for reference
        notes: data.notes || "",
        items: items.map((item) => ({
          productId: item.productId,
          quantity: Number(item.quantity), // Ensure quantity is a number
          unitPrice: Number(item.unitPrice), // Ensure unitPrice is a number
        })),
      };

      console.log("Payload being sent:", payload);

      const response = await fetch("/api/purchase-orders", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(payload),
      });

      if (!response.ok) {
        const errorData = await response.json();
        console.error("API Error Response:", errorData);

        // Show detailed validation errors if available
        if (errorData.issues) {
          console.error("Validation Issues:", errorData.issues);
          const validationErrors = errorData.issues
            .map((issue: any) => `${issue.path.join(".")}: ${issue.message}`)
            .join(", ");
          throw new Error(`Validation failed: ${validationErrors}`);
        }

        throw new Error(errorData.error || "Failed to create purchase order");
      }

      const result = await response.json();
      toast.success("Purchase order created successfully");
      router.push(`/inventory/purchase-orders/${result.id}`);
    } catch (error) {
      console.error("Error creating purchase order:", error);
      setError(error instanceof Error ? error.message : "Failed to create purchase order");
      toast.error("Failed to create purchase order");
    } finally {
      setLoading(false);
    }
  };

  const { subtotal, taxPercentage, taxAmount, total } = calculateTotals();

  return (
    <MainLayout>
      <PageHeader
        title="Create Purchase Order"
        description="Create a new purchase order for supplier"
        actions={
          <Button variant="outline" asChild>
            <Link href="/inventory/purchase-orders">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Purchase Orders
            </Link>
          </Button>
        }
      />

      {error && (
        <Alert variant="destructive" className="mb-6">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      <div className="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle>Purchase Order Details</CardTitle>
          </CardHeader>
          <CardContent>
            <Form {...form}>
              <form
                onSubmit={form.handleSubmit(onSubmit)}
                className="space-y-6"
                id="purchase-order-form"
              >
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <FormField
                    control={form.control}
                    name="supplierId"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Supplier</FormLabel>
                        <Select onValueChange={field.onChange} defaultValue={field.value}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select supplier" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {suppliers.map((supplier) => (
                              <SelectItem key={supplier.id} value={supplier.id}>
                                {supplier.name}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="orderDate"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Order Date</FormLabel>
                        <Popover>
                          <PopoverTrigger asChild>
                            <FormControl>
                              <Button
                                variant="outline"
                                className={cn(
                                  "w-full justify-start text-left font-normal",
                                  !field.value && "text-muted-foreground"
                                )}
                              >
                                <CalendarIcon className="mr-2 h-4 w-4" />
                                {field.value ? format(field.value, "PPP") : "Select date"}
                              </Button>
                            </FormControl>
                          </PopoverTrigger>
                          <PopoverContent className="w-auto p-0">
                            <Calendar
                              mode="single"
                              selected={field.value}
                              onSelect={field.onChange}
                              initialFocus
                            />
                          </PopoverContent>
                        </Popover>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="taxPercentage"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Tax Percentage (%)</FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            placeholder="0"
                            min="0"
                            max="100"
                            step="0.1"
                            {...field}
                            onChange={(e) => {
                              field.onChange(e.target.valueAsNumber || 0);
                            }}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <FormField
                  control={form.control}
                  name="notes"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Notes</FormLabel>
                      <FormControl>
                        <Textarea placeholder="Additional notes or comments..." {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </form>
            </Form>
          </CardContent>
        </Card>

        {/* Items Section */}
        <Card>
          <CardHeader>
            <div className="flex justify-between items-center">
              <CardTitle>Items</CardTitle>
              <Button onClick={addItem} variant="outline">
                <Plus className="h-4 w-4 mr-2" />
                Add Item
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            {items.length === 0 ? (
              <div className="text-center p-8 border-2 border-dashed rounded-lg">
                <Package className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                <h3 className="text-lg font-medium mb-2">No items added</h3>
                <p className="text-muted-foreground mb-4">Add products to this purchase order</p>
                <Button onClick={addItem}>
                  <Plus className="h-4 w-4 mr-2" />
                  Add First Item
                </Button>
              </div>
            ) : (
              <div className="space-y-4">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Product</TableHead>
                      <TableHead>Quantity</TableHead>
                      <TableHead>Unit Price</TableHead>
                      <TableHead>Subtotal</TableHead>
                      <TableHead className="w-[50px]"></TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {items.map((item, index) => (
                      <TableRow key={index}>
                        <TableCell>
                          <Select
                            value={item.productId}
                            onValueChange={(value) => updateItem(index, "productId", value)}
                          >
                            <SelectTrigger>
                              <SelectValue placeholder="Select product" />
                            </SelectTrigger>
                            <SelectContent>
                              {products.map((product) => (
                                <SelectItem key={product.id} value={product.id}>
                                  {product.name} ({product.sku})
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </TableCell>
                        <TableCell>
                          <Input
                            type="number"
                            min="1"
                            value={item.quantity}
                            onChange={(e) =>
                              updateItem(index, "quantity", parseInt(e.target.value) || 0)
                            }
                            className="w-20"
                          />
                        </TableCell>
                        <TableCell>
                          <Input
                            type="number"
                            min="0"
                            step="0.01"
                            value={item.unitPrice}
                            onChange={(e) =>
                              updateItem(index, "unitPrice", parseFloat(e.target.value) || 0)
                            }
                            className="w-32"
                          />
                        </TableCell>
                        <TableCell>Rp {item.subtotal.toLocaleString("id-ID")}</TableCell>
                        <TableCell>
                          <Button variant="ghost" size="sm" onClick={() => removeItem(index)}>
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>

                {/* Totals */}
                <div className="flex justify-end">
                  <div className="w-64 space-y-2">
                    <div className="flex justify-between">
                      <span>Subtotal:</span>
                      <span>Rp {subtotal.toLocaleString("id-ID")}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Tax ({taxPercentage}%):</span>
                      <span>Rp {taxAmount.toLocaleString("id-ID")}</span>
                    </div>
                    <div className="flex justify-between font-bold border-t pt-2">
                      <span>Total:</span>
                      <span>Rp {total.toLocaleString("id-ID")}</span>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Actions */}
        <div className="flex justify-end gap-4">
          <Button variant="outline" asChild>
            <Link href="/inventory/purchase-orders">Cancel</Link>
          </Button>
          <Button
            variant="secondary"
            onClick={() => form.handleSubmit(saveDraft)()}
            disabled={savingDraft || loading || !form.watch("supplierId")}
          >
            {savingDraft && <Loader2 className="h-4 w-4 mr-2 animate-spin" />}
            Save as Draft
          </Button>
          <Button type="submit" form="purchase-order-form" disabled={loading || items.length === 0}>
            {loading && <Loader2 className="h-4 w-4 mr-2 animate-spin" />}
            Create Purchase Order
          </Button>
        </div>
      </div>
    </MainLayout>
  );
}
