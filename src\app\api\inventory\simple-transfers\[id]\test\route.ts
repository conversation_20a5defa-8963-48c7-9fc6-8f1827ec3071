import { NextRequest, NextResponse } from "next/server";

// Simple test endpoint to verify route handler registration
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  console.log("[API] GET /api/inventory/simple-transfers/[id]/test - Start");
  console.log("[API] Transfer ID:", params.id);
  
  return NextResponse.json({
    message: "Test endpoint working",
    transferId: params.id,
    timestamp: new Date().toISOString()
  });
}

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  console.log("[API] POST /api/inventory/simple-transfers/[id]/test - Start");
  console.log("[API] Transfer ID:", params.id);
  
  let body;
  try {
    body = await request.json();
  } catch (error) {
    body = { error: "Could not parse request body" };
  }
  
  return NextResponse.json({
    message: "Test endpoint working",
    transferId: params.id,
    body,
    timestamp: new Date().toISOString()
  });
}
