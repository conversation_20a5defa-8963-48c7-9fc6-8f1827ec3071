import { createMockAuthRequest, runApiTest, mockPrisma } from "./testUtils";
import { NextResponse } from "next/server";

// Mock API handlers
// We need to create these instead of importing the actual handlers
// because the actual handlers use server-side modules that aren't available in the browser

// Mock Store Stock API
const getStoreStock = async (req: Request) => {
  return NextResponse.json({
    storeStock: await mockPrisma.storeStock.findMany(),
    pagination: {
      total: await mockPrisma.storeStock.count(),
      page: 1,
      limit: 10,
      pages: 1
    }
  });
};

const updateStoreStock = async (req: Request) => {
  const body = await req.json();
  const storeStock = await mockPrisma.storeStock.upsert();
  return NextResponse.json({ storeStock });
};

// Mock Adjustments API
const getAdjustments = async (req: Request) => {
  return NextResponse.json({
    adjustments: await mockPrisma.stockAdjustment.findMany(),
    pagination: {
      total: await mockPrisma.stockAdjustment.count(),
      page: 1,
      limit: 10,
      pages: 1
    }
  });
};

const createAdjustment = async (req: Request) => {
  const body = await req.json();
  const adjustment = await mockPrisma.stockAdjustment.create();
  return NextResponse.json({ adjustment });
};

// Mock Stock History API
const getStockHistory = async (req: Request) => {
  return NextResponse.json({
    history: await mockPrisma.stockHistory.findMany(),
    pagination: {
      total: await mockPrisma.stockHistory.count(),
      page: 1,
      limit: 10,
      pages: 1
    }
  });
};

// Mock Transfers API
const getTransfers = async (req: Request) => {
  return NextResponse.json({
    transfers: await mockPrisma.stockTransfer.findMany(),
    pagination: {
      total: await mockPrisma.stockTransfer.count(),
      page: 1,
      limit: 10,
      pages: 1
    }
  });
};

const createTransfer = async (req: Request) => {
  const body = await req.json();
  const transfer = await mockPrisma.stockTransfer.create();
  return NextResponse.json({ transfer });
};

const getTransfer = async (req: Request, { params }: { params: { id: string } }) => {
  const transfer = await mockPrisma.stockTransfer.findUnique();
  return NextResponse.json({ transfer });
};

const updateTransfer = async (req: Request, { params }: { params: { id: string } }) => {
  const body = await req.json();
  const transfer = await mockPrisma.stockTransfer.update();
  return NextResponse.json({ transfer });
};

// Mock Low Stock API
const getLowStock = async (req: Request) => {
  const products = await mockPrisma.product.findMany();
  const lowStockProducts = products.map(product => ({
    ...product,
    stockStatus: "LOW",
    percentRemaining: 60
  }));

  return NextResponse.json({
    lowStockProducts,
    pagination: {
      total: lowStockProducts.length,
      page: 1,
      limit: 10,
      pages: 1
    }
  });
};

const createLowStockNotifications = async (req: Request) => {
  const notification = await mockPrisma.notification.create();
  return NextResponse.json({
    success: true,
    notificationsCreated: 2,
    lowStockCount: 2
  });
};

// Mock Reports API
const getInventoryReports = async (req: Request) => {
  const url = new URL(req.url);
  const reportType = url.searchParams.get("type") || "summary";

  if (reportType === "summary") {
    return NextResponse.json({
      reportType: "summary",
      generatedAt: new Date(),
      summary: {
        totalProducts: 2,
        totalStoreQuantity: 13,
        totalWarehouseQuantity: 20,
        lowStockCount: 1,
        outOfStockCount: 0
      },
      products: await mockPrisma.product.findMany()
    });
  } else if (reportType === "valuation") {
    return NextResponse.json({
      reportType: "valuation",
      generatedAt: new Date(),
      summary: {
        totalProducts: 2,
        totalStoreValue: 142.87,
        totalWarehouseValue: 219.8,
        totalInventoryValue: 362.67
      },
      products: await mockPrisma.product.findMany()
    });
  } else if (reportType === "movement") {
    return NextResponse.json({
      reportType: "movement",
      generatedAt: new Date(),
      period: {
        startDate: new Date("2023-01-01"),
        endDate: new Date("2023-12-31")
      },
      summary: {
        totalProducts: 2,
        totalMovements: 5,
        totalIn: 30,
        totalOut: 15,
        netChange: 15
      },
      products: await mockPrisma.product.findMany()
    });
  } else if (reportType === "low_stock") {
    return NextResponse.json({
      reportType: "low_stock",
      generatedAt: new Date(),
      summary: {
        totalLowStock: 1,
        criticalCount: 0,
        outOfStockCount: 0
      },
      products: await mockPrisma.product.findMany()
    });
  }

  return NextResponse.json({ error: "Invalid report type" }, { status: 400 });
};

// Store Stock API Tests
export async function runStoreStockTests() {
  const tests = [];

  // Test GET /api/inventory/store-stock
  tests.push(
    await runApiTest(
      "GET /api/inventory/store-stock",
      getStoreStock,
      createMockAuthRequest("http://localhost:3000/api/inventory/store-stock")
    )
  );

  // Test GET /api/inventory/store-stock with filters
  tests.push(
    await runApiTest(
      "GET /api/inventory/store-stock with filters",
      getStoreStock,
      createMockAuthRequest("http://localhost:3000/api/inventory/store-stock?productId=mock-product-id&lowStock=true")
    )
  );

  // Test POST /api/inventory/store-stock
  tests.push(
    await runApiTest(
      "POST /api/inventory/store-stock",
      updateStoreStock,
      createMockAuthRequest(
        "http://localhost:3000/api/inventory/store-stock",
        "POST",
        {
          productId: "mock-product-id",
          quantity: 15,
          minThreshold: 5,
          maxThreshold: 30
        }
      )
    )
  );

  return tests;
}

// Stock Adjustments API Tests
export async function runAdjustmentTests() {
  const tests = [];

  // Test GET /api/inventory/adjustments
  tests.push(
    await runApiTest(
      "GET /api/inventory/adjustments",
      getAdjustments,
      createMockAuthRequest("http://localhost:3000/api/inventory/adjustments")
    )
  );

  // Test GET /api/inventory/adjustments with filters
  tests.push(
    await runApiTest(
      "GET /api/inventory/adjustments with filters",
      getAdjustments,
      createMockAuthRequest("http://localhost:3000/api/inventory/adjustments?productId=mock-product-id&reason=INVENTORY_COUNT")
    )
  );

  // Test POST /api/inventory/adjustments (store)
  tests.push(
    await runApiTest(
      "POST /api/inventory/adjustments (store)",
      createAdjustment,
      createMockAuthRequest(
        "http://localhost:3000/api/inventory/adjustments",
        "POST",
        {
          productId: "mock-product-id",
          locationType: "STORE",
          adjustmentQuantity: 5,
          reason: "INVENTORY_COUNT",
          notes: "Test adjustment"
        }
      )
    )
  );

  // Test POST /api/inventory/adjustments (warehouse)
  tests.push(
    await runApiTest(
      "POST /api/inventory/adjustments (warehouse)",
      createAdjustment,
      createMockAuthRequest(
        "http://localhost:3000/api/inventory/adjustments",
        "POST",
        {
          productId: "mock-product-id",
          locationType: "WAREHOUSE",
          adjustmentQuantity: 5,
          reason: "INVENTORY_COUNT",
          notes: "Test adjustment"
        }
      )
    )
  );

  return tests;
}

// Stock History API Tests
export async function runHistoryTests() {
  const tests = [];

  // Test GET /api/inventory/history
  tests.push(
    await runApiTest(
      "GET /api/inventory/history",
      getStockHistory,
      createMockAuthRequest("http://localhost:3000/api/inventory/history")
    )
  );

  // Test GET /api/inventory/history with filters
  tests.push(
    await runApiTest(
      "GET /api/inventory/history with filters",
      getStockHistory,
      createMockAuthRequest("http://localhost:3000/api/inventory/history?productId=mock-product-id&source=ADJUSTMENT&locationType=STORE")
    )
  );

  return tests;
}

// Stock Transfers API Tests
export async function runTransferTests() {
  const tests = [];

  // Test GET /api/inventory/transfers
  tests.push(
    await runApiTest(
      "GET /api/inventory/transfers",
      getTransfers,
      createMockAuthRequest("http://localhost:3000/api/inventory/transfers")
    )
  );

  // Test GET /api/inventory/transfers with filters
  tests.push(
    await runApiTest(
      "GET /api/inventory/transfers with filters",
      getTransfers,
      createMockAuthRequest("http://localhost:3000/api/inventory/transfers?productId=mock-product-id&status=PENDING")
    )
  );

  // Test POST /api/inventory/transfers
  tests.push(
    await runApiTest(
      "POST /api/inventory/transfers",
      createTransfer,
      createMockAuthRequest(
        "http://localhost:3000/api/inventory/transfers",
        "POST",
        {
          productId: "mock-product-id",
          quantity: 5,
          sourceType: "STORE",
          sourceId: "mock-store-stock-id",
          destinationType: "WAREHOUSE",
          destinationId: "mock-warehouse-stock-id",
          notes: "Test transfer"
        }
      )
    )
  );

  // Test GET /api/inventory/transfers/[id]
  tests.push(
    await runApiTest(
      "GET /api/inventory/transfers/[id]",
      (req) => getTransfer(req, { params: { id: "mock-transfer-id" } }),
      createMockAuthRequest("http://localhost:3000/api/inventory/transfers/mock-transfer-id")
    )
  );

  // Test PATCH /api/inventory/transfers/[id] (approve)
  tests.push(
    await runApiTest(
      "PATCH /api/inventory/transfers/[id] (approve)",
      (req) => updateTransfer(req, { params: { id: "mock-transfer-id" } }),
      createMockAuthRequest(
        "http://localhost:3000/api/inventory/transfers/mock-transfer-id",
        "PATCH",
        {
          status: "APPROVED",
          notes: "Approved transfer"
        }
      )
    )
  );

  return tests;
}

// Low Stock API Tests
export async function runLowStockTests() {
  const tests = [];

  // Test GET /api/inventory/low-stock
  tests.push(
    await runApiTest(
      "GET /api/inventory/low-stock",
      getLowStock,
      createMockAuthRequest("http://localhost:3000/api/inventory/low-stock")
    )
  );

  // Test GET /api/inventory/low-stock with filters
  tests.push(
    await runApiTest(
      "GET /api/inventory/low-stock with filters",
      getLowStock,
      createMockAuthRequest("http://localhost:3000/api/inventory/low-stock?categoryId=mock-category-id")
    )
  );

  // Test POST /api/inventory/low-stock/notify
  tests.push(
    await runApiTest(
      "POST /api/inventory/low-stock/notify",
      createLowStockNotifications,
      createMockAuthRequest("http://localhost:3000/api/inventory/low-stock/notify", "POST")
    )
  );

  return tests;
}

// Inventory Reports API Tests
export async function runReportTests() {
  const tests = [];

  // Test GET /api/inventory/reports (summary)
  tests.push(
    await runApiTest(
      "GET /api/inventory/reports (summary)",
      getInventoryReports,
      createMockAuthRequest("http://localhost:3000/api/inventory/reports?type=summary")
    )
  );

  // Test GET /api/inventory/reports (valuation)
  tests.push(
    await runApiTest(
      "GET /api/inventory/reports (valuation)",
      getInventoryReports,
      createMockAuthRequest("http://localhost:3000/api/inventory/reports?type=valuation")
    )
  );

  // Test GET /api/inventory/reports (movement)
  tests.push(
    await runApiTest(
      "GET /api/inventory/reports (movement)",
      getInventoryReports,
      createMockAuthRequest("http://localhost:3000/api/inventory/reports?type=movement&startDate=2023-01-01&endDate=2023-12-31")
    )
  );

  // Test GET /api/inventory/reports (low_stock)
  tests.push(
    await runApiTest(
      "GET /api/inventory/reports (low_stock)",
      getInventoryReports,
      createMockAuthRequest("http://localhost:3000/api/inventory/reports?type=low_stock")
    )
  );

  return tests;
}

// Run all inventory API tests
export async function runAllInventoryTests() {
  const storeStockTests = await runStoreStockTests();
  const adjustmentTests = await runAdjustmentTests();
  const historyTests = await runHistoryTests();
  const transferTests = await runTransferTests();
  const lowStockTests = await runLowStockTests();
  const reportTests = await runReportTests();

  return [
    ...storeStockTests,
    ...adjustmentTests,
    ...historyTests,
    ...transferTests,
    ...lowStockTests,
    ...reportTests
  ];
}
