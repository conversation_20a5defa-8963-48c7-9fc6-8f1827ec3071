/*
  Warnings:

  - A unique constraint covering the columns `[userId]` on the table `CashDrawer` will be added. If there are existing duplicate values, this will fail.

*/
-- AlterTable
ALTER TABLE "CashDrawer" ADD COLUMN     "userId" TEXT;

-- CreateIndex
CREATE UNIQUE INDEX "CashDrawer_userId_key" ON "CashDrawer"("userId");

-- AddForeignKey
ALTER TABLE "CashDrawer" ADD CONSTRAINT "CashDrawer_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE SET NULL ON UPDATE CASCADE;
