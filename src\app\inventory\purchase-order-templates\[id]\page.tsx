"use client";

import { useState, useEffect } from "react";
import { usePara<PERSON>, useRouter } from "next/navigation";
import { MainLayout } from "@/components/layout/MainLayout";
import { PageHeader } from "@/components/layout/PageHeader";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { toast } from "sonner";
import { Loader2, Edit, Copy, Trash2, ArrowLeft } from "lucide-react";
import Link from "next/link";

interface POTemplate {
  id: string;
  name: string;
  description?: string;
  supplier: {
    id: string;
    name: string;
    contactPerson?: string;
    phone?: string;
    email?: string;
  };
  taxPercentage?: number;
  notes?: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  createdBy: {
    id: string;
    name: string;
    email: string;
  };
  items: Array<{
    id: string;
    quantity: number;
    unitPrice: number;
    product: {
      id: string;
      name: string;
      sku: string;
      category?: {
        name: string;
      };
      unit?: {
        name: string;
      };
    };
  }>;
}

export default function PurchaseOrderTemplateDetailPage() {
  const params = useParams();
  const router = useRouter();
  const [template, setTemplate] = useState<POTemplate | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const templateId = params.id as string;

  // Fetch template details
  const fetchTemplate = async () => {
    setLoading(true);
    setError(null);
    try {
      const response = await fetch(`/api/purchase-order-templates/${templateId}`);
      if (!response.ok) {
        if (response.status === 404) {
          throw new Error("Template not found");
        }
        throw new Error("Failed to fetch template");
      }

      const data = await response.json();
      setTemplate(data);
    } catch (error) {
      console.error("Error fetching template:", error);
      setError(error instanceof Error ? error.message : "Failed to load template");
      toast.error("Failed to load template");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchTemplate();
  }, [templateId]);

  const handleDeleteTemplate = async () => {
    if (!confirm("Are you sure you want to delete this template?")) return;

    try {
      const response = await fetch(`/api/purchase-order-templates/${templateId}`, {
        method: "DELETE",
      });

      if (!response.ok) throw new Error("Failed to delete template");

      toast.success("Template deleted successfully");
      router.push("/inventory/purchase-order-templates");
    } catch (error) {
      console.error("Error deleting template:", error);
      toast.error("Failed to delete template");
    }
  };

  const handleCreatePOFromTemplate = async () => {
    try {
      const response = await fetch(`/api/purchase-order-templates/${templateId}/create-po`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          orderDate: new Date().toISOString(),
        }),
      });

      if (!response.ok) throw new Error("Failed to create PO from template");

      const data = await response.json();
      toast.success("Purchase order created successfully from template");
      
      // Redirect to the new PO
      router.push(`/inventory/purchase-orders/${data.purchaseOrder.id}`);
    } catch (error) {
      console.error("Error creating PO from template:", error);
      toast.error("Failed to create PO from template");
    }
  };

  const calculateTemplateTotal = () => {
    if (!template) return 0;
    const subtotal = template.items.reduce((sum, item) => sum + (item.quantity * item.unitPrice), 0);
    const tax = template.taxPercentage ? (subtotal * template.taxPercentage) / 100 : 0;
    return subtotal + tax;
  };

  const calculateSubtotal = () => {
    if (!template) return 0;
    return template.items.reduce((sum, item) => sum + (item.quantity * item.unitPrice), 0);
  };

  const calculateTax = () => {
    if (!template) return 0;
    const subtotal = calculateSubtotal();
    return template.taxPercentage ? (subtotal * template.taxPercentage) / 100 : 0;
  };

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency: 'IDR',
      minimumFractionDigits: 0,
    }).format(value);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('id-ID');
  };

  if (loading) {
    return (
      <MainLayout>
        <div className="flex justify-center items-center p-12">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
          <span className="ml-2">Loading template...</span>
        </div>
      </MainLayout>
    );
  }

  if (error || !template) {
    return (
      <MainLayout>
        <div className="text-center p-12">
          <h3 className="text-lg font-medium mb-2">Template not found</h3>
          <p className="text-muted-foreground mb-4">{error || "The requested template could not be found."}</p>
          <Button asChild>
            <Link href="/inventory/purchase-order-templates">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Templates
            </Link>
          </Button>
        </div>
      </MainLayout>
    );
  }

  return (
    <MainLayout>
      <PageHeader
        title={template.name}
        description="Purchase order template details"
        actions={
          <div className="flex items-center gap-2">
            <Button variant="outline" asChild>
              <Link href="/inventory/purchase-order-templates">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Templates
              </Link>
            </Button>
            <Button variant="outline" onClick={handleCreatePOFromTemplate}>
              <Copy className="h-4 w-4 mr-2" />
              Create PO from Template
            </Button>
            <Button variant="outline" asChild>
              <Link href={`/inventory/purchase-order-templates/${templateId}/edit`}>
                <Edit className="h-4 w-4 mr-2" />
                Edit
              </Link>
            </Button>
            <Button
              variant="outline"
              onClick={handleDeleteTemplate}
              className="text-red-600 hover:text-red-700"
            >
              <Trash2 className="h-4 w-4 mr-2" />
              Delete
            </Button>
          </div>
        }
      />

      <div className="space-y-6">
        {/* Template Information */}
        <Card>
          <CardHeader>
            <CardTitle>Template Information</CardTitle>
            <CardDescription>Basic details about this purchase order template</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Template Name</label>
                  <p className="text-sm">{template.name}</p>
                </div>
                {template.description && (
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">Description</label>
                    <p className="text-sm">{template.description}</p>
                  </div>
                )}
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Supplier</label>
                  <p className="text-sm">{template.supplier.name}</p>
                  {template.supplier.contactPerson && (
                    <p className="text-xs text-muted-foreground">Contact: {template.supplier.contactPerson}</p>
                  )}
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Status</label>
                  <div>
                    <Badge variant={template.isActive ? "default" : "secondary"}>
                      {template.isActive ? "Active" : "Inactive"}
                    </Badge>
                  </div>
                </div>
              </div>
              <div className="space-y-4">
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Tax Percentage</label>
                  <p className="text-sm">{template.taxPercentage || 0}%</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Created By</label>
                  <p className="text-sm">{template.createdBy.name}</p>
                  <p className="text-xs text-muted-foreground">{template.createdBy.email}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Created Date</label>
                  <p className="text-sm">{formatDate(template.createdAt)}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Last Updated</label>
                  <p className="text-sm">{formatDate(template.updatedAt)}</p>
                </div>
              </div>
            </div>
            {template.notes && (
              <div className="mt-6">
                <label className="text-sm font-medium text-muted-foreground">Notes</label>
                <p className="text-sm mt-1">{template.notes}</p>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Template Items */}
        <Card>
          <CardHeader>
            <CardTitle>Template Items ({template.items.length})</CardTitle>
            <CardDescription>Products included in this template</CardDescription>
          </CardHeader>
          <CardContent>
            {template.items.length === 0 ? (
              <div className="text-center py-8 text-muted-foreground">
                No items in this template
              </div>
            ) : (
              <div className="space-y-4">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Product</TableHead>
                      <TableHead>SKU</TableHead>
                      <TableHead>Category</TableHead>
                      <TableHead>Unit</TableHead>
                      <TableHead>Quantity</TableHead>
                      <TableHead>Unit Price</TableHead>
                      <TableHead>Subtotal</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {template.items.map((item) => (
                      <TableRow key={item.id}>
                        <TableCell>
                          <div className="font-medium">{item.product.name}</div>
                        </TableCell>
                        <TableCell>{item.product.sku}</TableCell>
                        <TableCell>{item.product.category?.name || "N/A"}</TableCell>
                        <TableCell>{item.product.unit?.name || "N/A"}</TableCell>
                        <TableCell>{item.quantity}</TableCell>
                        <TableCell>{formatCurrency(item.unitPrice)}</TableCell>
                        <TableCell>{formatCurrency(item.quantity * item.unitPrice)}</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>

                {/* Totals */}
                <div className="border-t pt-4">
                  <div className="flex justify-end">
                    <div className="w-64 space-y-2">
                      <div className="flex justify-between">
                        <span>Subtotal:</span>
                        <span>{formatCurrency(calculateSubtotal())}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Tax ({template.taxPercentage || 0}%):</span>
                        <span>{formatCurrency(calculateTax())}</span>
                      </div>
                      <div className="flex justify-between font-bold text-lg border-t pt-2">
                        <span>Estimated Total:</span>
                        <span>{formatCurrency(calculateTemplateTotal())}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </MainLayout>
  );
}
