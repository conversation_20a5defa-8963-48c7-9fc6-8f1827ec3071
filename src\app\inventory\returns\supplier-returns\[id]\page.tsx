"use client";

import { useState, useEffect } from "react";
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
import { MainLayout } from "@/components/layout/MainLayout";
import { PageHeader } from "@/components/layout/PageHeader";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  ArrowLeft,
  CheckCircle,
  XCircle,
  Clock,
  Package,
  User,
  Calendar,
  FileText,
  Edit,
  Truck,
} from "lucide-react";
import Link from "next/link";
import { toast } from "sonner";

interface SupplierReturnItem {
  id: string;
  productId: string;
  quantity: number;
  unitPrice: number;
  subtotal: number;
  product: {
    id: string;
    name: string;
    sku: string;
    category?: {
      name: string;
    };
    unit?: {
      name: string;
    };
  };
}

interface SupplierReturn {
  id: string;
  returnDate: string;
  reason: string;
  total: number;
  status: "PENDING" | "APPROVED" | "COMPLETED" | "REJECTED";
  notes?: string;
  createdAt: string;
  updatedAt: string;
  supplier: {
    id: string;
    name: string;
  };
  purchaseOrder: {
    id: string;
    createdBy?: {
      id: string;
      name: string;
      email: string;
    };
  } | null;
  items: SupplierReturnItem[];
}

export default function SupplierReturnDetailPage() {
  const params = useParams();
  const router = useRouter();
  const [supplierReturn, setSupplierReturn] = useState<SupplierReturn | null>(null);
  const [loading, setLoading] = useState(true);
  const [actionLoading, setActionLoading] = useState(false);
  const [showApproveDialog, setShowApproveDialog] = useState(false);
  const [showRejectDialog, setShowRejectDialog] = useState(false);
  const [showCompleteDialog, setShowCompleteDialog] = useState(false);
  const [actionNotes, setActionNotes] = useState("");

  const statusColors = {
    PENDING: "bg-yellow-100 text-yellow-800 border-yellow-200",
    APPROVED: "bg-blue-100 text-blue-800 border-blue-200",
    COMPLETED: "bg-green-100 text-green-800 border-green-200",
    REJECTED: "bg-red-100 text-red-800 border-red-200",
  };

  const statusIcons = {
    PENDING: <Clock className="h-3 w-3" />,
    APPROVED: <CheckCircle className="h-3 w-3" />,
    COMPLETED: <Package className="h-3 w-3" />,
    REJECTED: <XCircle className="h-3 w-3" />,
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("id-ID", {
      style: "currency",
      currency: "IDR",
    }).format(amount);
  };

  const fetchSupplierReturn = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/supplier-returns/${params.id}`);
      if (!response.ok) {
        if (response.status === 404) {
          toast.error("Supplier return not found");
          router.push("/inventory/returns/supplier-returns");
          return;
        }
        throw new Error("Failed to fetch supplier return");
      }
      const data = await response.json();
      setSupplierReturn(data);
    } catch (error) {
      console.error("Error fetching supplier return:", error);
      toast.error("Failed to load supplier return details");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchSupplierReturn();
  }, [params.id]);

  const handleStatusUpdate = async (newStatus: string, notes?: string) => {
    if (!supplierReturn) return;

    try {
      setActionLoading(true);
      const response = await fetch(`/api/supplier-returns/${supplierReturn.id}`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          status: newStatus,
          notes: notes || actionNotes,
        }),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || "Failed to update supplier return");
      }

      const updatedReturn = await response.json();
      setSupplierReturn(updatedReturn);
      setActionNotes("");

      // Close dialogs
      setShowApproveDialog(false);
      setShowRejectDialog(false);
      setShowCompleteDialog(false);

      toast.success(`Supplier return ${newStatus.toLowerCase()} successfully`);
    } catch (error) {
      console.error("Error updating supplier return:", error);
      toast.error(error instanceof Error ? error.message : "Failed to update supplier return");
    } finally {
      setActionLoading(false);
    }
  };

  if (loading) {
    return (
      <MainLayout>
        <div className="flex justify-center items-center min-h-[400px]">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        </div>
      </MainLayout>
    );
  }

  if (!supplierReturn) {
    return (
      <MainLayout>
        <div className="text-center py-8">
          <p className="text-muted-foreground">Supplier return not found</p>
        </div>
      </MainLayout>
    );
  }

  const canEdit = supplierReturn.status === "PENDING";
  const canApprove = supplierReturn.status === "PENDING";
  const canComplete = supplierReturn.status === "APPROVED";
  const canReject = supplierReturn.status === "PENDING";

  return (
    <MainLayout>
      <div className="space-y-6">
        <PageHeader
          title={`Supplier Return #${supplierReturn.id.slice(-8)}`}
          description="Supplier return details and management"
        >
          <div className="flex gap-2">
            <Link href="/inventory/returns/supplier-returns">
              <Button variant="outline">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Supplier Returns
              </Button>
            </Link>
            {canEdit && (
              <Link href={`/inventory/returns/supplier-returns/${supplierReturn.id}/edit`}>
                <Button variant="outline">
                  <Edit className="h-4 w-4 mr-2" />
                  Edit
                </Button>
              </Link>
            )}
          </div>
        </PageHeader>

        {/* Status and Action Buttons */}
        <Card>
          <CardHeader>
            <div className="flex justify-between items-center">
              <CardTitle className="flex items-center gap-2">
                <Truck className="h-5 w-5" />
                Return Status
              </CardTitle>
              <Badge variant="secondary" className={statusColors[supplierReturn.status]}>
                {statusIcons[supplierReturn.status]}
                <span className="ml-1">{supplierReturn.status}</span>
              </Badge>
            </div>
          </CardHeader>
          <CardContent>
            <div className="flex gap-2">
              {canApprove && (
                <Button
                  onClick={() => setShowApproveDialog(true)}
                  className="bg-blue-600 hover:bg-blue-700"
                >
                  <CheckCircle className="h-4 w-4 mr-2" />
                  Approve Return
                </Button>
              )}
              {canComplete && (
                <Button
                  onClick={() => setShowCompleteDialog(true)}
                  className="bg-green-600 hover:bg-green-700"
                >
                  <Package className="h-4 w-4 mr-2" />
                  Mark Complete
                </Button>
              )}
              {canReject && (
                <Button variant="destructive" onClick={() => setShowRejectDialog(true)}>
                  <XCircle className="h-4 w-4 mr-2" />
                  Reject Return
                </Button>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Return Information */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileText className="h-5 w-5" />
                Return Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label className="text-sm font-medium text-muted-foreground">Return ID</Label>
                <p className="font-mono text-sm">{supplierReturn.id}</p>
              </div>
              <div>
                <Label className="text-sm font-medium text-muted-foreground">Return Date</Label>
                <p className="flex items-center gap-2">
                  <Calendar className="h-4 w-4" />
                  {formatDate(supplierReturn.returnDate)}
                </p>
              </div>
              <div>
                <Label className="text-sm font-medium text-muted-foreground">Reason</Label>
                <p>{supplierReturn.reason}</p>
              </div>
              {supplierReturn.notes && (
                <div>
                  <Label className="text-sm font-medium text-muted-foreground">Notes</Label>
                  <p className="text-sm bg-muted p-2 rounded">{supplierReturn.notes}</p>
                </div>
              )}
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <User className="h-5 w-5" />
                Supplier & Purchase Order
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label className="text-sm font-medium text-muted-foreground">Supplier</Label>
                <p>{supplierReturn.supplier.name}</p>
              </div>
              {supplierReturn.purchaseOrder && (
                <div>
                  <Label className="text-sm font-medium text-muted-foreground">
                    Purchase Order
                  </Label>
                  <p className="font-mono text-sm">{supplierReturn.purchaseOrder.id}</p>
                  {supplierReturn.purchaseOrder.createdBy && (
                    <p className="text-sm text-muted-foreground">
                      Created by: {supplierReturn.purchaseOrder.createdBy.name}
                    </p>
                  )}
                </div>
              )}
              <div>
                <Label className="text-sm font-medium text-muted-foreground">Total Amount</Label>
                <p className="text-lg font-semibold">{formatCurrency(supplierReturn.total)}</p>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Return Items */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Package className="h-5 w-5" />
              Return Items ({supplierReturn.items.length})
            </CardTitle>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Product</TableHead>
                  <TableHead>SKU</TableHead>
                  <TableHead>Category</TableHead>
                  <TableHead>Quantity</TableHead>
                  <TableHead>Unit Price</TableHead>
                  <TableHead>Subtotal</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {supplierReturn.items.map((item) => (
                  <TableRow key={item.id}>
                    <TableCell>
                      <div>
                        <p className="font-medium">{item.product.name}</p>
                        {item.product.unit && (
                          <p className="text-sm text-muted-foreground">
                            Unit: {item.product.unit.name}
                          </p>
                        )}
                      </div>
                    </TableCell>
                    <TableCell className="font-mono text-sm">{item.product.sku}</TableCell>
                    <TableCell>{item.product.category?.name || "Uncategorized"}</TableCell>
                    <TableCell>{item.quantity}</TableCell>
                    <TableCell>{formatCurrency(item.unitPrice)}</TableCell>
                    <TableCell className="font-medium">{formatCurrency(item.subtotal)}</TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
            <div className="mt-4 pt-4 border-t">
              <div className="flex justify-end">
                <div className="text-right">
                  <p className="text-lg font-semibold">
                    Total: {formatCurrency(supplierReturn.total)}
                  </p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Approve Dialog */}
        <Dialog open={showApproveDialog} onOpenChange={setShowApproveDialog}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Approve Supplier Return</DialogTitle>
              <DialogDescription>
                This will approve the supplier return and reduce inventory quantities for the
                returned items.
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4">
              <div>
                <Label htmlFor="approve-notes">Notes (Optional)</Label>
                <Textarea
                  id="approve-notes"
                  placeholder="Add any notes about the approval..."
                  value={actionNotes}
                  onChange={(e) => setActionNotes(e.target.value)}
                />
              </div>
            </div>
            <DialogFooter>
              <Button
                variant="outline"
                onClick={() => setShowApproveDialog(false)}
                disabled={actionLoading}
              >
                Cancel
              </Button>
              <Button
                onClick={() => handleStatusUpdate("APPROVED")}
                disabled={actionLoading}
                className="bg-blue-600 hover:bg-blue-700"
              >
                {actionLoading ? "Approving..." : "Approve Return"}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        {/* Complete Dialog */}
        <Dialog open={showCompleteDialog} onOpenChange={setShowCompleteDialog}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Complete Supplier Return</DialogTitle>
              <DialogDescription>
                Mark this supplier return as completed. This action cannot be undone.
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4">
              <div>
                <Label htmlFor="complete-notes">Notes (Optional)</Label>
                <Textarea
                  id="complete-notes"
                  placeholder="Add any notes about the completion..."
                  value={actionNotes}
                  onChange={(e) => setActionNotes(e.target.value)}
                />
              </div>
            </div>
            <DialogFooter>
              <Button
                variant="outline"
                onClick={() => setShowCompleteDialog(false)}
                disabled={actionLoading}
              >
                Cancel
              </Button>
              <Button
                onClick={() => handleStatusUpdate("COMPLETED")}
                disabled={actionLoading}
                className="bg-green-600 hover:bg-green-700"
              >
                {actionLoading ? "Completing..." : "Mark Complete"}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        {/* Reject Dialog */}
        <Dialog open={showRejectDialog} onOpenChange={setShowRejectDialog}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Reject Supplier Return</DialogTitle>
              <DialogDescription>
                This will reject the supplier return. Please provide a reason for rejection.
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4">
              <div>
                <Label htmlFor="reject-notes">Rejection Reason *</Label>
                <Textarea
                  id="reject-notes"
                  placeholder="Please provide a reason for rejecting this return..."
                  value={actionNotes}
                  onChange={(e) => setActionNotes(e.target.value)}
                  required
                />
              </div>
            </div>
            <DialogFooter>
              <Button
                variant="outline"
                onClick={() => setShowRejectDialog(false)}
                disabled={actionLoading}
              >
                Cancel
              </Button>
              <Button
                variant="destructive"
                onClick={() => handleStatusUpdate("REJECTED")}
                disabled={actionLoading || !actionNotes.trim()}
              >
                {actionLoading ? "Rejecting..." : "Reject Return"}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>
    </MainLayout>
  );
}
