"use client";

import { useState, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { toast } from "sonner";
import { useRouter } from "next/navigation";

import { Badge } from "@/components/ui/badge";
import { Loader2, Plus } from "lucide-react";
import { MainLayout } from "@/components/layout/MainLayout";
import { PageHeader } from "@/components/layout/PageHeader";

interface Product {
  id: string;
  name: string;
  sku: string;
  storeStock?: {
    id: string;
    quantity: number;
  };
  warehouseStock?: {
    id: string;
    quantity: number;
  };
}

interface SimpleTransfer {
  id: string;
  date: string;
  product: {
    id: string;
    name: string;
    sku: string;
    category?: {
      name: string;
    };
    unit?: {
      name: string;
    };
  };
  quantity: number;
  fromStore: boolean;
  toStore: boolean;
  status: string;
  notes?: string;
  requestedBy: {
    name: string;
  };
  approvedBy?: {
    name: string;
  };
  approvedAt?: string;
  completedAt?: string;
}

export default function SimpleTransfersPage() {
  const router = useRouter();
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [products, setProducts] = useState<Product[]>([]);
  const [transfers, setTransfers] = useState<SimpleTransfer[]>([]);
  const [statusFilter, setStatusFilter] = useState("all");
  const [filteredTransfers, setFilteredTransfers] = useState<SimpleTransfer[]>([]);

  // Form state
  const [selectedProduct, setSelectedProduct] = useState("");
  const [fromStore, setFromStore] = useState(false);
  const [toStore, setToStore] = useState(true);
  const [quantity, setQuantity] = useState(1);
  const [notes, setNotes] = useState("");

  // Fetch products and transfers on page load
  useEffect(() => {
    fetchProducts();
    fetchTransfers();
  }, []);

  // Filter transfers when transfers array or status filter changes
  useEffect(() => {
    if (statusFilter === "all") {
      setFilteredTransfers(transfers);
    } else {
      const filtered = transfers.filter(
        (transfer) => transfer.status === statusFilter.toUpperCase()
      );
      setFilteredTransfers(filtered);
    }
  }, [transfers, statusFilter]);

  // Fetch products with stock information
  const fetchProducts = async () => {
    try {
      const response = await fetch("/api/products?includeStock=true");
      if (!response.ok) {
        throw new Error("Failed to fetch products");
      }
      const data = await response.json();
      setProducts(data.products);
    } catch (error) {
      console.error("Error fetching products:", error);
      toast.error("Failed to load products");
    }
  };

  // Fetch transfers
  const fetchTransfers = async () => {
    try {
      setIsLoading(true);
      const response = await fetch("/api/inventory/simple-transfers");
      if (!response.ok) {
        console.error("Failed to fetch transfers:", response.status, response.statusText);
        // Don't throw an error, just set empty transfers
        setTransfers([]);
        return;
      }
      const data = await response.json();
      if (data.transfers && Array.isArray(data.transfers)) {
        setTransfers(data.transfers);
      } else {
        console.warn("Unexpected response format:", data);
        setTransfers([]);
      }
    } catch (error) {
      console.error("Error fetching transfers:", error);
      // Don't show error toast since the API might not be fully implemented yet
      setTransfers([]);
    } finally {
      setIsLoading(false);
    }
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!selectedProduct) {
      toast.error("Please select a product");
      return;
    }

    if (quantity <= 0) {
      toast.error("Quantity must be greater than zero");
      return;
    }

    if (fromStore === toStore) {
      toast.error("Source and destination cannot be the same");
      return;
    }

    try {
      setIsLoading(true);

      // First check if the API endpoint is available
      try {
        const checkResponse = await fetch("/api/inventory/simple-transfers", {
          method: "HEAD",
        });

        if (!checkResponse.ok) {
          console.error("API endpoint not available:", checkResponse.status);
          toast.error("The API endpoint is not available yet. This is a UI prototype.");
          setIsLoading(false);
          return;
        }
      } catch (error) {
        console.error("Error checking API endpoint:", error);
        toast.error("The API endpoint is not available yet. This is a UI prototype.");
        setIsLoading(false);
        return;
      }

      const response = await fetch("/api/inventory/simple-transfers", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          productId: selectedProduct,
          quantity,
          fromStore,
          toStore,
          notes,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        console.error("API error response:", errorData);
        toast.error(errorData.error || "Failed to create transfer");
        return;
      }

      const data = await response.json();
      toast.success("Transfer request created successfully");

      // Reset form and close dialog
      setSelectedProduct("");
      setFromStore(false);
      setToStore(true);
      setQuantity(1);
      setNotes("");
      setIsCreateDialogOpen(false);

      // Refresh transfers list
      fetchTransfers();
    } catch (error) {
      console.error("Error creating transfer:", error);
      toast.error("Failed to create transfer. The API might not be fully implemented yet.");
    } finally {
      setIsLoading(false);
    }
  };

  // Get status badge color
  const getStatusBadge = (status: string) => {
    switch (status) {
      case "PENDING":
        return (
          <Badge variant="outline" className="bg-yellow-100 text-yellow-800">
            Pending
          </Badge>
        );
      case "APPROVED":
        return (
          <Badge variant="outline" className="bg-blue-100 text-blue-800">
            Approved
          </Badge>
        );
      case "COMPLETED":
        return (
          <Badge variant="outline" className="bg-green-100 text-green-800">
            Completed
          </Badge>
        );
      case "CANCELLED":
        return (
          <Badge variant="outline" className="bg-red-100 text-red-800">
            Cancelled
          </Badge>
        );
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  return (
    <MainLayout>
      <PageHeader title="Stock Transfers" />
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <div className="flex items-center space-x-4">
            <Label htmlFor="status-filter" className="text-sm font-medium">
              Status:
            </Label>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger id="status-filter" className="w-[180px]">
                <SelectValue placeholder="Filter by status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Transfers</SelectItem>
                <SelectItem value="pending">Pending</SelectItem>
                <SelectItem value="approved">Approved</SelectItem>
                <SelectItem value="completed">Completed</SelectItem>
                <SelectItem value="cancelled">Cancelled</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <Button onClick={() => setIsCreateDialogOpen(true)}>
            <Plus className="mr-2 h-4 w-4" /> New Transfer
          </Button>
        </div>

        <div className="space-y-4">
          <Card>
            <CardContent className="pt-6">
              {isLoading ? (
                <div className="flex justify-center items-center py-8">
                  <Loader2 className="h-8 w-8 animate-spin text-gray-500" />
                </div>
              ) : filteredTransfers.length === 0 ? (
                <p className="text-center py-8 text-gray-500">No transfers found</p>
              ) : (
                <div className="overflow-x-auto">
                  <table className="w-full">
                    <thead>
                      <tr className="border-b">
                        <th className="text-left py-3 px-4">Date</th>
                        <th className="text-left py-3 px-4">Product</th>
                        <th className="text-left py-3 px-4">From</th>
                        <th className="text-left py-3 px-4">To</th>
                        <th className="text-left py-3 px-4">Quantity</th>
                        <th className="text-left py-3 px-4">Status</th>
                        <th className="text-left py-3 px-4">Requested By</th>
                      </tr>
                    </thead>
                    <tbody>
                      {filteredTransfers.map((transfer) => (
                        <tr
                          key={transfer.id}
                          className="border-b hover:bg-gray-50 cursor-pointer"
                          onClick={() =>
                            router.push(`/inventory/stock/simple-transfers/${transfer.id}`)
                          }
                        >
                          <td className="py-3 px-4">
                            {new Date(transfer.date).toLocaleDateString()}
                          </td>
                          <td className="py-3 px-4">
                            {transfer.product.name} ({transfer.product.sku})
                          </td>
                          <td className="py-3 px-4">
                            {transfer.fromStore ? "Store" : "Warehouse"}
                          </td>
                          <td className="py-3 px-4">{transfer.toStore ? "Store" : "Warehouse"}</td>
                          <td className="py-3 px-4">{transfer.quantity}</td>
                          <td className="py-3 px-4">{getStatusBadge(transfer.status)}</td>
                          <td className="py-3 px-4">{transfer.requestedBy.name}</td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Create Transfer Dialog */}
        <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
          <DialogContent className="sm:max-w-[500px]">
            <DialogHeader>
              <DialogTitle>Create Stock Transfer</DialogTitle>
              <DialogDescription>
                Transfer inventory between locations. Enter the details below.
              </DialogDescription>
            </DialogHeader>

            <form onSubmit={handleSubmit}>
              <div className="grid gap-4 py-4">
                <div className="grid gap-2">
                  <Label htmlFor="product">Product</Label>
                  <Select value={selectedProduct} onValueChange={setSelectedProduct}>
                    <SelectTrigger id="product">
                      <SelectValue placeholder="Select a product" />
                    </SelectTrigger>
                    <SelectContent>
                      {products.map((product) => (
                        <SelectItem key={product.id} value={product.id}>
                          {product.name} ({product.sku})
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="grid gap-2">
                    <Label htmlFor="fromLocation">From</Label>
                    <Select
                      value={fromStore ? "store" : "warehouse"}
                      onValueChange={(value) => {
                        const isStore = value === "store";
                        setFromStore(isStore);
                        // Automatically set the destination to the opposite
                        setToStore(!isStore);
                      }}
                    >
                      <SelectTrigger id="fromLocation">
                        <SelectValue placeholder="Select location" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="store">Store</SelectItem>
                        <SelectItem value="warehouse">Warehouse</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="grid gap-2">
                    <Label htmlFor="toLocation">To</Label>
                    <Select
                      value={toStore ? "store" : "warehouse"}
                      onValueChange={(value) => {
                        const isStore = value === "store";
                        setToStore(isStore);
                        // Automatically set the source to the opposite
                        setFromStore(!isStore);
                      }}
                    >
                      <SelectTrigger id="toLocation">
                        <SelectValue placeholder="Select location" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="store">Store</SelectItem>
                        <SelectItem value="warehouse">Warehouse</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="grid gap-2">
                  <Label htmlFor="quantity">Quantity</Label>
                  <Input
                    id="quantity"
                    type="number"
                    min="1"
                    value={quantity}
                    onChange={(e) => setQuantity(Number(e.target.value))}
                  />
                </div>

                <div className="grid gap-2">
                  <Label htmlFor="notes">Notes (Optional)</Label>
                  <Textarea
                    id="notes"
                    value={notes}
                    onChange={(e) => setNotes(e.target.value)}
                    placeholder="Enter additional details about this transfer"
                  />
                </div>
              </div>

              <DialogFooter>
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setIsCreateDialogOpen(false)}
                >
                  Cancel
                </Button>
                <Button type="submit" disabled={isLoading}>
                  {isLoading ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : null}
                  Create Transfer
                </Button>
              </DialogFooter>
            </form>
          </DialogContent>
        </Dialog>
      </div>
    </MainLayout>
  );
}
