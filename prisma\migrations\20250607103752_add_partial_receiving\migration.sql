-- AlterEnum
ALTER TYPE "POStatus" ADD VALUE 'PARTIALLY_RECEIVED';

-- AlterTable
ALTER TABLE "PurchaseOrder" ADD COLUMN     "taxPercentage" DECIMAL(5,2);

-- AlterTable
ALTER TABLE "PurchaseOrderItem" ADD COLUMN     "receivedQuantity" DECIMAL(10,2) NOT NULL DEFAULT 0;

-- CreateTable
CREATE TABLE "PurchaseOrderReceiving" (
    "id" TEXT NOT NULL,
    "purchaseOrderId" TEXT NOT NULL,
    "receivedById" TEXT NOT NULL,
    "receivedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "notes" TEXT,
    "discrepancyReason" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "PurchaseOrderReceiving_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "PurchaseOrderReceivingItem" (
    "id" TEXT NOT NULL,
    "purchaseOrderReceivingId" TEXT NOT NULL,
    "purchaseOrderItemId" TEXT NOT NULL,
    "receivedQuantity" DECIMAL(10,2) NOT NULL,
    "discrepancyQuantity" DECIMAL(10,2) NOT NULL DEFAULT 0,
    "discrepancyReason" TEXT,
    "notes" TEXT,

    CONSTRAINT "PurchaseOrderReceivingItem_pkey" PRIMARY KEY ("id")
);

-- AddForeignKey
ALTER TABLE "PurchaseOrderReceiving" ADD CONSTRAINT "PurchaseOrderReceiving_purchaseOrderId_fkey" FOREIGN KEY ("purchaseOrderId") REFERENCES "PurchaseOrder"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "PurchaseOrderReceiving" ADD CONSTRAINT "PurchaseOrderReceiving_receivedById_fkey" FOREIGN KEY ("receivedById") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "PurchaseOrderReceivingItem" ADD CONSTRAINT "PurchaseOrderReceivingItem_purchaseOrderReceivingId_fkey" FOREIGN KEY ("purchaseOrderReceivingId") REFERENCES "PurchaseOrderReceiving"("id") ON DELETE CASCADE ON UPDATE CASCADE;
