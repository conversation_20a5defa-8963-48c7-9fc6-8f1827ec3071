import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/auth";

// Helper function to verify the authentication token
async function verifyAuthToken(request: NextRequest) {
  // Get the session token from cookies
  const token = request.cookies.get("session-token");

  // Check if token exists
  if (!token) {
    console.log("[API] /api/inventory/reports - No session token found");
    return {
      authenticated: false,
      error: "Unauthorized - No session token",
      status: 403,
      user: null
    };
  }

  // Verify the token
  try {
    const { payload } = await import("jose").then(({ jwtVerify }) =>
      jwtVerify(
        token.value,
        new TextEncoder().encode(process.env.NEXTAUTH_SECRET || "fallback-secret")
      )
    );

    return {
      authenticated: true,
      user: {
        id: payload.id as string,
        name: payload.name as string,
        email: payload.email as string,
        role: payload.role as string
      }
    };
  } catch (error) {
    console.error("[API] /api/inventory/reports - JWT verification failed:", error);
    return {
      authenticated: false,
      error: "Unauthorized - Invalid token",
      status: 403,
      user: null
    };
  }
}

// GET /api/inventory/reports - Get inventory reports
export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const auth = await verifyAuthToken(request);

    if (!auth.authenticated) {
      return NextResponse.json(
        { error: auth.error },
        { status: auth.status }
      );
    }

    // Check if user has permission to view reports
    const hasPermission = ["SUPER_ADMIN", "WAREHOUSE_ADMIN", "FINANCE_ADMIN"].includes(auth.user.role);
    if (!hasPermission) {
      return NextResponse.json(
        { error: "Unauthorized - Insufficient permissions" },
        { status: 403 }
      );
    }

    // Parse query parameters
    const searchParams = request.nextUrl.searchParams;
    const reportType = searchParams.get("type") || "summary";
    const categoryId = searchParams.get("categoryId");
    const startDate = searchParams.get("startDate");
    const endDate = searchParams.get("endDate");

    // Generate the requested report
    switch (reportType) {
      case "summary":
        return await generateSummaryReport(categoryId);
      case "valuation":
        return await generateValuationReport(categoryId);
      case "movement":
        return await generateMovementReport(startDate, endDate, categoryId);
      case "low_stock":
        return await generateLowStockReport(categoryId);
      default:
        return NextResponse.json(
          { error: "Invalid report type" },
          { status: 400 }
        );
    }
  } catch (error) {
    console.error("Error generating inventory report:", error);
    return NextResponse.json(
      { error: "Failed to generate inventory report", message: (error as Error).message },
      { status: 500 }
    );
  }
}

// Generate a summary report of current inventory levels
async function generateSummaryReport(categoryId: string | null) {
  // Build the query
  const query: any = {
    active: true
  };

  if (categoryId) {
    query.categoryId = categoryId;
  }

  // Get products with stock information
  const products = await prisma.product.findMany({
    where: query,
    include: {
      category: true,
      unit: true,
      storeStock: true,
      warehouseStock: true
    },
    orderBy: {
      name: "asc"
    }
  });

  // Get top and bottom selling products
  const salesData = await prisma.transactionItem.groupBy({
    by: ['productId'],
    _sum: {
      quantity: true,
      subtotal: true
    },
    orderBy: {
      _sum: {
        quantity: 'desc'
      }
    },
    take: 100 // Get enough to filter by category if needed
  });

  // Get product details for the sales data
  const productIds = salesData.map(item => item.productId);
  const productsWithSales = await prisma.product.findMany({
    where: {
      id: { in: productIds },
      ...(categoryId ? { categoryId } : {})
    },
    include: {
      category: true,
      unit: true
    }
  });

  // Map sales data to products
  const productSalesMap = new Map();
  salesData.forEach(item => {
    productSalesMap.set(item.productId, {
      unitsSold: Number(item._sum.quantity || 0),
      revenue: Number(item._sum.subtotal || 0)
    });
  });

  // Create top and bottom selling products arrays
  const productsWithSalesData = productsWithSales.map(product => ({
    id: product.id,
    name: product.name,
    sku: product.sku,
    category: product.category?.name || "Uncategorized",
    unitsSold: productSalesMap.get(product.id)?.unitsSold || 0,
    revenue: productSalesMap.get(product.id)?.revenue || 0
  }))
  .filter(product => product.unitsSold > 0); // Only include products with sales

  // Filter by category if needed
  const filteredProductsWithSales = categoryId
    ? productsWithSalesData.filter(p => {
        const matchingProduct = products.find(prod => prod.id === p.id);
        return matchingProduct && matchingProduct.category?.id === categoryId;
      })
    : productsWithSalesData;

  // Get top 5 and bottom 5 selling products
  const topSellingProducts = [...filteredProductsWithSales]
    .sort((a, b) => b.unitsSold - a.unitsSold)
    .slice(0, 5);

  const lowestSellingProducts = [...filteredProductsWithSales]
    .sort((a, b) => a.unitsSold - b.unitsSold)
    .slice(0, 5);

  // Calculate summary statistics
  const totalProducts = products.length;
  const totalStoreQuantity = products.reduce((sum, product) =>
    sum + (product.storeStock ? Number(product.storeStock.quantity) : 0), 0);
  const totalWarehouseQuantity = products.reduce((sum, product) =>
    sum + (product.warehouseStock ? Number(product.warehouseStock.quantity) : 0), 0);
  const lowStockCount = products.filter(product =>
    product.storeStock && Number(product.storeStock.quantity) <= Number(product.storeStock.minThreshold)).length;
  const outOfStockCount = products.filter(product =>
    !product.storeStock || Number(product.storeStock.quantity) <= 0).length;

  // Group by category
  const categoryGroups: any = {};
  products.forEach(product => {
    const categoryName = product.category?.name || "Uncategorized";
    if (!categoryGroups[categoryName]) {
      categoryGroups[categoryName] = {
        count: 0,
        storeQuantity: 0,
        warehouseQuantity: 0
      };
    }
    categoryGroups[categoryName].count++;
    categoryGroups[categoryName].storeQuantity += product.storeStock ? Number(product.storeStock.quantity) : 0;
    categoryGroups[categoryName].warehouseQuantity += product.warehouseStock ? Number(product.warehouseStock.quantity) : 0;
  });

  return NextResponse.json({
    reportType: "summary",
    generatedAt: new Date(),
    summary: {
      totalProducts,
      totalStoreQuantity,
      totalWarehouseQuantity,
      lowStockCount,
      outOfStockCount
    },
    categoryBreakdown: Object.entries(categoryGroups).map(([category, data]) => ({
      category,
      ...data
    })),
    products: products.map(product => ({
      id: product.id,
      name: product.name,
      sku: product.sku,
      category: product.category?.name || "Uncategorized",
      unit: product.unit.abbreviation,
      storeQuantity: product.storeStock ? Number(product.storeStock.quantity) : 0,
      warehouseQuantity: product.warehouseStock ? Number(product.warehouseStock.quantity) : 0,
      minThreshold: product.storeStock ? Number(product.storeStock.minThreshold) : 0
    })),
    topSellingProducts,
    lowestSellingProducts
  });
}

// Generate an inventory valuation report
async function generateValuationReport(categoryId: string | null) {
  // Build the query
  const query: any = {
    active: true
  };

  if (categoryId) {
    query.categoryId = categoryId;
  }

  // Get products with stock and price information
  const products = await prisma.product.findMany({
    where: query,
    include: {
      category: true,
      unit: true,
      storeStock: true,
      warehouseStock: true
    },
    orderBy: {
      name: "asc"
    }
  });

  // Calculate valuation
  let totalStoreValue = 0;
  let totalWarehouseValue = 0;
  let totalInventoryValue = 0;

  const productsWithValue = products.map(product => {
    const purchasePrice = Number(product.purchasePrice || product.basePrice);
    const storeQuantity = product.storeStock ? Number(product.storeStock.quantity) : 0;
    const warehouseQuantity = product.warehouseStock ? Number(product.warehouseStock.quantity) : 0;
    const storeValue = storeQuantity * purchasePrice;
    const warehouseValue = warehouseQuantity * purchasePrice;
    const totalValue = storeValue + warehouseValue;

    totalStoreValue += storeValue;
    totalWarehouseValue += warehouseValue;
    totalInventoryValue += totalValue;

    return {
      id: product.id,
      name: product.name,
      sku: product.sku,
      category: product.category?.name || "Uncategorized",
      unit: product.unit.abbreviation,
      purchasePrice,
      storeQuantity,
      warehouseQuantity,
      storeValue,
      warehouseValue,
      totalValue
    };
  });

  // Group by category
  const categoryValuation: any = {};
  productsWithValue.forEach(product => {
    const categoryName = product.category || "Uncategorized";
    if (!categoryValuation[categoryName]) {
      categoryValuation[categoryName] = {
        count: 0,
        storeValue: 0,
        warehouseValue: 0,
        totalValue: 0
      };
    }
    categoryValuation[categoryName].count++;
    categoryValuation[categoryName].storeValue += product.storeValue;
    categoryValuation[categoryName].warehouseValue += product.warehouseValue;
    categoryValuation[categoryName].totalValue += product.totalValue;
  });

  return NextResponse.json({
    reportType: "valuation",
    generatedAt: new Date(),
    summary: {
      totalProducts: products.length,
      totalStoreValue,
      totalWarehouseValue,
      totalInventoryValue
    },
    categoryBreakdown: Object.entries(categoryValuation).map(([category, data]) => ({
      category,
      ...data
    })),
    products: productsWithValue
  });
}

// Generate a stock movement report for a specific period
async function generateMovementReport(startDate: string | null, endDate: string | null, categoryId: string | null) {
  // Parse dates
  const start = startDate ? new Date(startDate) : new Date(new Date().setDate(new Date().getDate() - 30));
  const end = endDate ? new Date(endDate) : new Date();

  // Build the query for stock history
  const historyQuery: any = {
    date: {
      gte: start,
      lte: end
    }
  };

  if (categoryId) {
    historyQuery.product = {
      categoryId
    };
  }

  // Get stock history for the period
  const stockHistory = await prisma.stockHistory.findMany({
    where: historyQuery,
    include: {
      product: {
        include: {
          category: true,
          unit: true
        }
      },
      user: {
        select: {
          id: true,
          name: true,
          role: true
        }
      }
    },
    orderBy: {
      date: "desc"
    }
  });

  // Group by product
  const productMovements: any = {};
  stockHistory.forEach(history => {
    const productId = history.productId;
    if (!productMovements[productId]) {
      productMovements[productId] = {
        product: {
          id: history.product.id,
          name: history.product.name,
          sku: history.product.sku,
          category: history.product.category?.name || "Uncategorized",
          unit: history.product.unit.abbreviation
        },
        movements: [],
        totalIn: 0,
        totalOut: 0,
        netChange: 0
      };
    }

    const changeQuantity = Number(history.changeQuantity);
    productMovements[productId].movements.push({
      date: history.date,
      source: history.source,
      previousQuantity: Number(history.previousQuantity),
      newQuantity: Number(history.newQuantity),
      changeQuantity,
      notes: history.notes,
      user: history.user.name
    });

    if (changeQuantity > 0) {
      productMovements[productId].totalIn += changeQuantity;
    } else {
      productMovements[productId].totalOut += Math.abs(changeQuantity);
    }
    productMovements[productId].netChange += changeQuantity;
  });

  // Calculate summary statistics
  const totalIn = Object.values(productMovements).reduce((sum: any, product: any) => sum + product.totalIn, 0);
  const totalOut = Object.values(productMovements).reduce((sum: any, product: any) => sum + product.totalOut, 0);
  const netChange = totalIn - totalOut;

  // Create arrays for highest and lowest movement products
  const productsArray = Object.values(productMovements) as any[];

  // Add movement count to each product
  const productsWithMovementCount = productsArray.map(productData => ({
    name: productData.product.name,
    sku: productData.product.sku,
    category: productData.product.category,
    movementCount: productData.movements.length,
    netChange: productData.netChange
  }));

  // Get products with highest and lowest movement counts
  const highestMovementProducts = [...productsWithMovementCount]
    .sort((a, b) => b.movementCount - a.movementCount)
    .slice(0, 5);

  const lowestMovementProducts = [...productsWithMovementCount]
    .filter(p => p.movementCount > 0) // Only include products with at least one movement
    .sort((a, b) => a.movementCount - b.movementCount)
    .slice(0, 5);

  return NextResponse.json({
    reportType: "movement",
    generatedAt: new Date(),
    period: {
      startDate: start,
      endDate: end
    },
    summary: {
      totalProducts: Object.keys(productMovements).length,
      totalMovements: stockHistory.length,
      totalIn,
      totalOut,
      netChange
    },
    products: Object.values(productMovements),
    highestMovementProducts,
    lowestMovementProducts
  });
}

// Generate a low stock report
async function generateLowStockReport(categoryId: string | null) {
  // Build the query
  const query: any = {
    storeStock: {
      quantity: {
        lte: prisma.storeStock.fields.minThreshold
      }
    },
    active: true
  };

  if (categoryId) {
    query.categoryId = categoryId;
  }

  // Get products with low stock
  const lowStockProducts = await prisma.product.findMany({
    where: query,
    include: {
      category: true,
      unit: true,
      supplier: true,
      storeStock: true
    },
    orderBy: [
      {
        storeStock: {
          quantity: "asc"
        }
      },
      {
        name: "asc"
      }
    ]
  });

  // Calculate stock status for each product
  const productsWithStatus = lowStockProducts.map(product => {
    const storeStock = product.storeStock;
    let stockStatus = "NORMAL";
    let percentRemaining = 100;
    let daysUntilStockout = null;

    if (storeStock) {
      const quantity = Number(storeStock.quantity);
      const minThreshold = Number(storeStock.minThreshold);

      if (quantity <= 0) {
        stockStatus = "OUT_OF_STOCK";
        percentRemaining = 0;
      } else if (quantity <= minThreshold * 0.5) {
        stockStatus = "CRITICAL";
        percentRemaining = Math.round((quantity / minThreshold) * 100);
      } else if (quantity <= minThreshold) {
        stockStatus = "LOW";
        percentRemaining = Math.round((quantity / minThreshold) * 100);
      }
    }

    return {
      id: product.id,
      name: product.name,
      sku: product.sku,
      category: product.category?.name || "Uncategorized",
      unit: product.unit.abbreviation,
      supplier: product.supplier?.name,
      currentQuantity: product.storeStock ? Number(product.storeStock.quantity) : 0,
      minThreshold: product.storeStock ? Number(product.storeStock.minThreshold) : 0,
      stockStatus,
      percentRemaining,
      daysUntilStockout
    };
  });

  // Group by category
  const categoryGroups: any = {};
  productsWithStatus.forEach(product => {
    const categoryName = product.category || "Uncategorized";
    if (!categoryGroups[categoryName]) {
      categoryGroups[categoryName] = {
        count: 0,
        criticalCount: 0,
        outOfStockCount: 0
      };
    }
    categoryGroups[categoryName].count++;
    if (product.stockStatus === "CRITICAL") {
      categoryGroups[categoryName].criticalCount++;
    } else if (product.stockStatus === "OUT_OF_STOCK") {
      categoryGroups[categoryName].outOfStockCount++;
    }
  });

  return NextResponse.json({
    reportType: "low_stock",
    generatedAt: new Date(),
    summary: {
      totalLowStock: lowStockProducts.length,
      criticalCount: productsWithStatus.filter(p => p.stockStatus === "CRITICAL").length,
      outOfStockCount: productsWithStatus.filter(p => p.stockStatus === "OUT_OF_STOCK").length
    },
    categoryBreakdown: Object.entries(categoryGroups).map(([category, data]) => ({
      category,
      ...data
    })),
    products: productsWithStatus
  });
}
