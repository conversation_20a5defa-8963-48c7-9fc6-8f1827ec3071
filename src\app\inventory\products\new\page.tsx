"use client";

import { useState } from "react";
import { MainLayout } from "@/components/layout/MainLayout";
import { PageHeader } from "@/components/layout/PageHeader";
import { But<PERSON> } from "@/components/ui/button";
import { ProductForm, ProductFormValues } from "@/components/products/ProductForm";
import { ArrowLeft } from "lucide-react";
import { useRouter } from "next/navigation";
import Link from "next/link";

export default function NewProductPage() {
  const router = useRouter();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleSubmit = async (data: ProductFormValues) => {
    setIsSubmitting(true);
    setError(null);

    try {
      // Process data before submission
      const processedData = {
        ...data,
        // Ensure optional fields are properly handled
        description: data.description,
        // If barcode is empty or just whitespace, set it to null
        barcode: data.barcode && data.barcode.trim() !== "" ? data.barcode : null,
        imageUrl: data.imageUrl,
        purchasePrice: data.purchasePrice,
        optionalPrice1: data.optionalPrice1,
        optionalPrice2: data.optionalPrice2,
        expiryDate: data.expiryDate,
        // Ensure categoryId and supplierId are properly handled
        categoryId: data.categoryId === "none" ? null : data.categoryId,
        supplierId: data.supplierId === "none" ? null : data.supplierId,
      };

      console.log("Original form data:", JSON.stringify(data, null, 2));
      console.log("Processed data for submission:", JSON.stringify(processedData, null, 2));

      const response = await fetch("/api/products", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(processedData),
      });

      const responseData = await response.json();

      if (!response.ok) {
        console.error("Server response:", responseData);

        // Handle specific error types
        if (responseData.error === "Barcode already exists") {
          // Set field-specific error for barcode
          return {
            success: false,
            fieldError: {
              field: "barcode",
              message: "This barcode is already in use by another product",
            },
          };
        } else if (responseData.error === "SKU already exists") {
          // Set field-specific error for SKU
          return {
            success: false,
            fieldError: {
              field: "sku",
              message: "This SKU is already in use by another product",
            },
          };
        }

        let errorMessage = responseData.error || "Failed to create product";

        // Add more details if available
        if (responseData.message) {
          errorMessage += `: ${responseData.message}`;
        }

        // If there are validation issues, format them
        if (responseData.issues) {
          const issues = responseData.issues
            .map((issue: any) => `${issue.path.join(".")}: ${issue.message}`)
            .join(", ");
          errorMessage += ` (${issues})`;
        }

        throw new Error(errorMessage);
      }

      // Redirect to products page on success
      router.push("/inventory/products");
      return { success: true };
    } catch (error) {
      console.error("Error creating product:", error);
      setError((error as Error).message || "Failed to create product. Please try again.");
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <MainLayout>
      <PageHeader
        title="Add New Product"
        description="Create a new product in your inventory"
        actions={
          <Button variant="outline" asChild>
            <Link href="/inventory/products">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Products
            </Link>
          </Button>
        }
      />

      <div className="mt-6">
        <ProductForm onSubmit={handleSubmit} isSubmitting={isSubmitting} error={error} />
      </div>
    </MainLayout>
  );
}
