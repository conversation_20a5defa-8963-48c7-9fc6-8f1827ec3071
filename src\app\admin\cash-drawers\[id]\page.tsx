"use client";

import React, { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { MainLayout } from "@/components/layout/MainLayout";
import { PageHeader } from "@/components/layout/PageHeader";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { ArrowLeft, Loader2, Calendar, User, DollarSign } from "lucide-react";
import { toast } from "sonner";
import { formatCurrency, formatDate } from "@/lib/utils";
import Link from "next/link";
import { useAuth } from "@/hooks/use-auth";

interface DrawerSession {
  id: string;
  openingBalance: number;
  expectedClosingBalance?: number;
  actualClosingBalance?: number;
  discrepancy?: number;
  openedAt: string;
  closedAt?: string;
  status: "OPEN" | "CLOSED" | "RECONCILED";
  businessDate: string;
  user: {
    id: string;
    name: string;
  };
}

interface CashDrawer {
  id: string;
  name: string;
  location?: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  drawerSessions: DrawerSession[];
}

export default function DrawerDetailPage({ params }: { params: { id: string } }) {
  const router = useRouter();
  const { user } = useAuth();
  const [drawer, setDrawer] = useState<CashDrawer | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Unwrap params using React.use()
  const { id } = React.use(params);

  // Fetch drawer details
  const fetchDrawer = async () => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await fetch(`/api/cash-drawers/${id}`);
      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || "Failed to fetch cash drawer");
      }

      setDrawer(data.drawer);
    } catch (err) {
      setError(err instanceof Error ? err.message : "An unknown error occurred");
      console.error("Error fetching cash drawer:", err);
    } finally {
      setIsLoading(false);
    }
  };

  // Load drawer on mount
  useEffect(() => {
    fetchDrawer();
  }, [id]);

  // Get status badge variant
  const getStatusBadge = (status: string) => {
    switch (status) {
      case "OPEN":
        return <Badge variant="success">Open</Badge>;
      case "CLOSED":
        return <Badge variant="default">Closed</Badge>;
      case "RECONCILED":
        return <Badge variant="secondary">Reconciled</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  // View session details
  const handleViewSession = (sessionId: string) => {
    router.push(`/admin/drawer-sessions/${sessionId}`);
  };

  return (
    <MainLayout>
      <div className="flex items-center">
        <Button variant="outline" size="icon" className="mr-4" asChild>
          <Link href="/admin/cash-drawers">
            <ArrowLeft className="h-4 w-4" />
          </Link>
        </Button>
        <PageHeader
          heading={drawer ? `${drawer.name} Drawer` : "Cash Drawer"}
          subheading={drawer?.location ? `Location: ${drawer.location}` : "View drawer sessions"}
        />
      </div>

      {isLoading ? (
        <div className="flex justify-center items-center py-8">
          <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
        </div>
      ) : error ? (
        <div className="bg-destructive/10 text-destructive p-4 rounded-md mt-6">{error}</div>
      ) : drawer ? (
        <>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
            <Card>
              <CardHeader>
                <CardTitle>Drawer Status</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-center justify-between">
                  <span>Status:</span>
                  {drawer.isActive ? (
                    <Badge variant="success">Active</Badge>
                  ) : (
                    <Badge variant="secondary">Inactive</Badge>
                  )}
                </div>
                <div className="flex items-center justify-between mt-2">
                  <span>Created:</span>
                  <span>{formatDate(new Date(drawer.createdAt))}</span>
                </div>
                <div className="flex items-center justify-between mt-2">
                  <span>Last Updated:</span>
                  <span>{formatDate(new Date(drawer.updatedAt))}</span>
                </div>
                <div className="mt-4">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => router.push(`/admin/cash-drawers/${drawer.id}/edit`)}
                  >
                    Edit Drawer
                  </Button>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Session Statistics</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-center justify-between">
                  <span>Total Sessions:</span>
                  <span>{drawer.drawerSessions.length}</span>
                </div>
                <div className="flex items-center justify-between mt-2">
                  <span>Open Sessions:</span>
                  <span>
                    {drawer.drawerSessions.filter((session) => session.status === "OPEN").length}
                  </span>
                </div>
                <div className="flex items-center justify-between mt-2">
                  <span>Closed Sessions:</span>
                  <span>
                    {drawer.drawerSessions.filter((session) => session.status !== "OPEN").length}
                  </span>
                </div>
              </CardContent>
            </Card>
          </div>

          <Card className="mt-6">
            <CardHeader>
              <CardTitle>Drawer Sessions</CardTitle>
              <CardDescription>View all sessions for this drawer</CardDescription>
            </CardHeader>
            <CardContent>
              {drawer.drawerSessions.length === 0 ? (
                <div className="text-center py-8 text-muted-foreground">
                  No sessions found for this drawer.
                </div>
              ) : (
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Business Date</TableHead>
                      <TableHead>Cashier</TableHead>
                      <TableHead>Opening Balance</TableHead>
                      <TableHead>Closing Balance</TableHead>
                      <TableHead>Discrepancy</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Opened At</TableHead>
                      <TableHead>Closed At</TableHead>
                      <TableHead className="text-right">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {drawer.drawerSessions.map((session) => (
                      <TableRow key={session.id}>
                        <TableCell>{formatDate(new Date(session.businessDate))}</TableCell>
                        <TableCell>{session.user.name}</TableCell>
                        <TableCell>{formatCurrency(session.openingBalance)}</TableCell>
                        <TableCell>
                          {session.actualClosingBalance
                            ? formatCurrency(session.actualClosingBalance)
                            : "-"}
                        </TableCell>
                        <TableCell>
                          {session.discrepancy !== null && session.discrepancy !== undefined ? (
                            <span
                              className={
                                session.discrepancy > 0
                                  ? "text-green-600"
                                  : session.discrepancy < 0
                                    ? "text-red-600"
                                    : ""
                              }
                            >
                              {session.discrepancy > 0 ? "+" : ""}
                              {formatCurrency(session.discrepancy)}
                            </span>
                          ) : (
                            "-"
                          )}
                        </TableCell>
                        <TableCell>{getStatusBadge(session.status)}</TableCell>
                        <TableCell>{formatDate(new Date(session.openedAt), true)}</TableCell>
                        <TableCell>
                          {session.closedAt ? formatDate(new Date(session.closedAt), true) : "-"}
                        </TableCell>
                        <TableCell className="text-right">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleViewSession(session.id)}
                          >
                            View Details
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              )}
            </CardContent>
          </Card>
        </>
      ) : (
        <div className="text-center py-8 text-muted-foreground">Cash drawer not found.</div>
      )}
    </MainLayout>
  );
}
