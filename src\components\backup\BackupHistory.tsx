"use client";

import { useState, useEffect } from "react";
import { formatDistanceToNow, format } from "date-fns";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { BackupHistoryEntry } from "@/lib/backup/backup-history";
import {
  Pagination,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination";

export default function BackupHistory() {
  const [history, setHistory] = useState<BackupHistoryEntry[]>([]);
  const [lastBackup, setLastBackup] = useState<BackupHistoryEntry | null>(null);
  const [lastRestore, setLastRestore] = useState<BackupHistoryEntry | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 15; // Limit to 15 items per page

  // Fetch backup history
  const fetchHistory = async () => {
    setLoading(true);
    try {
      const response = await fetch("/api/backup/history");
      const data = await response.json();

      if (data.error) {
        throw new Error(data.message || "Failed to fetch backup history");
      }

      setHistory(data.history || []);
      setLastBackup(data.lastBackup || null);
      setLastRestore(data.lastRestore || null);
      setError(null);
    } catch (err) {
      console.error("Error fetching backup history:", err);
      setError((err as Error).message || "Failed to fetch backup history");
    } finally {
      setLoading(false);
    }
  };

  // Load history on component mount
  useEffect(() => {
    fetchHistory();

    // Refresh history every 30 seconds
    const intervalId = setInterval(fetchHistory, 100000);

    // Clean up interval on unmount
    return () => clearInterval(intervalId);
  }, []);

  // Format date for display
  const formatDate = (dateString: string) => {
    try {
      const date = new Date(dateString);
      return format(date, "yyyy-MM-dd HH:mm:ss");
    } catch (err) {
      return dateString;
    }
  };

  // Get status badge class
  const getStatusBadge = (success: boolean) => {
    return success
      ? "inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800"
      : "inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800";
  };

  // Get method badge class
  const getMethodBadge = (method: string) => {
    switch (method) {
      case "pg_dump":
        return "inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800";
      case "prisma":
        return "inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800";
      case "rotation":
        return "inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800";
      case "scheduled":
        return "inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800";
      case "manual":
        return "inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-indigo-100 text-indigo-800";
      case "download":
        return "inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800";
      case "upload":
        return "inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-orange-100 text-orange-800";
      default:
        return "inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800";
    }
  };

  // Pagination helpers
  const totalPages = Math.ceil(history.length / itemsPerPage);

  // Get paginated data
  const getPaginatedData = () => {
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    return history.slice(startIndex, endIndex);
  };

  // Handle page change
  const handlePageChange = (page: number) => {
    if (page < 1 || page > totalPages) return;
    setCurrentPage(page);
  };

  // Generate page numbers for pagination
  const getPageNumbers = () => {
    const pageNumbers = [];
    const maxPagesToShow = 5; // Show at most 5 page numbers

    if (totalPages <= maxPagesToShow) {
      // If we have 5 or fewer pages, show all of them
      for (let i = 1; i <= totalPages; i++) {
        pageNumbers.push(i);
      }
    } else {
      // Always show first page
      pageNumbers.push(1);

      // Calculate start and end of the middle section
      let startPage = Math.max(2, currentPage - 1);
      let endPage = Math.min(totalPages - 1, currentPage + 1);

      // Adjust if we're near the beginning or end
      if (currentPage <= 2) {
        endPage = 3;
      } else if (currentPage >= totalPages - 1) {
        startPage = totalPages - 2;
      }

      // Add ellipsis after first page if needed
      if (startPage > 2) {
        pageNumbers.push(-1); // -1 represents ellipsis
      }

      // Add middle pages
      for (let i = startPage; i <= endPage; i++) {
        pageNumbers.push(i);
      }

      // Add ellipsis before last page if needed
      if (endPage < totalPages - 1) {
        pageNumbers.push(-2); // -2 represents ellipsis
      }

      // Always show last page
      pageNumbers.push(totalPages);
    }

    return pageNumbers;
  };

  return (
    <div className="mt-8">
      <h2 className="text-xl font-semibold mb-4">Backup History</h2>

      {error && (
        <Alert variant="destructive" className="mb-4">
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
        {lastBackup && (
          <div className="bg-white p-4 rounded-lg shadow">
            <h3 className="text-md font-medium mb-2">Last Backup</h3>
            <p className="text-sm text-gray-600 mb-1">
              <span className="font-medium">Date:</span> {formatDate(lastBackup.timestamp)}
            </p>
            <p className="text-sm text-gray-600 mb-1">
              <span className="font-medium">File:</span> {lastBackup.fileName}
            </p>
            <p className="text-sm text-gray-600 mb-1">
              <span className="font-medium">Method:</span>{" "}
              <span className={getMethodBadge(lastBackup.method)}>{lastBackup.method}</span>
            </p>
            <p className="text-sm text-gray-600">
              <span className="font-medium">Status:</span>{" "}
              <span className={getStatusBadge(lastBackup.success)}>
                {lastBackup.success ? "Success" : "Failed"}
              </span>
            </p>
          </div>
        )}

        {lastRestore && (
          <div className="bg-white p-4 rounded-lg shadow">
            <h3 className="text-md font-medium mb-2">Last Restore</h3>
            <p className="text-sm text-gray-600 mb-1">
              <span className="font-medium">Date:</span> {formatDate(lastRestore.timestamp)}
            </p>
            <p className="text-sm text-gray-600 mb-1">
              <span className="font-medium">File:</span> {lastRestore.fileName}
            </p>
            <p className="text-sm text-gray-600 mb-1">
              <span className="font-medium">Method:</span>{" "}
              <span className={getMethodBadge(lastRestore.method)}>{lastRestore.method}</span>
            </p>
            <p className="text-sm text-gray-600">
              <span className="font-medium">Status:</span>{" "}
              <span className={getStatusBadge(lastRestore.success)}>
                {lastRestore.success ? "Success" : "Failed"}
              </span>
            </p>
          </div>
        )}
      </div>

      {/* Mobile view for history */}
      <div className="md:hidden">
        {loading && <p className="text-center py-4">Loading history...</p>}

        {!loading && history.length === 0 && <p className="text-center py-4">No history found.</p>}

        {!loading &&
          getPaginatedData().map((entry, index) => (
            <div key={index} className="mb-3 p-3 border rounded-lg bg-white shadow-sm">
              <div className="flex justify-between items-center mb-2">
                <span className={getMethodBadge(entry.method)}>{entry.method}</span>
                <span className={getStatusBadge(entry.success)}>
                  {entry.success ? "Success" : "Failed"}
                </span>
              </div>
              <div className="grid grid-cols-2 gap-2 text-xs">
                <div>
                  <span className="text-gray-500">Date:</span>
                  <br />
                  {formatDate(entry.timestamp)}
                </div>
                <div>
                  <span className="text-gray-500">Type:</span>
                  <br />
                  <span className="capitalize">{entry.type}</span>
                </div>
                <div className="col-span-2">
                  <span className="text-gray-500">File:</span>
                  <br />
                  {entry.fileName}
                </div>
              </div>
            </div>
          ))}

        {/* Mobile Pagination */}
        {!loading && history.length > 0 && (
          <div className="mt-4">
            <Pagination>
              <PaginationContent>
                <PaginationItem>
                  <PaginationPrevious
                    onClick={() => handlePageChange(currentPage - 1)}
                    className={currentPage === 1 ? "pointer-events-none opacity-50" : ""}
                  />
                </PaginationItem>

                {getPageNumbers().map((pageNum, i) => (
                  <PaginationItem key={i}>
                    {pageNum === -1 || pageNum === -2 ? (
                      <PaginationEllipsis />
                    ) : (
                      <PaginationLink
                        isActive={pageNum === currentPage}
                        onClick={() => handlePageChange(pageNum)}
                      >
                        {pageNum}
                      </PaginationLink>
                    )}
                  </PaginationItem>
                ))}

                <PaginationItem>
                  <PaginationNext
                    onClick={() => handlePageChange(currentPage + 1)}
                    className={currentPage === totalPages ? "pointer-events-none opacity-50" : ""}
                  />
                </PaginationItem>
              </PaginationContent>
            </Pagination>
          </div>
        )}
      </div>

      {/* Desktop view (show table) - hide on mobile */}
      <div className="hidden md:block">
        <div className="bg-white rounded-lg shadow overflow-hidden">
          <div className="px-4 py-5 sm:px-6 bg-gray-50">
            <h3 className="text-lg font-medium leading-6 text-gray-900">Recent Operations</h3>
            <p className="mt-1 max-w-2xl text-sm text-gray-500">
              A history of recent backup and restore operations.
            </p>
          </div>

          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Date
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Type
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Method
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    File
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {loading && (
                  <tr>
                    <td colSpan={5} className="px-6 py-4 text-center">
                      Loading history...
                    </td>
                  </tr>
                )}

                {!loading && history.length === 0 && (
                  <tr>
                    <td colSpan={5} className="px-6 py-4 text-center">
                      No history found.
                    </td>
                  </tr>
                )}

                {!loading &&
                  getPaginatedData().map((entry, index) => (
                    <tr key={index} className={index % 2 === 0 ? "bg-white" : "bg-gray-50"}>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {formatDate(entry.timestamp)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        <span className="capitalize">{entry.type}</span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        <span className={getMethodBadge(entry.method)}>{entry.method}</span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {entry.fileName}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        <span className={getStatusBadge(entry.success)}>
                          {entry.success ? "Success" : "Failed"}
                        </span>
                      </td>
                    </tr>
                  ))}
              </tbody>
            </table>
          </div>

          {/* Desktop Pagination */}
          {!loading && history.length > 0 && (
            <div className="px-6 py-4 bg-gray-50 flex justify-between items-center">
              <div className="text-sm text-gray-500">
                Showing {(currentPage - 1) * itemsPerPage + 1} to{" "}
                {Math.min(currentPage * itemsPerPage, history.length)} of {history.length} entries
              </div>

              <Pagination>
                <PaginationContent>
                  <PaginationItem>
                    <PaginationPrevious
                      onClick={() => handlePageChange(currentPage - 1)}
                      className={currentPage === 1 ? "pointer-events-none opacity-50" : ""}
                    />
                  </PaginationItem>

                  {getPageNumbers().map((pageNum, i) => (
                    <PaginationItem key={i}>
                      {pageNum === -1 || pageNum === -2 ? (
                        <PaginationEllipsis />
                      ) : (
                        <PaginationLink
                          isActive={pageNum === currentPage}
                          onClick={() => handlePageChange(pageNum)}
                        >
                          {pageNum}
                        </PaginationLink>
                      )}
                    </PaginationItem>
                  ))}

                  <PaginationItem>
                    <PaginationNext
                      onClick={() => handlePageChange(currentPage + 1)}
                      className={currentPage === totalPages ? "pointer-events-none opacity-50" : ""}
                    />
                  </PaginationItem>
                </PaginationContent>
              </Pagination>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
