"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { MainLayout } from "@/components/layout/MainLayout";
import { PageHeader } from "@/components/layout/PageHeader";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { ArrowLeft, Plus, Trash2, Search, Package, User, FileText } from "lucide-react";
import Link from "next/link";
import { toast } from "sonner";
import { z } from "zod";

interface Supplier {
  id: string;
  name: string;
}

interface PurchaseOrder {
  id: string;
  supplierId: string;
  status: string;
  total: number;
  createdAt: string;
  supplier: {
    name: string;
  };
  items: PurchaseOrderItem[];
}

interface PurchaseOrderItem {
  id: string;
  productId: string;
  quantity: number;
  unitPrice: number;
  subtotal: number;
  product: {
    id: string;
    name: string;
    sku: string;
    category?: {
      name: string;
    };
    unit?: {
      name: string;
    };
  };
}

interface ReturnItem {
  productId: string;
  quantity: number;
  unitPrice: number;
  subtotal: number;
  product: {
    id: string;
    name: string;
    sku: string;
  };
}

const createSupplierReturnSchema = z.object({
  purchaseOrderId: z.string().min(1, "Purchase order is required"),
  supplierId: z.string().min(1, "Supplier is required"),
  reason: z.string().min(1, "Reason is required"),
  notes: z.string().optional(),
  items: z
    .array(
      z.object({
        productId: z.string(),
        quantity: z.number().positive("Quantity must be greater than 0"),
        unitPrice: z.number().positive("Unit price must be greater than 0"),
        subtotal: z.number().positive("Subtotal must be greater than 0"),
      })
    )
    .min(1, "At least one item is required"),
});

export default function NewSupplierReturnPage() {
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [suppliers, setSuppliers] = useState<Supplier[]>([]);
  const [purchaseOrders, setPurchaseOrders] = useState<PurchaseOrder[]>([]);
  const [selectedPurchaseOrder, setSelectedPurchaseOrder] = useState<PurchaseOrder | null>(null);
  const [returnItems, setReturnItems] = useState<ReturnItem[]>([]);

  // Form data
  const [formData, setFormData] = useState({
    purchaseOrderId: "",
    supplierId: "",
    reason: "",
    notes: "",
  });

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("id-ID", {
      style: "currency",
      currency: "IDR",
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  };

  // Fetch suppliers
  const fetchSuppliers = async () => {
    try {
      const response = await fetch("/api/suppliers");
      if (!response.ok) throw new Error("Failed to fetch suppliers");
      const data = await response.json();
      setSuppliers(data.suppliers || []);
    } catch (error) {
      console.error("Error fetching suppliers:", error);
      toast.error("Failed to load suppliers");
    }
  };

  // Fetch purchase orders for selected supplier
  const fetchPurchaseOrders = async (supplierId: string) => {
    try {
      const response = await fetch(`/api/purchase-orders?supplierId=${supplierId}&status=RECEIVED`);
      if (!response.ok) throw new Error("Failed to fetch purchase orders");
      const data = await response.json();
      setPurchaseOrders(data.purchaseOrders || []);
    } catch (error) {
      console.error("Error fetching purchase orders:", error);
      toast.error("Failed to load purchase orders");
    }
  };

  // Fetch purchase order details
  const fetchPurchaseOrderDetails = async (purchaseOrderId: string) => {
    try {
      const response = await fetch(`/api/purchase-orders/${purchaseOrderId}`);
      if (!response.ok) throw new Error("Failed to fetch purchase order details");
      const data = await response.json();
      setSelectedPurchaseOrder(data);

      // Initialize return items with zero quantities
      const initialItems: ReturnItem[] = data.items.map((item: PurchaseOrderItem) => ({
        productId: item.productId,
        quantity: 0,
        unitPrice: item.unitPrice,
        subtotal: 0,
        product: item.product,
      }));
      setReturnItems(initialItems);
    } catch (error) {
      console.error("Error fetching purchase order details:", error);
      toast.error("Failed to load purchase order details");
    }
  };

  useEffect(() => {
    fetchSuppliers();
  }, []);

  useEffect(() => {
    if (formData.supplierId) {
      fetchPurchaseOrders(formData.supplierId);
      setFormData((prev) => ({ ...prev, purchaseOrderId: "" }));
      setSelectedPurchaseOrder(null);
      setReturnItems([]);
    }
  }, [formData.supplierId]);

  useEffect(() => {
    if (formData.purchaseOrderId) {
      fetchPurchaseOrderDetails(formData.purchaseOrderId);
    }
  }, [formData.purchaseOrderId]);

  const handleItemQuantityChange = (productId: string, quantity: number) => {
    setReturnItems((prev) =>
      prev.map((item) => {
        if (item.productId === productId) {
          const newQuantity = Math.max(0, quantity);
          return {
            ...item,
            quantity: newQuantity,
            subtotal: newQuantity * item.unitPrice,
          };
        }
        return item;
      })
    );
  };

  const calculateTotal = () => {
    return returnItems.reduce((sum, item) => sum + item.subtotal, 0);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    const itemsToReturn = returnItems.filter((item) => item.quantity > 0);

    if (itemsToReturn.length === 0) {
      toast.error("Please select at least one item to return");
      return;
    }

    const returnData = {
      ...formData,
      items: itemsToReturn,
    };

    try {
      setLoading(true);

      // Validate data
      createSupplierReturnSchema.parse(returnData);

      const response = await fetch("/api/supplier-returns", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(returnData),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || "Failed to create supplier return");
      }

      const newReturn = await response.json();
      toast.success("Supplier return created successfully");
      router.push(`/inventory/returns/supplier-returns/${newReturn.id}`);
    } catch (error) {
      console.error("Error creating supplier return:", error);
      if (error instanceof z.ZodError) {
        toast.error("Please fill in all required fields correctly");
      } else {
        toast.error(error instanceof Error ? error.message : "Failed to create supplier return");
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <MainLayout>
      <div className="space-y-6">
        <PageHeader title="Create New Supplier Return" description="Return items to supplier">
          <Link href="/inventory/returns/supplier-returns">
            <Button variant="outline">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Supplier Returns
            </Button>
          </Link>
        </PageHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Return Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileText className="h-5 w-5" />
                Return Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="supplier">Supplier *</Label>
                  <Select
                    value={formData.supplierId}
                    onValueChange={(value) =>
                      setFormData((prev) => ({ ...prev, supplierId: value }))
                    }
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select supplier" />
                    </SelectTrigger>
                    <SelectContent>
                      {suppliers.map((supplier) => (
                        <SelectItem key={supplier.id} value={supplier.id}>
                          {supplier.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label htmlFor="purchaseOrder">Purchase Order *</Label>
                  <Select
                    value={formData.purchaseOrderId}
                    onValueChange={(value) =>
                      setFormData((prev) => ({ ...prev, purchaseOrderId: value }))
                    }
                    disabled={!formData.supplierId}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select purchase order" />
                    </SelectTrigger>
                    <SelectContent>
                      {purchaseOrders.map((po) => (
                        <SelectItem key={po.id} value={po.id}>
                          {po.id.slice(-8)} - {formatCurrency(po.total)} ({formatDate(po.createdAt)}
                          )
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div>
                <Label htmlFor="reason">Return Reason *</Label>
                <Input
                  id="reason"
                  value={formData.reason}
                  onChange={(e) => setFormData((prev) => ({ ...prev, reason: e.target.value }))}
                  placeholder="Enter reason for return"
                  required
                />
              </div>

              <div>
                <Label htmlFor="notes">Notes (Optional)</Label>
                <Textarea
                  id="notes"
                  value={formData.notes}
                  onChange={(e) => setFormData((prev) => ({ ...prev, notes: e.target.value }))}
                  placeholder="Additional notes about the return"
                  rows={3}
                />
              </div>
            </CardContent>
          </Card>

          {/* Return Items */}
          {selectedPurchaseOrder && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Package className="h-5 w-5" />
                  Select Items to Return
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="text-sm text-muted-foreground">
                    Purchase Order: <span className="font-mono">{selectedPurchaseOrder.id}</span>
                  </div>

                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Product</TableHead>
                        <TableHead>SKU</TableHead>
                        <TableHead>Category</TableHead>
                        <TableHead>Unit Price</TableHead>
                        <TableHead>Return Qty</TableHead>
                        <TableHead>Subtotal</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {returnItems.map((item) => (
                        <TableRow key={item.productId}>
                          <TableCell>
                            <div>
                              <p className="font-medium">{item.product.name}</p>
                              {item.product.unit && (
                                <p className="text-sm text-muted-foreground">
                                  Unit: {item.product.unit.name}
                                </p>
                              )}
                            </div>
                          </TableCell>
                          <TableCell className="font-mono text-sm">{item.product.sku}</TableCell>
                          <TableCell>{item.product.category?.name || "Uncategorized"}</TableCell>
                          <TableCell>{formatCurrency(item.unitPrice)}</TableCell>
                          <TableCell>
                            <Input
                              type="number"
                              min="0"
                              value={item.quantity}
                              onChange={(e) =>
                                handleItemQuantityChange(
                                  item.productId,
                                  parseInt(e.target.value) || 0
                                )
                              }
                              className="w-20"
                            />
                          </TableCell>
                          <TableCell className="font-medium">
                            {formatCurrency(item.subtotal)}
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>

                  <div className="flex justify-between items-center pt-4 border-t">
                    <div className="text-sm text-muted-foreground">
                      Items to return: {returnItems.filter((item) => item.quantity > 0).length}
                    </div>
                    <div className="text-right">
                      <p className="text-lg font-semibold">
                        Total: {formatCurrency(calculateTotal())}
                      </p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Submit Button */}
          <div className="flex justify-end gap-2">
            <Link href="/inventory/returns/supplier-returns">
              <Button variant="outline" disabled={loading}>
                Cancel
              </Button>
            </Link>
            <Button
              type="submit"
              disabled={
                loading ||
                !selectedPurchaseOrder ||
                returnItems.filter((item) => item.quantity > 0).length === 0
              }
            >
              {loading ? "Creating..." : "Create Supplier Return"}
            </Button>
          </div>
        </form>
      </div>
    </MainLayout>
  );
}
