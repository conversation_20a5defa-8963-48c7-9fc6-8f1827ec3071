import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/auth";
import { verifyAuthToken } from "@/lib/auth-utils";

import { z } from "zod";

// Transaction item schema for validation
const transactionItemSchema = z.object({
  productId: z.string(),
  quantity: z.number().positive(),
  unitPrice: z.number().positive(),
  discount: z.number().min(0).default(0),
  subtotal: z.number().positive(),
});

// Transaction schema for validation
const transactionSchema = z.object({
  customerId: z.string().optional(),
  subtotal: z.number().positive(),
  discount: z.number().min(0).default(0),
  tax: z.number().min(0).default(0),
  total: z.number().positive(),
  paymentMethod: z.enum(["CASH", "DEBIT", "QRIS"]),
  paymentStatus: z.enum(["PENDING", "PAID", "PARTIAL", "CANCELLED"]).default("PENDING"),
  cashReceived: z.number().optional(),
  changeAmount: z.number().optional(),
  dueDate: z.string().optional(),
  notes: z.string().optional(),
  terminalId: z.string().optional(),
  items: z.array(transactionItemSchema).min(1, { message: "At least one item is required" }),
})
.refine(
  (data) => {
    // If payment method is CASH and status is PAID, cashReceived must be provided and >= total
    if (data.paymentMethod === "CASH" && data.paymentStatus === "PAID") {
      return data.cashReceived !== undefined && data.cashReceived >= data.total;
    }
    return true;
  },
  {
    message: "Cash received must be greater than or equal to the total amount for cash payments",
    path: ["cashReceived"],
  }
);

// GET /api/transactions - Get all transactions with optional filtering
export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const auth = await verifyAuthToken(request);

    if (!auth.authenticated) {
      return NextResponse.json(
        { error: auth.error },
        { status: auth.status }
      );
    }

    // Get query parameters
    const url = new URL(request.url);
    const startDate = url.searchParams.get("startDate");
    const endDate = url.searchParams.get("endDate");
    const customerId = url.searchParams.get("customerId");
    const cashierId = url.searchParams.get("cashierId");
    const paymentMethod = url.searchParams.get("paymentMethod");
    const paymentStatus = url.searchParams.get("paymentStatus");
    const status = url.searchParams.get("status");
    const search = url.searchParams.get("search");
    const page = parseInt(url.searchParams.get("page") || "1");
    const limit = parseInt(url.searchParams.get("limit") || "10");
    const skip = (page - 1) * limit;

    // Build filter conditions
    const where: any = {};

    // Process date range with proper timezone handling
    if (startDate || endDate) {
      const { processDateRange } = await import("@/lib/utils");
      const { start, end } = processDateRange(startDate, endDate);

      where.transactionDate = {};

      if (start) {
        where.transactionDate.gte = start;
      }

      if (end) {
        where.transactionDate.lte = end;
      }

      // Log the processed dates for debugging
      console.log("Date filter applied:", {
        startDate: startDate ? new Date(startDate).toISOString() : null,
        endDate: endDate ? new Date(endDate).toISOString() : null,
        processedStart: start?.toISOString(),
        processedEnd: end?.toISOString(),
      });
    }

    if (customerId) {
      where.customerId = customerId;
    }

    if (cashierId) {
      where.cashierId = cashierId;
    }

    if (paymentMethod) {
      where.paymentMethod = paymentMethod;
    }

    if (paymentStatus) {
      where.paymentStatus = paymentStatus;
    }

    if (status) {
      where.status = status;
    }

    // Add search functionality
    if (search) {
      where.OR = [
        // Exact transaction ID match (highest priority)
        { id: { equals: search } },
        // Partial transaction ID match
        { id: { contains: search, mode: 'insensitive' } },
        // Customer name search
        { customer: { name: { contains: search, mode: 'insensitive' } } },
        // Customer email search
        { customer: { email: { contains: search, mode: 'insensitive' } } },
        // Cashier name search
        { cashier: { name: { contains: search, mode: 'insensitive' } } },
      ];
    }

    // Get transactions with pagination
    const [transactions, totalCount] = await Promise.all([
      prisma.transaction.findMany({
        where,
        include: {
          customer: true,
          cashier: {
            select: {
              id: true,
              name: true,
            },
          },
          items: {
            include: {
              product: {
                select: {
                  id: true,
                  name: true,
                  sku: true,
                  unit: true,
                },
              },
            },
          },
        },
        orderBy: {
          transactionDate: "desc",
        },
        skip,
        take: limit,
      }),
      prisma.transaction.count({ where }),
    ]);

    return NextResponse.json({
      transactions,
      pagination: {
        total: totalCount,
        page,
        limit,
        totalPages: Math.ceil(totalCount / limit),
      },
    });
  } catch (error) {
    console.error("Error fetching transactions:", error);
    return NextResponse.json(
      { error: "Failed to fetch transactions", message: (error as Error).message },
      { status: 500 }
    );
  }
}

// POST /api/transactions - Create a new transaction
export async function POST(request: NextRequest) {
  try {
    // Basic authentication for transaction creation
    const auth = await verifyAuthToken(request);

    if (!auth.authenticated) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      );
    }

    // Log auth information for debugging
    console.log("Auth user information:", auth.user);

    // Only cashiers can create transactions
    if (auth.user!.role !== "CASHIER") {
      console.error("Permission denied for user role:", auth.user!.role);
      return NextResponse.json(
        {
          error: "Unauthorized - Only cashiers can create transactions",
          details: {
            userRole: auth.user!.role,
            requiredRole: "CASHIER"
          }
        },
        { status: 403 }
      );
    }

    // Check if the cashier has an active drawer session
    const activeDrawerSession = await prisma.drawerSession.findFirst({
      where: {
        userId: auth.user!.id,
        status: "OPEN",
      },
    });

    if (!activeDrawerSession) {
      return NextResponse.json(
        {
          error: "No active drawer session",
          details: "You must open a cash drawer session before creating transactions"
        },
        { status: 400 }
      );
    }

    // Parse and validate request body
    const data = await request.json();
    console.log("Received transaction data:", JSON.stringify(data, null, 2));

    const validationResult = transactionSchema.safeParse(data);

    if (!validationResult.success) {
      console.error("Transaction validation failed:", validationResult.error.format());
      return NextResponse.json(
        { error: "Validation failed", details: validationResult.error.format() },
        { status: 400 }
      );
    }

    const validatedData = validationResult.data;
    const { items, ...transactionData } = validatedData;

    // Validate that all product IDs exist and have sufficient stock
    try {
      // Check if all products exist and have sufficient stock
      for (const item of items) {
        const product = await prisma.product.findUnique({
          where: { id: item.productId },
          include: {
            storeStock: true
          }
        });

        if (!product) {
          console.error(`Product with ID ${item.productId} not found`);
          return NextResponse.json(
            { error: `Product with ID ${item.productId} not found` },
            { status: 400 }
          );
        }

        // Check if product has sufficient stock
        if (!product.storeStock || product.storeStock.quantity < item.quantity) {
          const availableQuantity = product.storeStock ? product.storeStock.quantity : 0;
          console.error(`Insufficient stock for product ${product.name} (ID: ${product.id}). Requested: ${item.quantity}, Available: ${availableQuantity}`);
          return NextResponse.json(
            {
              error: `Insufficient stock for product ${product.name}`,
              details: {
                productId: product.id,
                productName: product.name,
                requestedQuantity: item.quantity,
                availableQuantity: availableQuantity
              }
            },
            { status: 400 }
          );
        }
      }
    } catch (error) {
      console.error("Error validating products:", error);
      return NextResponse.json(
        { error: "Error validating products", message: (error as Error).message },
        { status: 500 }
      );
    }

    // Convert dueDate string to Date if provided
    const dueDate = validatedData.dueDate ? new Date(validatedData.dueDate) : undefined;

    // Ensure cashReceived and changeAmount are properly set for CASH payments
    let cashReceived = validatedData.cashReceived;
    let changeAmount = validatedData.changeAmount;

    if (validatedData.paymentMethod === "CASH" && validatedData.paymentStatus === "PAID") {
      // Ensure cashReceived is set
      if (!cashReceived) {
        return NextResponse.json(
          { error: "Cash received amount is required for cash payments" },
          { status: 400 }
        );
      }

      // Calculate change amount if not provided
      if (changeAmount === undefined) {
        changeAmount = cashReceived - validatedData.total;
      }
    }

    // Start a transaction to ensure all operations succeed or fail together
    const transaction = await prisma.$transaction(async (prismaClient) => {
      // Create the transaction
      const newTransaction = await prismaClient.transaction.create({
        data: {
          ...transactionData,
          dueDate,
          cashReceived,
          changeAmount,
          cashierId: auth.user!.id,
          drawerSessionId: activeDrawerSession.id, // Associate with the active drawer session
          terminalId: validatedData.terminalId || activeDrawerSession.terminalId, // Use provided terminal ID or session terminal ID
          status: "COMPLETED", // Default to completed for POS transactions
          items: {
            create: items.map(item => ({
              productId: item.productId,
              quantity: item.quantity,
              unitPrice: item.unitPrice,
              discount: item.discount,
              subtotal: item.subtotal,
            })),
          },
        },
        include: {
          customer: true,
          cashier: {
            select: {
              id: true,
              name: true,
            },
          },
          items: {
            include: {
              product: true,
            },
          },
        },
      });

      // Update stock levels for each product
      for (const item of items) {
        // Get current stock
        const storeStock = await prismaClient.storeStock.findFirst({
          where: { productId: item.productId },
        });

        if (!storeStock) {
          throw new Error(`No stock found for product ID: ${item.productId}`);
        }

        // Calculate new quantity
        const previousQuantity = storeStock.quantity;
        const newQuantity = Number(previousQuantity) - Number(item.quantity);

        // Update stock quantity
        await prismaClient.storeStock.update({
          where: { id: storeStock.id },
          data: {
            quantity: newQuantity,
            lastUpdated: new Date(),
          },
        });

        // Create stock history record
        await prismaClient.stockHistory.create({
          data: {
            productId: item.productId,
            storeStockId: storeStock.id,
            userId: auth.user!.id,
            source: "SALE",
            previousQuantity: previousQuantity,
            newQuantity: newQuantity,
            changeQuantity: -Number(item.quantity), // Negative for sales
            referenceId: newTransaction.id,
            referenceType: "Transaction",
            notes: `Sale through transaction ${newTransaction.id}`,
          },
        });
      }

      // Log activity
      await prismaClient.activityLog.create({
        data: {
          userId: auth.user!.id,
          action: "CREATE_TRANSACTION",
          details: `Created transaction with ${items.length} items, total: ${transactionData.total}`,
        },
      });

      return newTransaction;
    });

    return NextResponse.json({ transaction }, { status: 201 });
  } catch (error) {
    console.error("Error creating transaction:", error);

    // Provide more detailed error information
    let errorMessage = "Failed to create transaction";
    let errorDetails = {};

    if (error instanceof Error) {
      errorMessage = error.message;

      // Check for specific error types
      if (error.name === "PrismaClientKnownRequestError") {
        const prismaError = error as any;
        errorDetails = {
          code: prismaError.code,
          meta: prismaError.meta,
          clientVersion: prismaError.clientVersion
        };

        // Handle specific Prisma error codes
        if (prismaError.code === "P2002") {
          errorMessage = "A transaction with this information already exists";
        } else if (prismaError.code === "P2003") {
          errorMessage = "Referenced record does not exist";
        }
      }
    }

    return NextResponse.json(
      {
        error: errorMessage,
        details: errorDetails,
        stack: process.env.NODE_ENV === "development" ? (error as Error).stack : undefined
      },
      { status: 500 }
    );
  }
}
