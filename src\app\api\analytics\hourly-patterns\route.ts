import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/auth";
import { verifyAuthToken } from "@/lib/auth-utils";
import { transformToHourlyPatterns } from "@/lib/analytics/dataTransformers";
import { subDays, startOfDay, endOfDay } from "date-fns";

export async function GET(request: NextRequest) {
  try {
    // Verify authentication using JW<PERSON> token from cookies
    const authResult = await verifyAuthToken(request);

    if (!authResult.authenticated || !authResult.user) {
      console.log("[Hourly Patterns API] Authentication failed:", authResult.error);
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { user } = authResult;
    const userRole = user.role;
    const userId = user.id;

    // Check if user has analytics access
    const hasAccess = ["SUPER_ADMIN", "FINANCE_ADMIN", "CASHIER"].includes(userRole);
    if (!hasAccess) {
      console.log("[Hourly Patterns API] User does not have access:", userRole);
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    const { searchParams } = new URL(request.url);
    const dateRange = searchParams.get("dateRange") || "30d";
    const cashierIds = searchParams.get("cashierIds")?.split(",").filter(Boolean);
    const terminalIds = searchParams.get("terminalIds")?.split(",").filter(Boolean);

    // Calculate date range
    let fromDate: Date;
    let toDate = endOfDay(new Date());

    switch (dateRange) {
      case "7d":
        fromDate = startOfDay(subDays(new Date(), 7));
        break;
      case "30d":
        fromDate = startOfDay(subDays(new Date(), 30));
        break;
      case "90d":
        fromDate = startOfDay(subDays(new Date(), 90));
        break;
      case "1y":
        fromDate = startOfDay(subDays(new Date(), 365));
        break;
      default:
        fromDate = startOfDay(subDays(new Date(), 30));
    }

    // Build where clause
    const whereClause: any = {
      createdAt: {
        gte: fromDate,
        lte: toDate,
      },
      status: {
        not: "VOIDED",
      },
    };

    // Add cashier filter if specified
    if (cashierIds && cashierIds.length > 0) {
      whereClause.cashierId = {
        in: cashierIds,
      };
    }

    // Add terminal filter if specified
    if (terminalIds && terminalIds.length > 0) {
      whereClause.terminalId = {
        in: terminalIds,
      };
    }

    // For cashiers, only show their own data
    if (userRole === "CASHIER") {
      whereClause.cashierId = userId;
    }

    // Fetch transactions
    const transactions = await prisma.transaction.findMany({
      where: whereClause,
      select: {
        id: true,
        total: true,
        createdAt: true,
        status: true,
      },
      orderBy: {
        createdAt: "asc",
      },
    });

    // Transform data for chart
    const hourlyPatterns = transformToHourlyPatterns(transactions);

    return NextResponse.json({
      data: hourlyPatterns,
      success: true,
      timestamp: new Date().toISOString(),
    });

  } catch (error) {
    console.error("Error fetching hourly patterns:", error);
    return NextResponse.json(
      {
        error: "Failed to fetch hourly patterns",
        success: false,
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}
