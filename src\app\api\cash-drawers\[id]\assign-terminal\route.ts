import { NextRequest, NextResponse } from "next/server";
import { z } from "zod";
import { PrismaClient } from "@/generated/prisma";
import { verifyAuthToken } from "@/lib/auth-utils";

const prisma = new PrismaClient();

// Terminal assignment schema for validation
const terminalAssignmentSchema = z.object({
  terminalId: z.string().nullable(),
});

// POST /api/cash-drawers/[id]/assign-terminal - Assign a terminal to a drawer
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication
    const auth = await verifyAuthToken(request);

    if (!auth.authenticated) {
      return NextResponse.json(
        { error: auth.error },
        { status: auth.status }
      );
    }

    // Check if user has permission to assign terminals
    const hasPermission = ["SUPER_ADMIN", "FINANCE_ADMIN"].includes(auth.user.role);
    if (!hasPermission) {
      return NextResponse.json(
        { error: "Unauthorized - Insufficient permissions" },
        { status: 403 }
      );
    }

    // Parse and validate request body
    const body = await request.json();
    const validatedData = terminalAssignmentSchema.parse(body);

    // Check if drawer exists
    const drawer = await prisma.cashDrawer.findUnique({
      where: { id: params.id },
    });

    if (!drawer) {
      return NextResponse.json(
        { error: "Cash drawer not found" },
        { status: 404 }
      );
    }

    // Check if drawer is active (only allow active drawers to be assigned)
    if (!drawer.isActive) {
      return NextResponse.json(
        { error: "Cash drawer is not active" },
        { status: 400 }
      );
    }

    // If terminalId is null, unassign the terminal
    if (validatedData.terminalId === null) {
      // Check if there are any open drawer sessions for this drawer
      const openSessions = await prisma.drawerSession.findFirst({
        where: {
          drawerId: params.id,
          status: "OPEN",
        },
      });

      if (openSessions) {
        return NextResponse.json(
          {
            error: "Drawer has open sessions",
            message: "Cannot unassign terminal while there are open sessions"
          },
          { status: 400 }
        );
      }

      // Find the current terminal assigned to this drawer
      const currentTerminal = await prisma.terminal.findFirst({
        where: { drawerId: params.id },
      });

      if (currentTerminal) {
        // Unassign terminal
        await prisma.terminal.update({
          where: { id: currentTerminal.id },
          data: { drawerId: null },
        });

        // Log activity
        await prisma.activityLog.create({
          data: {
            userId: auth.user.id,
            action: "UNASSIGN_TERMINAL",
            details: `Unassigned terminal "${currentTerminal.name}" from drawer "${drawer.name}"`,
          },
        });
      }

      // Get updated drawer
      const updatedDrawer = await prisma.cashDrawer.findUnique({
        where: { id: params.id },
        include: {
          terminal: {
            select: {
              id: true,
              name: true,
              location: true,
            },
          },
        },
      });

      return NextResponse.json({ drawer: updatedDrawer });
    }

    // Check if terminal exists
    const terminal = await prisma.terminal.findUnique({
      where: { id: validatedData.terminalId },
    });

    if (!terminal) {
      return NextResponse.json(
        { error: "Terminal not found" },
        { status: 404 }
      );
    }

    // Check if terminal is already assigned to another drawer
    if (terminal.drawerId && terminal.drawerId !== params.id) {
      const assignedDrawer = await prisma.cashDrawer.findUnique({
        where: { id: terminal.drawerId },
      });

      return NextResponse.json(
        {
          error: "Terminal already assigned",
          message: `This terminal is already assigned to drawer "${assignedDrawer?.name}"`
        },
        { status: 400 }
      );
    }

    // Check if drawer is already assigned to another terminal
    const existingTerminal = await prisma.terminal.findFirst({
      where: {
        drawerId: params.id,
        id: { not: validatedData.terminalId },
      },
    });

    if (existingTerminal) {
      // Unassign the existing terminal first
      await prisma.terminal.update({
        where: { id: existingTerminal.id },
        data: { drawerId: null },
      });
    }

    // Assign terminal to drawer
    await prisma.terminal.update({
      where: { id: validatedData.terminalId },
      data: { drawerId: params.id },
    });

    // Log activity
    await prisma.activityLog.create({
      data: {
        userId: auth.user.id,
        action: "ASSIGN_TERMINAL",
        details: `Assigned terminal "${terminal.name}" to drawer "${drawer.name}"`,
      },
    });

    // Get updated drawer
    const updatedDrawer = await prisma.cashDrawer.findUnique({
      where: { id: params.id },
      include: {
        terminal: {
          select: {
            id: true,
            name: true,
            location: true,
          },
        },
      },
    });

    return NextResponse.json({ drawer: updatedDrawer });
  } catch (error) {
    console.error("Error assigning terminal:", error);
    return NextResponse.json(
      { error: "Failed to assign terminal", message: (error as Error).message },
      { status: 500 }
    );
  }
}
