"use client";

import React, { createContext, useContext, useState, useEffect } from "react";
import { useClientAuth } from "@/hooks/use-client-auth";

interface SettingsContextType {
  settings: Record<string, string>;
  isLoading: boolean;
  error: string | null;
  updateSettings: (newSettings: Record<string, string>) => Promise<void>;
  isChatEnabled: boolean;
}

const SettingsContext = createContext<SettingsContextType | undefined>(undefined);

export function SettingsProvider({ children }: { children: React.ReactNode }) {
  const [settings, setSettings] = useState<Record<string, string>>({});
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { user } = useClientAuth();

  // Fetch settings on mount and when user changes
  useEffect(() => {
    if (user) {
      fetchSettings();
    }
  }, [user]);

  // Function to fetch settings from the API
  const fetchSettings = async () => {
    try {
      setIsLoading(true);
      setError(null);

      const response = await fetch("/api/settings");

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to fetch settings");
      }

      const data = await response.json();
      setSettings(data.settings || {});
    } catch (err: any) {
      console.error("Error fetching settings:", err);
      setError(err.message || "An error occurred while fetching settings");
    } finally {
      setIsLoading(false);
    }
  };

  // Function to update settings
  const updateSettings = async (newSettings: Record<string, string>) => {
    try {
      setIsLoading(true);
      setError(null);

      const response = await fetch("/api/settings", {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ settings: newSettings }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to update settings");
      }

      const data = await response.json();
      setSettings(data.settings || {});
    } catch (err: any) {
      console.error("Error updating settings:", err);
      setError(err.message || "An error occurred while updating settings");
      throw err; // Re-throw to allow handling in the component
    } finally {
      setIsLoading(false);
    }
  };

  // Helper function to check if chat is enabled
  const isChatEnabled = settings.enableChat === "true";

  return (
    <SettingsContext.Provider
      value={{
        settings,
        isLoading,
        error,
        updateSettings,
        isChatEnabled,
      }}
    >
      {children}
    </SettingsContext.Provider>
  );
}

// Custom hook to use the settings context
export function useSettings() {
  const context = useContext(SettingsContext);
  if (context === undefined) {
    throw new Error("useSettings must be used within a SettingsProvider");
  }
  return context;
}
