-- Create<PERSON><PERSON>
CREATE TYPE "DiscrepancyCategory" AS ENUM ('COUNTING_ERROR', 'SYSTEM_ERROR', 'THEFT_SUSPECTED', 'CASH_SHORTAGE', 'CASH_SURPLUS', 'REGISTER_ERROR', 'TRAINING_ERROR', 'PROCEDURAL_ERROR', 'UNKNOWN', 'OTHER');

-- <PERSON>reate<PERSON>num
CREATE TYPE "ResolutionStatus" AS ENUM ('PENDING', 'INVESTIGATING', 'RESOLVED', 'WRITTEN_OFF', 'ESCALATED', 'CLOSED');

-- Create<PERSON>num
CREATE TYPE "AuditAlertType" AS ENUM ('LARGE_DISCREPANCY', 'FREQUENT_SHORTAGES', 'PATTERN_DETECTED', 'THRESHOLD_EXCEEDED', 'UNUSUAL_ACTIVITY', 'COMPLIANCE_VIOLATION');

-- Create<PERSON><PERSON>
CREATE TYPE "AlertSeverity" AS ENUM ('LOW', 'MEDIUM', 'HIGH', 'CRITICAL');

-- CreateTable
CREATE TABLE "CashAuditAlert" (
    "id" TEXT NOT NULL,
    "cashReconciliationId" TEXT NOT NULL,
    "alertType" "AuditAlertType" NOT NULL,
    "severity" "AlertSeverity" NOT NULL DEFAULT 'MEDIUM',
    "message" TEXT NOT NULL,
    "threshold" DECIMAL(10,2),
    "actualValue" DECIMAL(10,2),
    "isResolved" BOOLEAN NOT NULL DEFAULT false,
    "resolvedBy" TEXT,
    "resolvedAt" TIMESTAMP(3),
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "CashAuditAlert_pkey" PRIMARY KEY ("id")
);

-- AlterTable
ALTER TABLE "CashReconciliation" ADD COLUMN     "discrepancyCategory" "DiscrepancyCategory",
ADD COLUMN     "investigatedBy" TEXT,
ADD COLUMN     "resolutionNotes" TEXT,
ADD COLUMN     "resolutionStatus" "ResolutionStatus" NOT NULL DEFAULT 'PENDING',
ADD COLUMN     "resolvedAt" TIMESTAMP(3);

-- AddForeignKey
ALTER TABLE "CashReconciliation" ADD CONSTRAINT "CashReconciliation_investigatedBy_fkey" FOREIGN KEY ("investigatedBy") REFERENCES "User"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "CashAuditAlert" ADD CONSTRAINT "CashAuditAlert_cashReconciliationId_fkey" FOREIGN KEY ("cashReconciliationId") REFERENCES "CashReconciliation"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "CashAuditAlert" ADD CONSTRAINT "CashAuditAlert_resolvedBy_fkey" FOREIGN KEY ("resolvedBy") REFERENCES "User"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- Create indexes for better performance
CREATE INDEX "CashReconciliation_discrepancyCategory_idx" ON "CashReconciliation"("discrepancyCategory");
CREATE INDEX "CashReconciliation_resolutionStatus_idx" ON "CashReconciliation"("resolutionStatus");
CREATE INDEX "CashReconciliation_businessDate_idx" ON "CashReconciliation"("businessDate");
CREATE INDEX "CashAuditAlert_alertType_idx" ON "CashAuditAlert"("alertType");
CREATE INDEX "CashAuditAlert_severity_idx" ON "CashAuditAlert"("severity");
CREATE INDEX "CashAuditAlert_isResolved_idx" ON "CashAuditAlert"("isResolved");
CREATE INDEX "CashAuditAlert_createdAt_idx" ON "CashAuditAlert"("createdAt");
