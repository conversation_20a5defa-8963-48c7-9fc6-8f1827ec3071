"use client";

import { useState, useEffect } from "react";
import { <PERSON>, <PERSON>Content, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { formatCurrency } from "@/lib/utils";
import { ShoppingCart, CreditCard, AlertCircle } from "lucide-react";
import { Alert, AlertDescription } from "@/components/ui/alert";

interface TodayStats {
  todaySales: number;
  todayRevenue: number;
}

export function useTodayStats(date?: string) {
  const [stats, setStats] = useState<TodayStats>({
    todaySales: 0,
    todayRevenue: 0,
  });
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchTodayStats = async () => {
      try {
        setIsLoading(true);
        // Fetch today's sales and revenue
        const url = date 
          ? `/api/transactions/today?date=${date}`
          : "/api/transactions/today";
          
        const response = await fetch(url);
        
        if (!response.ok) {
          throw new Error("Failed to fetch today's statistics");
        }
        
        const data = await response.json();
        
        setStats({
          todaySales: data.todaySales || 0,
          todayRevenue: data.todayRevenue || 0,
        });
      } catch (err) {
        console.error("Error fetching today's statistics:", err);
        setError(err instanceof Error ? err.message : "An unknown error occurred");
        
        // Set fallback data
        setStats({
          todaySales: 0,
          todayRevenue: 0,
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchTodayStats();
  }, [date]);

  return { stats, isLoading, error };
}

export function TodaySalesCard({ stats }: { stats: TodayStats }) {
  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between pb-2">
        <CardTitle className="text-sm font-medium">Today's Sales</CardTitle>
        <ShoppingCart className="h-4 w-4 text-muted-foreground" />
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold">{stats.todaySales}</div>
        <div className="flex items-center pt-1 text-xs text-muted-foreground">
          <span>Transactions today</span>
        </div>
      </CardContent>
    </Card>
  );
}

export function TodayRevenueCard({ stats }: { stats: TodayStats }) {
  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between pb-2">
        <CardTitle className="text-sm font-medium">Today's Revenue</CardTitle>
        <CreditCard className="h-4 w-4 text-muted-foreground" />
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold">{formatCurrency(stats.todayRevenue)}</div>
        <div className="flex items-center pt-1 text-xs text-muted-foreground">
          <span>Revenue today</span>
        </div>
      </CardContent>
    </Card>
  );
}
