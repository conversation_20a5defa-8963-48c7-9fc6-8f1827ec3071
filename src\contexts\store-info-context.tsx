"use client";

import React, { createContext, useContext, useState, useEffect } from "react";
import { useClientAuth } from "@/hooks/use-client-auth";

interface StoreInfo {
  id: string;
  storeName: string;
  phone?: string | null;
  address?: string | null;
  email?: string | null;
  website?: string | null;
  taxId?: string | null;
  logoUrl?: string | null;
  createdAt: string;
  updatedAt: string;
}

interface StoreInfoContextType {
  storeInfo: StoreInfo | null;
  isLoading: boolean;
  error: string | null;
  updateStoreInfo: (data: Partial<StoreInfo>) => Promise<void>;
  refreshStoreInfo: () => Promise<void>;
}

const StoreInfoContext = createContext<StoreInfoContextType | undefined>(undefined);

export function StoreInfoProvider({ children }: { children: React.ReactNode }) {
  const [storeInfo, setStoreInfo] = useState<StoreInfo | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { user } = useClientAuth();

  // Fetch store info on mount and when user changes
  useEffect(() => {
    if (user) {
      fetchStoreInfo();
    }
  }, [user]);

  // Try to load store info from localStorage on initial mount for faster display
  useEffect(() => {
    const cachedStoreInfo = localStorage.getItem("store-info");
    if (cachedStoreInfo) {
      try {
        const parsed = JSON.parse(cachedStoreInfo);
        setStoreInfo(parsed);
        setIsLoading(false); // Set loading to false immediately if we have cached data
      } catch (error) {
        console.error("Error parsing cached store info:", error);
        localStorage.removeItem("store-info");
      }
    }
  }, []);

  // Function to fetch store info from the API
  const fetchStoreInfo = async () => {
    try {
      // Only set loading to true if we don't have cached data
      if (!storeInfo) {
        setIsLoading(true);
      }
      setError(null);

      const response = await fetch("/api/store-info");

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to fetch store information");
      }

      const data = await response.json();
      setStoreInfo(data.storeInfo);

      // Cache the store info in localStorage for faster subsequent loads
      localStorage.setItem("store-info", JSON.stringify(data.storeInfo));
    } catch (err: any) {
      console.error("Error fetching store info:", err);
      setError(err.message || "An error occurred while fetching store information");
    } finally {
      setIsLoading(false);
    }
  };

  // Function to update store info
  const updateStoreInfo = async (data: Partial<StoreInfo>) => {
    try {
      setIsLoading(true);
      setError(null);

      const response = await fetch("/api/store-info", {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to update store information");
      }

      const result = await response.json();
      setStoreInfo(result.storeInfo);

      // Update the cache with new store info
      localStorage.setItem("store-info", JSON.stringify(result.storeInfo));
    } catch (err: any) {
      console.error("Error updating store info:", err);
      setError(err.message || "An error occurred while updating store information");
      throw err; // Re-throw to allow handling in the component
    } finally {
      setIsLoading(false);
    }
  };

  // Function to refresh store info
  const refreshStoreInfo = async () => {
    await fetchStoreInfo();
  };

  return (
    <StoreInfoContext.Provider
      value={{
        storeInfo,
        isLoading,
        error,
        updateStoreInfo,
        refreshStoreInfo,
      }}
    >
      {children}
    </StoreInfoContext.Provider>
  );
}

// Custom hook to use the store info context
export function useStoreInfo() {
  const context = useContext(StoreInfoContext);
  if (context === undefined) {
    throw new Error("useStoreInfo must be used within a StoreInfoProvider");
  }
  return context;
}
