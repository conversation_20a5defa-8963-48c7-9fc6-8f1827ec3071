"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { MainLayout } from "@/components/layout/MainLayout";
import { PageHeader } from "@/components/layout/PageHeader";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Loader2, Plus, Search, Filter, ArrowUpDown } from "lucide-react";
import { toast } from "sonner";
import CustomPagination from "@/components/ui/custom-pagination";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Textarea } from "@/components/ui/textarea";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";

// Define adjustment reasons
const adjustmentReasons = [
  { value: "INVENTORY_COUNT", label: "Inventory Count" },
  { value: "DAMAGED", label: "Damaged" },
  { value: "EXPIRED", label: "Expired" },
  { value: "THEFT", label: "Theft" },
  { value: "LOSS", label: "Loss" },
  { value: "RETURN", label: "Return" },
  { value: "CORRECTION", label: "Correction" },
  { value: "OTHER", label: "Other" },
];

// Define form schema
const adjustmentFormSchema = z.object({
  productId: z.string({
    required_error: "Please select a product",
  }),
  locationType: z.enum(["STORE", "WAREHOUSE"], {
    required_error: "Please select a location type",
  }),
  adjustmentQuantity: z.number({
    required_error: "Please enter an adjustment quantity",
  }),
  reason: z.enum(
    ["INVENTORY_COUNT", "DAMAGED", "EXPIRED", "THEFT", "LOSS", "RETURN", "CORRECTION", "OTHER"],
    {
      required_error: "Please select a reason",
    }
  ),
  notes: z.string().optional(),
});

interface Product {
  id: string;
  name: string;
  sku: string;
  category?: {
    id: string;
    name: string;
  } | null;
  unit?: {
    id: string;
    name: string;
    abbreviation: string;
  } | null;
}

interface StockAdjustment {
  id: string;
  date: string;
  productId: string;
  previousQuantity: number;
  newQuantity: number;
  adjustmentQuantity: number;
  reason: string;
  notes?: string;
  product: Product;
  user: {
    id: string;
    name: string;
    role: string;
  };
}

export default function StockAdjustmentsPage() {
  const router = useRouter();
  const [adjustments, setAdjustments] = useState<StockAdjustment[]>([]);
  const [filteredAdjustments, setFilteredAdjustments] = useState<StockAdjustment[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [reasonFilter, setReasonFilter] = useState("all");
  const [products, setProducts] = useState<Product[]>([]);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 10,
    total: 0,
    pages: 0,
  });
  const [sortConfig, setSortConfig] = useState<{
    key: string;
    direction: "ascending" | "descending";
  }>({ key: "date", direction: "descending" });

  // Initialize form
  const form = useForm<z.infer<typeof adjustmentFormSchema>>({
    resolver: zodResolver(adjustmentFormSchema),
    defaultValues: {
      locationType: "STORE",
      adjustmentQuantity: 0,
      notes: "",
    },
  });

  // Fetch adjustments data
  useEffect(() => {
    const fetchAdjustments = async () => {
      setLoading(true);
      try {
        const response = await fetch(
          `/api/inventory/adjustments?page=${pagination.page}&limit=${pagination.limit}`
        );
        if (!response.ok) throw new Error("Failed to fetch adjustments");
        const data = await response.json();

        setAdjustments(data.adjustments);
        setFilteredAdjustments(data.adjustments);
        setPagination(data.pagination);
      } catch (error) {
        console.error("Error fetching adjustments:", error);
        toast.error("Failed to load adjustments");
      } finally {
        setLoading(false);
      }
    };

    fetchAdjustments();
  }, [pagination.page, pagination.limit]);

  // Fetch products for the form
  useEffect(() => {
    const fetchProducts = async () => {
      try {
        const response = await fetch("/api/products?limit=500&active=true");
        if (!response.ok) throw new Error("Failed to fetch products");
        const data = await response.json();
        setProducts(data.products);
      } catch (error) {
        console.error("Error fetching products:", error);
        toast.error("Failed to load products");
      }
    };

    fetchProducts();
  }, []);

  // Handle search and filtering
  useEffect(() => {
    if (searchTerm || (reasonFilter && reasonFilter !== "all")) {
      const filtered = adjustments.filter((adjustment) => {
        const matchesSearch = searchTerm
          ? adjustment.product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
            adjustment.product.sku.toLowerCase().includes(searchTerm.toLowerCase())
          : true;

        const matchesReason =
          reasonFilter && reasonFilter !== "all" ? adjustment.reason === reasonFilter : true;

        return matchesSearch && matchesReason;
      });

      setFilteredAdjustments(filtered);
    } else {
      setFilteredAdjustments(adjustments);
    }
  }, [searchTerm, reasonFilter, adjustments]);

  // Handle sorting
  const handleSort = (key: string) => {
    let direction: "ascending" | "descending" = "ascending";

    if (sortConfig.key === key) {
      direction = sortConfig.direction === "ascending" ? "descending" : "ascending";
    }

    setSortConfig({ key, direction });

    const sortedData = [...filteredAdjustments].sort((a, b) => {
      let aValue, bValue;

      // Handle nested properties
      if (key.includes(".")) {
        const keys = key.split(".");
        aValue = keys.reduce((obj, k) => obj?.[k], a);
        bValue = keys.reduce((obj, k) => obj?.[k], b);
      } else {
        aValue = a[key];
        bValue = b[key];
      }

      // Handle date comparison
      if (key === "date") {
        return direction === "ascending"
          ? new Date(aValue).getTime() - new Date(bValue).getTime()
          : new Date(bValue).getTime() - new Date(aValue).getTime();
      }

      // Handle string comparison
      if (typeof aValue === "string") {
        return direction === "ascending"
          ? aValue.localeCompare(bValue)
          : bValue.localeCompare(aValue);
      }

      // Handle number comparison
      return direction === "ascending" ? aValue - bValue : bValue - aValue;
    });

    setFilteredAdjustments(sortedData);
  };

  // Handle form submission
  const onSubmit = async (values: z.infer<typeof adjustmentFormSchema>) => {
    try {
      const response = await fetch("/api/inventory/adjustments", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(values),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to create adjustment");
      }

      toast.success("Stock adjustment created successfully");
      form.reset();
      setIsDialogOpen(false);

      // Refresh the adjustments list
      const refreshResponse = await fetch(
        `/api/inventory/adjustments?page=${pagination.page}&limit=${pagination.limit}`
      );
      const refreshData = await refreshResponse.json();
      setAdjustments(refreshData.adjustments);
      setFilteredAdjustments(refreshData.adjustments);
    } catch (error) {
      console.error("Error creating adjustment:", error);
      toast.error(error instanceof Error ? error.message : "Failed to create adjustment");
    }
  };

  // Format reason for display
  const formatReason = (reason: string) => {
    const reasonObj = adjustmentReasons.find((r) => r.value === reason);
    return reasonObj ? reasonObj.label : reason;
  };

  // Handle page change
  const handlePageChange = (page: number) => {
    setPagination((prev) => ({ ...prev, page }));
  };

  return (
    <MainLayout>
      <PageHeader
        title="Stock Adjustments"
        actions={
          <Button variant="outline" onClick={() => router.push("/inventory/stock")}>
            Back to Stock
          </Button>
        }
      />
      <div className="space-y-6">
        <div className="flex justify-end">
          <div className="flex space-x-2">
            <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
              <DialogTrigger asChild>
                <Button>
                  <Plus className="mr-2 h-4 w-4" /> New Adjustment
                </Button>
              </DialogTrigger>
              <DialogContent className="sm:max-w-[500px]">
                <DialogHeader>
                  <DialogTitle>Create Stock Adjustment</DialogTitle>
                  <DialogDescription>
                    Adjust inventory levels for a product. Enter the details below.
                  </DialogDescription>
                </DialogHeader>
                <Form {...form}>
                  <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                    <FormField
                      control={form.control}
                      name="productId"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Product</FormLabel>
                          <Select onValueChange={field.onChange} defaultValue={field.value}>
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Select a product" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              {products.map((product) => (
                                <SelectItem key={product.id} value={product.id}>
                                  {product.name} ({product.sku})
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name="locationType"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Location</FormLabel>
                          <Select onValueChange={field.onChange} defaultValue={field.value}>
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Select location" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              <SelectItem value="STORE">Store</SelectItem>
                              <SelectItem value="WAREHOUSE">Warehouse</SelectItem>
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name="adjustmentQuantity"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Adjustment Quantity</FormLabel>
                          <FormControl>
                            <Input
                              type="number"
                                const value = e.target.value;
                                if (value === "" || value === "-") {
                                  field.onChange(value === "" ? 0 : 0);
                                } else {
                                  const intValue = parseInt(value, 10);
                                  if (!isNaN(intValue)) {
                                    field.onChange(intValue);
                                  }
                                }
                              }}
                              value={field.value === 0 ? "" : field.value.toString()}
                              placeholder="Enter quantity (use - for decreases)"
                            />
                          </FormControl>
                          <FormDescription>
                            Use positive values to add stock, negative to remove
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name="reason"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Reason</FormLabel>
                          <Select onValueChange={field.onChange} defaultValue={field.value}>
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Select reason" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              {adjustmentReasons.map((reason) => (
                                <SelectItem key={reason.value} value={reason.value}>
                                  {reason.label}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name="notes"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Notes (Optional)</FormLabel>
                          <FormControl>
                            <Textarea
                              placeholder="Enter additional details about this adjustment"
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <DialogFooter>
                      <Button type="submit">Save Adjustment</Button>
                    </DialogFooter>
                  </form>
                </Form>
              </DialogContent>
            </Dialog>
          </div>
        </div>

        {/* Filters */}

        <div className="flex flex-wrap gap-4">
          <div className="flex-1 min-w-[200px]">
            <div className="relative">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                type="search"
                placeholder="Search products..."
                className="pl-8"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
          </div>
          <div className="w-[200px]">
            <Select value={reasonFilter} onValueChange={setReasonFilter}>
              <SelectTrigger>
                <SelectValue placeholder="All Reasons" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Reasons</SelectItem>
                {adjustmentReasons.map((reason) => (
                  <SelectItem key={reason.value} value={reason.value}>
                    {reason.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>

        {/* Adjustments List */}
        <Card>
          <CardHeader>
            <CardTitle>Adjustment History</CardTitle>
            <CardDescription>View and manage stock adjustments</CardDescription>
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className="flex justify-center items-center py-8">
                <Loader2 className="h-8 w-8 animate-spin text-primary" />
              </div>
            ) : filteredAdjustments.length > 0 ? (
              <>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead className="cursor-pointer" onClick={() => handleSort("date")}>
                        Date <ArrowUpDown className="inline h-4 w-4 ml-1" />
                      </TableHead>
                      <TableHead
                        className="cursor-pointer"
                        onClick={() => handleSort("product.name")}
                      >
                        Product <ArrowUpDown className="inline h-4 w-4 ml-1" />
                      </TableHead>
                      <TableHead>SKU</TableHead>
                      <TableHead
                        className="cursor-pointer"
                        onClick={() => handleSort("adjustmentQuantity")}
                      >
                        Adjustment <ArrowUpDown className="inline h-4 w-4 ml-1" />
                      </TableHead>
                      <TableHead>Previous</TableHead>
                      <TableHead>New</TableHead>
                      <TableHead>Reason</TableHead>
                      <TableHead>User</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredAdjustments.map((adjustment) => (
                      <TableRow key={adjustment.id}>
                        <TableCell>{new Date(adjustment.date).toLocaleString()}</TableCell>
                        <TableCell className="font-medium">{adjustment.product.name}</TableCell>
                        <TableCell>{adjustment.product.sku}</TableCell>
                        <TableCell>
                          <span
                            className={
                              adjustment.adjustmentQuantity > 0 ? "text-green-600" : "text-red-600"
                            }
                          >
                            {adjustment.adjustmentQuantity > 0 ? "+" : ""}
                            {Number(adjustment.adjustmentQuantity).toFixed(2)}
                          </span>
                        </TableCell>
                        <TableCell>{Number(adjustment.previousQuantity).toFixed(2)}</TableCell>
                        <TableCell>{Number(adjustment.newQuantity).toFixed(2)}</TableCell>
                        <TableCell>{formatReason(adjustment.reason)}</TableCell>
                        <TableCell>{adjustment.user.name}</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
                <div className="mt-4">
                  <CustomPagination
                    currentPage={pagination.page}
                    totalPages={pagination.pages}
                    onPageChange={handlePageChange}
                  />
                </div>
              </>
            ) : (
              <div className="text-center py-8">
                <p className="text-muted-foreground">No adjustments found</p>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </MainLayout>
  );
}
