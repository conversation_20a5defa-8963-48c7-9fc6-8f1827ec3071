"use client";

import React, { useState, useEffect, useCallback } from "react";
import { useRouter } from "next/navigation";
import { MainLayout } from "@/components/layout/MainLayout";
import { PageHeader } from "@/components/layout/PageHeader";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
} from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Loader2, RefreshCw } from "lucide-react";
import { toast } from "sonner";
import { formatCurrency, formatDate } from "@/lib/utils";
import Link from "next/link";
import { useAuth } from "@/hooks/use-auth";
import CustomPagination from "@/components/ui/custom-pagination";

interface DrawerSession {
  id: string;
  drawerId: string;
  userId: string;
  businessDate: string;
  openingBalance: number;
  expectedClosingBalance?: number;
  actualClosingBalance?: number;
  discrepancy?: number;
  openedAt: string;
  closedAt?: string;
  status: "OPEN" | "CLOSED" | "RECONCILED";
  drawer: {
    id: string;
    name: string;
    location?: string;
  };
  user: {
    id: string;
    name: string;
  };
  _count: {
    transactions: number;
  };
}

interface PaginationInfo {
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

export default function DrawerSessionsPage() {
  const router = useRouter();
  const { user } = useAuth();
  const [sessions, setSessions] = useState<DrawerSession[]>([]);
  const [pagination, setPagination] = useState<PaginationInfo>({
    total: 0,
    page: 1,
    limit: 10,
    totalPages: 0,
  });
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isAutoRefreshing, setIsAutoRefreshing] = useState(true);
  const [lastRefreshed, setLastRefreshed] = useState<Date>(new Date());

  // Fetch drawer sessions
  const fetchSessions = useCallback(async (page = 1) => {
    try {
      setError(null);
      
      // Only show loading indicator on initial load or page change
      if (sessions.length === 0 || pagination.page !== page) {
        setIsLoading(true);
      }

      const response = await fetch(`/api/drawer-sessions?page=${page}&limit=${pagination.limit}`);
      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || "Failed to fetch drawer sessions");
      }

      setSessions(data.sessions);
      setPagination(data.pagination);
      setLastRefreshed(new Date());
    } catch (err) {
      setError(err instanceof Error ? err.message : "An unknown error occurred");
      console.error("Error fetching drawer sessions:", err);
    } finally {
      setIsLoading(false);
    }
  }, [pagination.limit, sessions.length, pagination.page]);

  // Load sessions on mount and set up auto-refresh
  useEffect(() => {
    fetchSessions(pagination.page);
    
    // Set up auto-refresh every 10 seconds if enabled
    let intervalId: NodeJS.Timeout | null = null;
    
    if (isAutoRefreshing) {
      intervalId = setInterval(() => {
        fetchSessions(pagination.page);
      }, 10000); // 10 seconds
    }
    
    // Clean up interval on unmount
    return () => {
      if (intervalId) {
        clearInterval(intervalId);
      }
    };
  }, [fetchSessions, isAutoRefreshing, pagination.page]);

  // Handle page change
  const handlePageChange = (page: number) => {
    fetchSessions(page);
  };

  // Toggle auto-refresh
  const toggleAutoRefresh = () => {
    setIsAutoRefreshing(!isAutoRefreshing);
    if (!isAutoRefreshing) {
      // Refresh immediately when turning auto-refresh on
      fetchSessions(pagination.page);
    }
  };

  // Manual refresh
  const handleManualRefresh = () => {
    fetchSessions(pagination.page);
    toast.success("Drawer sessions refreshed");
  };

  // Get status badge
  const getStatusBadge = (status: string) => {
    switch (status) {
      case "OPEN":
        return <Badge variant="success">Open</Badge>;
      case "CLOSED":
        return <Badge variant="default">Closed</Badge>;
      case "RECONCILED":
        return <Badge variant="secondary">Reconciled</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  // View session details
  const handleViewSession = (sessionId: string) => {
    router.push(`/admin/drawer-sessions/${sessionId}`);
  };

  return (
    <MainLayout>
      <PageHeader
        heading="Drawer Sessions"
        subheading="View all cash drawer sessions"
      />

      <div className="flex justify-between items-center mb-6">
        <div className="text-sm text-muted-foreground">
          Last refreshed: {formatDate(lastRefreshed)}
        </div>
        <div className="flex gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={toggleAutoRefresh}
          >
            {isAutoRefreshing ? "Disable Auto-Refresh" : "Enable Auto-Refresh"}
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={handleManualRefresh}
            disabled={isLoading}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>All Drawer Sessions</CardTitle>
          <CardDescription>
            View all cash drawer sessions across all drawers
            {isAutoRefreshing && " (Auto-refreshing every 10 seconds)"}
          </CardDescription>
        </CardHeader>
        <CardContent>
          {isLoading && sessions.length === 0 ? (
            <div className="flex justify-center items-center py-8">
              <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
            </div>
          ) : error ? (
            <div className="bg-destructive/10 text-destructive p-4 rounded-md">{error}</div>
          ) : sessions.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              No drawer sessions found.
            </div>
          ) : (
            <>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Drawer</TableHead>
                    <TableHead>Cashier</TableHead>
                    <TableHead>Business Date</TableHead>
                    <TableHead>Opening Balance</TableHead>
                    <TableHead>Transactions</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Opened At</TableHead>
                    <TableHead>Closed At</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {sessions.map((session) => (
                    <TableRow key={session.id}>
                      <TableCell>{session.drawer.name}</TableCell>
                      <TableCell>{session.user.name}</TableCell>
                      <TableCell>{formatDate(new Date(session.businessDate))}</TableCell>
                      <TableCell>{formatCurrency(session.openingBalance)}</TableCell>
                      <TableCell>{session._count.transactions}</TableCell>
                      <TableCell>{getStatusBadge(session.status)}</TableCell>
                      <TableCell>{formatDate(new Date(session.openedAt))}</TableCell>
                      <TableCell>
                        {session.closedAt ? formatDate(new Date(session.closedAt)) : "-"}
                      </TableCell>
                      <TableCell className="text-right">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleViewSession(session.id)}
                        >
                          View Details
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
              
              {pagination.totalPages > 1 && (
                <div className="mt-4">
                  <CustomPagination
                    currentPage={pagination.page}
                    totalPages={pagination.totalPages}
                    onPageChange={handlePageChange}
                  />
                </div>
              )}
            </>
          )}
        </CardContent>
      </Card>
    </MainLayout>
  );
}
