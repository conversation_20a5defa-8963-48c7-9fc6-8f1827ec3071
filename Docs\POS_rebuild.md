# 🧾 Revamped POS System - Development Specification

## ❌ Decommission Old POS

- **Remove** all existing POS-related files:
  - Components
  - API routes
  - Utility functions
  - Context/hooks
  - Database models
  - Third-party libraries (if only used by POS)

---

## 🚀 New POS System Overview

### 🔐 Access Control

- **Only accessible to** users with role: `<PERSON><PERSON><PERSON>IER`.
- Upon login:
  - If role is `CAS<PERSON><PERSON><PERSON>`, redirect immediately to `/pos`.
  - Restrict all other routes from access.

---

## 🎨 Layout & UI

### 🧱 Custom Layout (No Main Layout)

- Exclude global Sidebar and Header.
- Use a dedicated POS layout component.

### 🧭 POS Header Includes:

| Element       | Description                                 |
| ------------- | ------------------------------------------- |
| `h1` Title    | **"Point of Sale"**                         |
| Cashier Name  | e.g., `Logged in as: <PERSON>`              |
| Logout Button | Standard logout action                      |
| Live Clock    | Format: `DD/MM/YYYY HH:mm:ss` (GMT+7)       |
| Drawer Status | Show: `Drawer: OPEN` or `Drawer: NO DRAWER` |

---

## 🖼️ Page Layout Structure

### ➡️ Split into Two Main Sections:

- **Left Section (70%)** – Cart & Product Handling
- **Right Section (30%)** – Customer & Payment

---

## 🛒 Left Section: Shopping Cart

### 🔍 Product Search Field

- Searchable by:
  - Product Name
  - SKU
  - Barcode (EAN-13, 13 digits)

#### Typing Search:

- Starts after typing **3 characters**
- Shows dropdown results
- Add to cart by:
  - Clicking result
  - Tab → Arrow keys → Enter

#### Barcode Scan (Paste Behavior):

- On 13-digit input + `Enter`:
  - If match → auto add to cart
  - If no match → show "Product not found" message
  - Mimics barcode scanner input

---

### 📦 Cart Table Columns

| Column          | Behavior                 |
| --------------- | ------------------------ |
| Product Name    | Display only             |
| Price           | Display only             |
| Quantity        | With `+` and `-` buttons |
| Manual Discount | Inline editable          |
| Subtotal        | Auto-calculated          |
| Delete Button   | Removes item from cart   |

---

### 🧾 Below Cart Summary

- Cart Subtotal
- Discount (editable)
- Total (Subtotal - Discount)
- "Clear Cart" button

---

## 👤 Right Section: Customer & Payment

### 👥 Customer Dropdown

- Dropdown with customers from DB
- Default option: `"Walk-in Customer"`
- Quick Add option:
  - Opens dialog with:
    - Name (required)
    - Phone (optional)
  - On submit → auto-select new customer

---

### 📦 Order Summary Box

| Field          | Description          |
| -------------- | -------------------- |
| Items Count    | Total unique items   |
| Subtotal       | Cart subtotal        |
| Discount       | Cart discount        |
| Total          | Final amount         |
| Proceed Button | Opens payment dialog |

---

### 🧾 Cash Drawer Info (Collapsible)

- Default: Collapsed
- Info to show (when expanded):
  - Terminal Name
  - Drawer Name
  - Opening Balance
  - Cash Sales
  - Expected Balance
  - Close Drawer Button

---

## ⚙️ Functional Behavior

### A. Login Behavior

1. Redirect `CASHIER` role to `/pos`.
2. Auto-open assigned drawer if eligible.

#### Drawer State Scenarios:

| Scenario                        | Result                                             |
| ------------------------------- | -------------------------------------------------- |
| Drawer inactive                 | Show message: `"Drawer inactive. Contact admin."`  |
| Drawer not assigned to terminal | Show message: `"Drawer not assigned to terminal."` |

---

### B. Operation Workflow

#### 1. Product Search

- Supports both typing & barcode (paste)
- Barcode triggers instant add
- Supports keyboard navigation

---

#### 2. Payment Modal (On Proceed Click)

- **Select Payment Method** (dropdown):
  - `Cash` (default)
  - `Debit`
  - `QRIS`

##### If Cash is Selected:

- Show **Amount Received** field
  - Must be ≥ Total
  - If valid → show `Change`
  - If invalid → disable "Complete Payment" button
- Hide field for other methods

---

- **Select Payment Status**:
  - Options: `Paid (default)`, `Partial`, `Pending`
- **Optional Note** textarea

---

#### 3. On Complete Payment

- Save transaction to DB
- Close dialog
- Clear cart
- Refocus on search field
- Open new tab:
  - Show printable receipt
  - Auto-trigger print dialog
- Returning to POS tab:
  - Search field remains focused

---

#### 4. Smart Focus Behavior

- Any interaction (dropdowns, clicks) auto-refocuses back to the **search input**.
- Keeps flow fast for next transaction.

---

## ✅ Ready for Implementation
