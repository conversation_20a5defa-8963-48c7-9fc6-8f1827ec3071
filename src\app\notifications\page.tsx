"use client";

import { useState, useEffect } from "react";
import { MainLayout } from "@/components/layout/MainLayout";
import { PageHeader } from "@/components/layout/PageHeader";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Bell,
  Package,
  Search,
  Filter,
  Check,
  CheckCheck,
  ExternalLink,
  Loader2,
  AlertTriangle,
} from "lucide-react";
import { format } from "date-fns";
import Link from "next/link";
import { toast } from "sonner";

interface Notification {
  id: string;
  title: string;
  message: string;
  type: string;
  isRead: boolean;
  createdAt: string;
  purchaseOrderId?: string;
  actionUrl?: string;
  metadata?: any;
  purchaseOrder?: {
    id: string;
    status: string;
    total: number;
    supplier: {
      name: string;
    };
  };
}

interface NotificationResponse {
  notifications: Notification[];
  pagination: {
    total: number;
    page: number;
    limit: number;
    pages: number;
  };
}

export default function NotificationsPage() {
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [pagination, setPagination] = useState({
    total: 0,
    page: 1,
    limit: 20,
    pages: 0,
  });

  // Filters
  const [activeTab, setActiveTab] = useState("all");
  const [typeFilter, setTypeFilter] = useState("ALL");
  const [searchQuery, setSearchQuery] = useState("");

  // Fetch notifications
  const fetchNotifications = async (page = 1) => {
    try {
      setLoading(true);
      setError(null);

      const params = new URLSearchParams({
        page: page.toString(),
        limit: pagination.limit.toString(),
      });

      if (activeTab === "unread") {
        params.append("unread", "true");
      }

      if (typeFilter && typeFilter !== "ALL") {
        params.append("type", typeFilter);
      }

      const response = await fetch(`/api/notifications?${params}`);
      if (!response.ok) {
        throw new Error("Failed to fetch notifications");
      }

      const data: NotificationResponse = await response.json();
      setNotifications(data.notifications);
      setPagination(data.pagination);
    } catch (error) {
      console.error("Error fetching notifications:", error);
      setError(error instanceof Error ? error.message : "Failed to load notifications");
    } finally {
      setLoading(false);
    }
  };

  // Mark notification as read
  const markAsRead = async (id: string) => {
    try {
      const response = await fetch(`/api/notifications/${id}`, {
        method: "PATCH",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ isRead: true }),
      });

      if (!response.ok) {
        throw new Error("Failed to mark notification as read");
      }

      setNotifications((prev) => prev.map((n) => (n.id === id ? { ...n, isRead: true } : n)));
      toast.success("Notification marked as read");
    } catch (error) {
      toast.error("Failed to mark notification as read");
    }
  };

  // Mark all notifications as read
  const markAllAsRead = async () => {
    try {
      const response = await fetch("/api/notifications/mark-all-read", {
        method: "POST",
      });

      if (!response.ok) {
        throw new Error("Failed to mark all notifications as read");
      }

      setNotifications((prev) => prev.map((n) => ({ ...n, isRead: true })));
      toast.success("All notifications marked as read");
    } catch (error) {
      toast.error("Failed to mark all notifications as read");
    }
  };

  // Get notification badge color
  const getNotificationBadgeColor = (type: string) => {
    switch (type) {
      case "PURCHASE_ORDER_APPROVAL":
        return "orange";
      case "PURCHASE_ORDER_APPROVED":
        return "green";
      case "PURCHASE_ORDER_REJECTED":
        return "destructive";
      case "PURCHASE_ORDER_RECEIVED":
        return "blue";
      case "ALERT":
        return "destructive";
      case "MESSAGE":
        return "blue";
      case "INFO":
        return "green";
      default:
        return "default";
    }
  };

  // Get notification icon
  const getNotificationIcon = (type: string) => {
    if (type.startsWith("PURCHASE_ORDER")) {
      return <Package className="h-4 w-4" />;
    }
    return <Bell className="h-4 w-4" />;
  };

  // Filter notifications by search query
  const filteredNotifications = notifications.filter(
    (notification) =>
      notification.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      notification.message.toLowerCase().includes(searchQuery.toLowerCase()) ||
      (notification.purchaseOrder?.supplier.name || "")
        .toLowerCase()
        .includes(searchQuery.toLowerCase())
  );

  useEffect(() => {
    fetchNotifications(1);
  }, [activeTab, typeFilter]);

  const unreadCount = notifications.filter((n) => !n.isRead).length;

  return (
    <MainLayout>
      <PageHeader
        title="Notifications"
        description="Manage your notifications and stay updated"
        actions={
          <div className="flex gap-2">
            {unreadCount > 0 && (
              <Button variant="outline" onClick={markAllAsRead}>
                <CheckCheck className="h-4 w-4 mr-2" />
                Mark All Read ({unreadCount})
              </Button>
            )}
          </div>
        }
      />

      <div className="space-y-6">
        {/* Summary Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium text-muted-foreground">Total</span>
                <Bell className="h-4 w-4 text-muted-foreground" />
              </div>
              <div className="text-2xl font-bold">{pagination.total}</div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium text-muted-foreground">Unread</span>
                <AlertTriangle className="h-4 w-4 text-orange-500" />
              </div>
              <div className="text-2xl font-bold text-orange-600">{unreadCount}</div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium text-muted-foreground">PO Related</span>
                <Package className="h-4 w-4 text-blue-500" />
              </div>
              <div className="text-2xl font-bold text-blue-600">
                {notifications.filter((n) => n.type.startsWith("PURCHASE_ORDER")).length}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium text-muted-foreground">Read</span>
                <Check className="h-4 w-4 text-green-500" />
              </div>
              <div className="text-2xl font-bold text-green-600">
                {notifications.filter((n) => n.isRead).length}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Filters and Search */}
        <Card>
          <CardHeader>
            <CardTitle>Filters</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex flex-col md:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="Search notifications..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>

              <Select value={typeFilter} onValueChange={setTypeFilter}>
                <SelectTrigger className="w-full md:w-[200px]">
                  <SelectValue placeholder="Filter by type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="ALL">All Types</SelectItem>
                  <SelectItem value="PURCHASE_ORDER_APPROVAL">PO Approval</SelectItem>
                  <SelectItem value="PURCHASE_ORDER_APPROVED">PO Approved</SelectItem>
                  <SelectItem value="PURCHASE_ORDER_REJECTED">PO Rejected</SelectItem>
                  <SelectItem value="PURCHASE_ORDER_RECEIVED">PO Received</SelectItem>
                  <SelectItem value="SYSTEM">System</SelectItem>
                  <SelectItem value="ALERT">Alert</SelectItem>
                  <SelectItem value="MESSAGE">Message</SelectItem>
                  <SelectItem value="INFO">Info</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </CardContent>
        </Card>

        {/* Notifications List */}
        <Card>
          <CardHeader>
            <CardTitle>Notifications</CardTitle>
          </CardHeader>
          <CardContent>
            <Tabs value={activeTab} onValueChange={setActiveTab}>
              <TabsList className="grid w-full grid-cols-2">
                <TabsTrigger value="all">All Notifications</TabsTrigger>
                <TabsTrigger value="unread">
                  Unread {unreadCount > 0 && `(${unreadCount})`}
                </TabsTrigger>
              </TabsList>

              <TabsContent value={activeTab} className="mt-6">
                {loading ? (
                  <div className="flex justify-center items-center p-12">
                    <Loader2 className="h-8 w-8 animate-spin text-primary" />
                    <span className="ml-2">Loading notifications...</span>
                  </div>
                ) : error ? (
                  <div className="text-center p-12">
                    <AlertTriangle className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                    <h3 className="text-lg font-medium mb-2">Error Loading Notifications</h3>
                    <p className="text-muted-foreground mb-4">{error}</p>
                    <Button onClick={() => fetchNotifications(1)}>Try Again</Button>
                  </div>
                ) : filteredNotifications.length === 0 ? (
                  <div className="text-center p-12">
                    <Bell className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                    <h3 className="text-lg font-medium mb-2">No Notifications</h3>
                    <p className="text-muted-foreground">
                      {searchQuery
                        ? "No notifications match your search."
                        : "You're all caught up!"}
                    </p>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {filteredNotifications.map((notification) => (
                      <div
                        key={notification.id}
                        className={`border rounded-lg p-4 ${
                          !notification.isRead ? "bg-accent/20 border-primary/20" : ""
                        }`}
                      >
                        <div className="flex items-start justify-between mb-3">
                          <div className="flex items-center gap-2">
                            {getNotificationIcon(notification.type)}
                            <Badge variant={getNotificationBadgeColor(notification.type)}>
                              {notification.type
                                .replace("PURCHASE_ORDER_", "PO ")
                                .replace("_", " ")}
                            </Badge>
                            {!notification.isRead && (
                              <Badge variant="outline" className="text-xs">
                                New
                              </Badge>
                            )}
                          </div>
                          <span className="text-sm text-muted-foreground">
                            {format(new Date(notification.createdAt), "MMM d, yyyy 'at' h:mm a")}
                          </span>
                        </div>

                        <h3 className="font-medium mb-2">{notification.title}</h3>
                        <p className="text-muted-foreground mb-3">{notification.message}</p>

                        {/* Purchase Order Details */}
                        {notification.purchaseOrder && (
                          <div className="bg-muted/50 rounded p-3 mb-3">
                            <div className="flex items-center justify-between mb-2">
                              <span className="font-medium">
                                Purchase Order #
                                {notification.purchaseOrder.id.slice(-8).toUpperCase()}
                              </span>
                              <Badge variant="outline">
                                {notification.purchaseOrder.status.replace("_", " ")}
                              </Badge>
                            </div>
                            <div className="flex items-center justify-between text-sm text-muted-foreground">
                              <span>{notification.purchaseOrder.supplier.name}</span>
                              <span className="font-medium">
                                Rp{" "}
                                {Number(notification.purchaseOrder.total).toLocaleString("id-ID")}
                              </span>
                            </div>
                          </div>
                        )}

                        {/* Action Buttons */}
                        <div className="flex gap-2">
                          {notification.actionUrl && (
                            <Button variant="outline" size="sm" asChild>
                              <Link href={notification.actionUrl}>
                                <ExternalLink className="h-4 w-4 mr-2" />
                                View Details
                              </Link>
                            </Button>
                          )}
                          {!notification.isRead && (
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => markAsRead(notification.id)}
                            >
                              <Check className="h-4 w-4 mr-2" />
                              Mark as Read
                            </Button>
                          )}
                        </div>
                      </div>
                    ))}

                    {/* Pagination */}
                    {pagination.pages > 1 && (
                      <div className="flex justify-center items-center gap-2 mt-6">
                        <Button
                          variant="outline"
                          size="sm"
                          disabled={pagination.page === 1}
                          onClick={() => fetchNotifications(pagination.page - 1)}
                        >
                          Previous
                        </Button>
                        <span className="text-sm text-muted-foreground">
                          Page {pagination.page} of {pagination.pages}
                        </span>
                        <Button
                          variant="outline"
                          size="sm"
                          disabled={pagination.page === pagination.pages}
                          onClick={() => fetchNotifications(pagination.page + 1)}
                        >
                          Next
                        </Button>
                      </div>
                    )}
                  </div>
                )}
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>
      </div>
    </MainLayout>
  );
}
