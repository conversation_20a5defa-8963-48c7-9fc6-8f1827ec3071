import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/auth";
import { jwtVerify } from "jose";
import { z } from "zod";

// Helper function to verify the authentication token
async function verifyAuthToken(request: NextRequest) {
  // Get the session token from cookies
  const token = request.cookies.get("session-token");

  // Check if token exists
  if (!token) {
    console.log("[API] /api/suppliers/[id] - No session token found");
    return {
      authenticated: false,
      error: "Unauthorized - No session token",
      status: 403,
      user: null
    };
  }

  // Verify the token
  try {
    const { payload } = await jwtVerify(
      token.value,
      new TextEncoder().encode(process.env.NEXTAUTH_SECRET || "fallback-secret")
    );

    return {
      authenticated: true,
      user: {
        id: payload.id as string,
        name: payload.name as string,
        email: payload.email as string,
        role: payload.role as string
      }
    };
  } catch (error) {
    console.error("[API] /api/suppliers/[id] - JWT verification failed:", error);
    return {
      authenticated: false,
      error: "Unauthorized - Invalid token",
      status: 403,
      user: null
    };
  }
}

// Supplier update schema for validation
const supplierUpdateSchema = z.object({
  name: z.string().min(2, { message: "Name must be at least 2 characters" }).optional(),
  contactPerson: z.string().optional().nullable(),
  phone: z.string().optional().nullable(),
  email: z.string().optional().nullable().refine(
    (val) => val === null || val === undefined || val === "" || /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(val),
    { message: "Invalid email address" }
  ),
  address: z.string().optional().nullable(),
});

// GET /api/suppliers/[id] - Get a specific supplier
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication
    const auth = await verifyAuthToken(request);

    if (!auth.authenticated) {
      return NextResponse.json(
        { error: auth.error },
        { status: auth.status }
      );
    }

    // Get supplier
    const supplier = await prisma.supplier.findUnique({
      where: { id: params.id },
      include: {
        _count: {
          select: { products: true, purchaseOrders: true }
        }
      },
    });

    // Check if supplier exists
    if (!supplier) {
      return NextResponse.json(
        { error: "Supplier not found" },
        { status: 404 }
      );
    }

    return NextResponse.json({ supplier });
  } catch (error) {
    console.error("Error fetching supplier:", error);
    return NextResponse.json(
      { error: "Failed to fetch supplier", message: (error as Error).message },
      { status: 500 }
    );
  }
}

// PATCH /api/suppliers/[id] - Update a supplier
export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication
    const auth = await verifyAuthToken(request);

    if (!auth.authenticated) {
      return NextResponse.json(
        { error: auth.error },
        { status: auth.status }
      );
    }

    // Check if user has permission to update suppliers
    const hasPermission = ["SUPER_ADMIN", "WAREHOUSE_ADMIN"].includes(auth.user.role);
    if (!hasPermission) {
      return NextResponse.json(
        { error: "Unauthorized - Insufficient permissions" },
        { status: 403 }
      );
    }

    // Get supplier
    const existingSupplier = await prisma.supplier.findUnique({
      where: { id: params.id },
    });

    // Check if supplier exists
    if (!existingSupplier) {
      return NextResponse.json(
        { error: "Supplier not found" },
        { status: 404 }
      );
    }

    // Get request body
    const body = await request.json();

    // Validate supplier data
    const validationResult = supplierUpdateSchema.safeParse(body);
    if (!validationResult.success) {
      return NextResponse.json(
        { error: "Validation failed", issues: validationResult.error.issues },
        { status: 400 }
      );
    }

    const supplierData = validationResult.data;

    // Track changes for activity log
    const changes: string[] = [];
    if (supplierData.name && supplierData.name !== existingSupplier.name) {
      changes.push(`name: ${existingSupplier.name} → ${supplierData.name}`);
    }
    if (supplierData.contactPerson !== undefined && supplierData.contactPerson !== existingSupplier.contactPerson) {
      changes.push(`contact person updated`);
    }
    if (supplierData.phone !== undefined && supplierData.phone !== existingSupplier.phone) {
      changes.push(`phone updated`);
    }
    if (supplierData.email !== undefined && supplierData.email !== existingSupplier.email) {
      changes.push(`email updated`);
    }
    if (supplierData.address !== undefined && supplierData.address !== existingSupplier.address) {
      changes.push(`address updated`);
    }

    // Update supplier
    const supplier = await prisma.supplier.update({
      where: { id: params.id },
      data: supplierData,
    });

    // Log activity
    if (changes.length > 0) {
      await prisma.activityLog.create({
        data: {
          userId: auth.user.id,
          action: "UPDATE_SUPPLIER",
          details: `Updated supplier: ${supplier.name} (${changes.join(", ")})`,
        },
      });
    }

    return NextResponse.json({ supplier });
  } catch (error) {
    console.error("Error updating supplier:", error);
    return NextResponse.json(
      { error: "Failed to update supplier", message: (error as Error).message },
      { status: 500 }
    );
  }
}

// DELETE /api/suppliers/[id] - Delete a supplier
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication
    const auth = await verifyAuthToken(request);

    if (!auth.authenticated) {
      return NextResponse.json(
        { error: auth.error },
        { status: auth.status }
      );
    }

    // Check if user has permission to delete suppliers
    const hasPermission = ["SUPER_ADMIN", "WAREHOUSE_ADMIN"].includes(auth.user.role);
    if (!hasPermission) {
      return NextResponse.json(
        { error: "Unauthorized - Insufficient permissions" },
        { status: 403 }
      );
    }

    // Get supplier
    const supplier = await prisma.supplier.findUnique({
      where: { id: params.id },
      include: {
        _count: {
          select: { products: true, purchaseOrders: true }
        }
      },
    });

    // Check if supplier exists
    if (!supplier) {
      return NextResponse.json(
        { error: "Supplier not found" },
        { status: 404 }
      );
    }

    // Check if supplier is used in products or purchase orders
    if (supplier._count.products > 0 || supplier._count.purchaseOrders > 0) {
      return NextResponse.json(
        {
          error: "Cannot delete supplier",
          message: "This supplier is associated with products or purchase orders and cannot be deleted."
        },
        { status: 400 }
      );
    }

    // Delete supplier
    await prisma.supplier.delete({
      where: { id: params.id },
    });

    // Log activity
    await prisma.activityLog.create({
      data: {
        userId: auth.user.id,
        action: "DELETE_SUPPLIER",
        details: `Deleted supplier: ${supplier.name}`,
      },
    });

    return NextResponse.json({
      success: true,
      message: "Supplier deleted successfully",
    });
  } catch (error) {
    console.error("Error deleting supplier:", error);
    return NextResponse.json(
      { error: "Failed to delete supplier", message: (error as Error).message },
      { status: 500 }
    );
  }
}
