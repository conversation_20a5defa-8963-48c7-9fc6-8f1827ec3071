import { NextRequest, NextResponse } from "next/server";
import { PrismaClient } from "@/generated/prisma";
import * as bcrypt from "bcryptjs";

const prisma = new PrismaClient();

// POST /api/setup - Create initial users and data
export async function POST(request: NextRequest) {
  try {
    // Check if setup key is provided
    const { searchParams } = new URL(request.url);
    const setupKey = searchParams.get("key");

    // Simple security check - require a setup key
    if (setupKey !== "setup-npos-system") {
      return NextResponse.json(
        { error: "Invalid setup key" },
        { status: 403 }
      );
    }

    // Create a super admin user
    const hashedPassword = await bcrypt.hash("admin123", 10);
    
    const superAdmin = await prisma.user.upsert({
      where: { email: "<EMAIL>" },
      update: {},
      create: {
        name: "Super Admin",
        email: "<EMAIL>",
        password: hashedPassword,
        role: "SUPER_ADMIN",
        active: true,
      },
    });
    
    // Create a cashier user
    const cashierPassword = await bcrypt.hash("cashier123", 10);
    
    const cashier = await prisma.user.upsert({
      where: { email: "<EMAIL>" },
      update: {},
      create: {
        name: "Cashier User",
        email: "<EMAIL>",
        password: cashierPassword,
        role: "CASHIER",
        active: true,
      },
    });
    
    // Create a finance admin user
    const financePassword = await bcrypt.hash("finance123", 10);
    
    const financeAdmin = await prisma.user.upsert({
      where: { email: "<EMAIL>" },
      update: {},
      create: {
        name: "Finance Admin",
        email: "<EMAIL>",
        password: financePassword,
        role: "FINANCE_ADMIN",
        active: true,
      },
    });
    
    // Create a developer user
    const developerPassword = await bcrypt.hash("developer123", 10);
    
    const developer = await prisma.user.upsert({
      where: { email: "<EMAIL>" },
      update: {},
      create: {
        name: "Developer",
        email: "<EMAIL>",
        password: developerPassword,
        role: "DEVELOPER",
        active: true,
      },
    });

    // Create a cash drawer (skip for now due to schema constraints)
    let cashDrawer = null;
    try {
      cashDrawer = await prisma.cashDrawer.findFirst({
        where: { name: "Main Drawer" }
      });
      if (!cashDrawer) {
        console.log("Skipping cash drawer creation due to schema constraints");
      }
    } catch (error) {
      console.log("Skipping cash drawer creation:", error);
    }

    // Create some basic system settings
    const settings = [
      { key: "STORE_NAME", value: "Next POS Store", description: "Store name displayed in receipts and UI" },
      { key: "STORE_ADDRESS", value: "123 Main St, City, Country", description: "Store address displayed in receipts" },
      { key: "STORE_PHONE", value: "****** 567 8900", description: "Store phone number" },
      { key: "CURRENCY_SYMBOL", value: "$", description: "Currency symbol used throughout the application" },
      { key: "TAX_RATE", value: "7.5", description: "Default tax rate percentage" },
      { key: "RECEIPT_FOOTER", value: "Thank you for shopping with us!", description: "Message displayed at the bottom of receipts" },
      { key: "ENABLE_CHAT", value: "true", description: "Enable or disable the chat feature" },
    ];

    for (const setting of settings) {
      await prisma.systemSetting.upsert({
        where: { key: setting.key },
        update: { value: setting.value },
        create: {
          key: setting.key,
          value: setting.value,
          description: setting.description,
        },
      });
    }

    return NextResponse.json({
      success: true,
      message: "System setup completed successfully",
      users: {
        superAdmin: superAdmin.email,
        cashier: cashier.email,
        financeAdmin: financeAdmin.email,
        developer: developer.email,
      },
      cashDrawer: cashDrawer?.name || "Not created",
      settingsCount: settings.length,
    });
  } catch (error) {
    console.error("Error during system setup:", error);
    return NextResponse.json(
      { error: "Failed to set up system", message: (error as Error).message },
      { status: 500 }
    );
  }
}
