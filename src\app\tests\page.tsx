"use client";

import Link from "next/link";
import {
  Card,
  CardContent,
  CardDescription,
  Card<PERSON>ooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { ArrowRight, Database, Package, Layers, Activity } from "lucide-react";

export default function TestsPage() {
  return (
    <div className="container mx-auto py-6">
      <h1 className="text-3xl font-bold mb-2">API Tests</h1>
      <p className="text-muted-foreground mb-6">Test various API routes in the application</p>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Package className="mr-2 h-5 w-5" />
              Inventory API Tests
            </CardTitle>
            <CardDescription>Test the inventory management API routes</CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-sm">
              Run tests for store stock management, adjustments, transfers, and inventory reports.
            </p>
          </CardContent>
          <CardFooter>
            <Link href="/tests/inventory-api" passHref>
              <Button className="bg-black text-white hover:bg-gray-800">
                Run Tests
                <ArrowRight className="ml-2 h-4 w-4" />
              </Button>
            </Link>
          </CardFooter>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Database className="mr-2 h-5 w-5" />
              Product API Tests
            </CardTitle>
            <CardDescription>Test the product management API routes</CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-sm">
              Run tests for product CRUD operations, categories, and pricing.
            </p>
          </CardContent>
          <CardFooter>
            <Link href="/tests/product-api" passHref>
              <Button className="bg-black text-white hover:bg-gray-800">
                Run Tests
                <ArrowRight className="ml-2 h-4 w-4" />
              </Button>
            </Link>
          </CardFooter>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Layers className="mr-2 h-5 w-5" />
              User API Tests
            </CardTitle>
            <CardDescription>Test the user management API routes</CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-sm">
              Run tests for user authentication, permissions, and profile management.
            </p>
          </CardContent>
          <CardFooter>
            <Link href="/tests/user-api" passHref>
              <Button className="bg-black text-white hover:bg-gray-800">
                Run Tests
                <ArrowRight className="ml-2 h-4 w-4" />
              </Button>
            </Link>
          </CardFooter>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Activity className="mr-2 h-5 w-5" />
              Transaction API Tests
            </CardTitle>
            <CardDescription>Test the transaction management API routes</CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-sm">
              Run tests for sales transactions, returns, and payment processing.
            </p>
          </CardContent>
          <CardFooter>
            <Link href="/tests/transaction-api" passHref>
              <Button className="bg-black text-white hover:bg-gray-800">
                Run Tests
                <ArrowRight className="ml-2 h-4 w-4" />
              </Button>
            </Link>
          </CardFooter>
        </Card>
      </div>
    </div>
  );
}
