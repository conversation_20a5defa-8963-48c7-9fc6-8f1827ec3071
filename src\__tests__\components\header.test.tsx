import React from "react";
import { render, screen, fireEvent } from "@testing-library/react";
import { Header } from "@/components/layout/Header";

// Import the mocked functions
const { signOut } = require("@/auth");

// Mock the useAuth hook
jest.mock("@/hooks/use-auth", () => ({
  useAuth: jest.fn(),
}));

describe("Header Component", () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("renders loading state when authentication is loading", () => {
    // Mock loading state
    (useAuth as jest.Mock).mockReturnValue({
      user: null,
      loading: true,
    });

    render(<Header />);

    // Should show loading indicator
    expect(screen.getByTestId("user-loading")).toBeInTheDocument();
  });

  it("renders login button when user is not authenticated", () => {
    // Mock unauthenticated state
    (useAuth as jest.Mock).mockReturnValue({
      user: null,
      loading: false,
    });

    render(<Header />);

    // Should show login button
    expect(screen.getByText(/Login/i)).toBeInTheDocument();
    expect(screen.getByRole("link", { name: /Login/i })).toHaveAttribute("href", "/login");
  });

  it("renders user profile when authenticated", () => {
    // Mock authenticated user
    (useAuth as jest.Mock).mockReturnValue({
      user: {
        id: "1",
        name: "Test User",
        email: "<EMAIL>",
        role: "CASHIER",
      },
      loading: false,
    });

    render(<Header />);

    // Should show user name
    expect(screen.getByText("Test User")).toBeInTheDocument();

    // User menu should be closed initially
    expect(screen.queryByText("<EMAIL>")).not.toBeInTheDocument();

    // Open user menu
    fireEvent.click(screen.getByText("Test User"));

    // User menu should now be open
    expect(screen.getByText("<EMAIL>")).toBeInTheDocument();
    expect(screen.getByText("CASHIER")).toBeInTheDocument();
    expect(screen.getByText("Logout")).toBeInTheDocument();
  });

  it("calls signOut when logout button is clicked", () => {
    // Mock authenticated user
    (useAuth as jest.Mock).mockReturnValue({
      user: {
        id: "1",
        name: "Test User",
        email: "<EMAIL>",
        role: "CASHIER",
      },
      loading: false,
    });

    render(<Header />);

    // Open user menu
    fireEvent.click(screen.getByText("Test User"));

    // Click logout button
    fireEvent.click(screen.getByText("Logout"));

    // signOut should be called
    expect(signOut).toHaveBeenCalledWith({ redirectTo: "/login" });
  });

  it("displays user initials in the avatar", () => {
    // Mock authenticated user with multi-word name
    (useAuth as jest.Mock).mockReturnValue({
      user: {
        id: "1",
        name: "John Doe",
        email: "<EMAIL>",
        role: "SUPER_ADMIN",
      },
      loading: false,
    });

    render(<Header />);

    // Should show user initials
    expect(screen.getByText("JD")).toBeInTheDocument();
  });
});
