# Enhanced Customer Resolution with Stock Validation

## Overview

The enhanced Customer Resolution workflow now includes comprehensive stock validation to handle insufficient stock scenarios for product replacements. This ensures that replacement promises are only made when products are actually available in inventory.

## Key Features

### 1. **Pre-Dialog Stock Validation**
- Automatic stock level checking before displaying resolution options
- Real-time stock availability assessment for all returned products
- Loading states during stock validation process

### 2. **Dynamic Radio Button States**
- **Sufficient Stock**: "Product Replacement" option enabled normally
- **Insufficient Stock**: "Product Replacement" option disabled with explanatory text
- **New Option**: "Schedule Replacement (Wait for Restock)" for insufficient stock scenarios

### 3. **Stock Availability Display**
- Current stock levels shown for each returned product
- Clear visual indicators (green/red) for stock sufficiency
- Required vs available quantity comparison
- Overall stock status summary

### 4. **Enhanced Resolution Status Management**
- **PENDING_REPLACEMENT**: New enum value for scheduled replacements
- **awaitingRestock**: Boolean field to track pending replacement status
- Persistent Customer Resolution button for pending replacements

### 5. **Intelligent Button Behavior**
- Button text changes to "Update Resolution" for pending replacements
- Re-checking stock levels when dialog reopens
- Dynamic option availability based on current stock

## Database Schema Changes

### New Enum Value
```sql
ALTER TYPE "CustomerResolution" ADD VALUE 'PENDING_REPLACEMENT';
```

### New Field
```sql
ALTER TABLE "Return" ADD COLUMN "awaitingRestock" BOOLEAN NOT NULL DEFAULT false;
```

## API Enhancements

### 1. Stock Check Endpoint
**GET** `/api/returns/[id]/stock-check`

**Response Structure**:
```json
{
  "returnId": "string",
  "items": [
    {
      "productId": "string",
      "productName": "string",
      "productSku": "string",
      "unitName": "string",
      "unitAbbreviation": "string",
      "returnedQuantity": 1,
      "currentStock": 0,
      "requiredStock": 1,
      "availableStock": 0,
      "hasSufficientStock": false,
      "stockShortage": 1,
      "stockStatus": "INSUFFICIENT"
    }
  ],
  "overallStockStatus": "INSUFFICIENT",
  "canProcessReplacement": false,
  "totalItemsChecked": 1,
  "itemsWithSufficientStock": 0,
  "itemsWithInsufficientStock": 1,
  "summary": {
    "canProcessImmediateReplacement": false,
    "requiresRestocking": true,
    "totalShortageItems": 1,
    "message": "All items have insufficient stock for immediate replacement"
  },
  "checkedAt": "2025-01-07T06:09:00.000Z"
}
```

### 2. Enhanced Customer Resolution Endpoint
**POST** `/api/returns/[id]/customer-resolution`

**New Request Body**:
```json
{
  "resolution": "REPLACEMENT" | "REFUND" | "PENDING_REPLACEMENT",
  "notes": "string (optional)"
}
```

**Enhanced Features**:
- Stock validation before processing REPLACEMENT
- Automatic `awaitingRestock` flag setting for PENDING_REPLACEMENT
- Allows updating existing PENDING_REPLACEMENT resolutions
- Comprehensive error handling for stock validation failures

## UI/UX Enhancements

### 1. Stock Check Loading State
```jsx
{stockCheckLoading && (
  <div className="flex items-center gap-2 p-3 bg-blue-50 border border-blue-200 rounded-lg">
    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
    <span className="text-sm text-blue-700">Checking stock availability...</span>
  </div>
)}
```

### 2. Stock Summary Display
- Product-by-product stock breakdown
- Visual indicators for stock sufficiency
- Clear messaging about overall stock status

### 3. Dynamic Radio Button Options
- Conditional enabling/disabling based on stock availability
- Explanatory text for disabled options
- New "Schedule Replacement" option for insufficient stock

### 4. Enhanced Status Display
- "Pending Replacement" badge in yellow color scheme
- Clear differentiation from completed replacements
- Timestamp tracking for all resolution actions

## Business Logic

### Stock Validation Rules
1. **Sufficient Stock**: `currentStock >= returnedQuantity` for all items
2. **Insufficient Stock**: Any item has `currentStock < returnedQuantity`
3. **Mixed Stock**: Some items sufficient, others insufficient

### Resolution Processing Logic
1. **REPLACEMENT**: Requires sufficient stock validation before processing
2. **PENDING_REPLACEMENT**: No immediate stock changes, sets awaiting flag
3. **REFUND**: No stock validation required, processes transaction void

### Update Workflow
1. **Pending to Replacement**: Re-validates stock, processes if sufficient
2. **Pending to Refund**: Processes refund, clears awaiting flag
3. **Stock Restoration**: Allows updating pending replacements when stock available

## Error Handling

### Stock Validation Errors
- Clear error messages for insufficient stock
- Graceful handling of products that no longer exist
- Proper validation of stock changes between dialog open and submission

### API Error Responses
```json
{
  "error": "Insufficient stock for replacement of Product Name. Available: 0, Required: 1"
}
```

## Testing Scenarios

### Test Case 1: Sufficient Stock Scenario
1. Return with items having sufficient stock
2. Verify "Product Replacement" option enabled
3. Process replacement successfully
4. Verify stock deduction and completion

### Test Case 2: Insufficient Stock Scenario
1. Return with items having insufficient stock
2. Verify "Product Replacement" option disabled
3. Verify "Schedule Replacement" option available
4. Process pending replacement
5. Verify status and button text changes

### Test Case 3: Stock Update Scenario
1. Return with pending replacement status
2. Restore stock levels
3. Click "Update Resolution"
4. Verify "Product Replacement" now enabled
5. Process immediate replacement

### Test Case 4: Mixed Stock Scenario
1. Return with multiple items (some sufficient, some insufficient)
2. Verify overall insufficient status
3. Verify appropriate option availability

## Integration Points

### 1. Inventory Management
- Real-time stock level checking
- Integration with StoreStock model
- Proper unit display and calculations

### 2. Transaction Processing
- Stock deduction for immediate replacements
- Transaction voiding for refunds
- Audit trail maintenance

### 3. User Interface
- Consistent design with existing patterns
- Responsive layout for stock information
- Clear visual feedback for all states

## Performance Considerations

### 1. Stock Check Optimization
- Single API call for all return items
- Efficient database queries with proper includes
- Caching considerations for frequently accessed data

### 2. UI Responsiveness
- Loading states during stock validation
- Debounced stock checks to prevent excessive API calls
- Proper error boundaries for graceful degradation

## Security & Permissions

### Role-Based Access Control
- **SUPER_ADMIN**: Full access to all resolution options
- **WAREHOUSE_ADMIN**: Can process replacements and pending replacements
- **FINANCE_ADMIN**: Can process refunds and view stock information
- **CASHIER**: Read-only access to resolution status

### Data Validation
- Comprehensive input validation using Zod schemas
- Stock level validation before processing
- Business rule enforcement at API level

## Future Enhancements

### Potential Improvements
1. **Automatic Stock Notifications**: Alert when stock becomes available for pending replacements
2. **Batch Processing**: Handle multiple pending replacements when stock is restored
3. **Stock Reservation**: Reserve stock for pending replacements
4. **Advanced Analytics**: Track replacement success rates and stock-out impacts
5. **Integration with Purchase Orders**: Automatic reorder suggestions for frequently insufficient items

## Conclusion

The enhanced Customer Resolution workflow provides a robust solution for handling stock-constrained replacement scenarios while maintaining excellent user experience and data integrity. The implementation ensures that customer promises align with actual inventory capabilities, reducing operational issues and improving customer satisfaction.
