import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/auth";
import { verifyAuthToken } from "@/lib/auth-utils";

// GET /api/users/count - Get the count of active users
export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const auth = await verifyAuthToken(request);

    if (!auth.authenticated) {
      return NextResponse.json(
        { error: auth.error },
        { status: auth.status }
      );
    }

    // Get total count of users
    const totalUsers = await prisma.user.count();
    
    // Get count of active users
    const activeUsers = await prisma.user.count({
      where: { active: true }
    });

    return NextResponse.json({ 
      total: totalUsers,
      active: activeUsers
    });
  } catch (error) {
    console.error("Error fetching user count:", error);
    return NextResponse.json(
      { error: "Failed to fetch user count", message: (error as Error).message },
      { status: 500 }
    );
  }
}
