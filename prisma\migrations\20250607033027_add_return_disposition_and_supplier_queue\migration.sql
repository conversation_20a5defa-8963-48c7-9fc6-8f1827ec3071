-- Create<PERSON>num
CREATE TYPE "ReturnDisposition" AS ENUM ('RETURN_TO_STOCK', 'DO_NOT_RETURN_TO_STOCK');

-- CreateEnum
CREATE TYPE "RevenueTargetType" AS ENUM ('DAILY', 'WEEKLY', 'MONTHLY', 'QUARTERLY', 'YEARLY');

-- DropForeignKey
ALTER TABLE "SupplierReturn" DROP CONSTRAINT "SupplierReturn_purchaseOrderId_fkey";

-- DropIndex
DROP INDEX "CashAuditAlert_alertType_idx";

-- DropIndex
DROP INDEX "CashAuditAlert_createdAt_idx";

-- DropIndex
DROP INDEX "CashAuditAlert_isResolved_idx";

-- DropIndex
DROP INDEX "CashAuditAlert_severity_idx";

-- DropIndex
DROP INDEX "CashReconciliation_businessDate_idx";

-- DropIndex
DROP INDEX "CashReconciliation_discrepancyCategory_idx";

-- DropIndex
DROP INDEX "CashReconciliation_resolutionStatus_idx";

-- DropIndex
DROP INDEX "DrawerSession_terminalId_status_key";

-- DropIndex
DROP INDEX "DrawerSession_userId_status_key";

-- DropIndex
DROP INDEX "StoreInfo_id_key";

-- AlterTable
ALTER TABLE "Return" ADD COLUMN     "disposition" "ReturnDisposition",
ADD COLUMN     "dispositionReason" TEXT,
ADD COLUMN     "supplierReturnQueueId" TEXT;

-- AlterTable
ALTER TABLE "StoreInfo" ALTER COLUMN "id" SET DEFAULT 'default-store';

-- AlterTable
ALTER TABLE "SupplierReturn" ALTER COLUMN "purchaseOrderId" DROP NOT NULL;

-- CreateTable
CREATE TABLE "RevenueTarget" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "targetType" "RevenueTargetType" NOT NULL DEFAULT 'MONTHLY',
    "startDate" DATE NOT NULL,
    "endDate" DATE NOT NULL,
    "amount" DECIMAL(12,2) NOT NULL,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "createdBy" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "RevenueTarget_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "RevenueTarget_startDate_endDate_targetType_key" ON "RevenueTarget"("startDate", "endDate", "targetType");

-- AddForeignKey
ALTER TABLE "Return" ADD CONSTRAINT "Return_supplierReturnQueueId_fkey" FOREIGN KEY ("supplierReturnQueueId") REFERENCES "SupplierReturn"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ReturnItem" ADD CONSTRAINT "ReturnItem_productId_fkey" FOREIGN KEY ("productId") REFERENCES "Product"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "SupplierReturn" ADD CONSTRAINT "SupplierReturn_purchaseOrderId_fkey" FOREIGN KEY ("purchaseOrderId") REFERENCES "PurchaseOrder"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "SupplierReturnItem" ADD CONSTRAINT "SupplierReturnItem_productId_fkey" FOREIGN KEY ("productId") REFERENCES "Product"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "RevenueTarget" ADD CONSTRAINT "RevenueTarget_createdBy_fkey" FOREIGN KEY ("createdBy") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
