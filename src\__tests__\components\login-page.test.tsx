import React from "react";
import { render, screen, fireEvent, waitFor } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import LoginPage from "@/app/login/page";

// Import the mocked functions
const { signIn } = require("@/auth");

describe("LoginPage", () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("renders the login form", () => {
    render(<LoginPage />);

    // Check for form elements
    expect(screen.getByText(/Sign in to your account/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/Email/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/Password/i)).toBeInTheDocument();
    expect(screen.getByRole("button", { name: /Sign in/i })).toBeInTheDocument();
  });

  it("validates email format", async () => {
    render(<LoginPage />);

    // Get form elements
    const emailInput = screen.getByLabelText(/Email/i);
    const submitButton = screen.getByRole("button", { name: /Sign in/i });

    // Type invalid email
    await userEvent.type(emailInput, "invalid-email");

    // Submit form
    fireEvent.click(submitButton);

    // Check for validation error
    await waitFor(() => {
      expect(screen.getByText(/Please enter a valid email address/i)).toBeInTheDocument();
    });
  });

  it("validates password length", async () => {
    render(<LoginPage />);

    // Get form elements
    const emailInput = screen.getByLabelText(/Email/i);
    const passwordInput = screen.getByLabelText(/Password/i);
    const submitButton = screen.getByRole("button", { name: /Sign in/i });

    // Type valid email but short password
    await userEvent.type(emailInput, "<EMAIL>");
    await userEvent.type(passwordInput, "12345");

    // Submit form
    fireEvent.click(submitButton);

    // Check for validation error
    await waitFor(() => {
      expect(screen.getByText(/Password must be at least 6 characters/i)).toBeInTheDocument();
    });
  });

  it("submits the form with valid data", async () => {
    // Mock successful sign in
    (signIn as jest.Mock).mockResolvedValue({ error: null });

    render(<LoginPage />);

    // Get form elements
    const emailInput = screen.getByLabelText(/Email/i);
    const passwordInput = screen.getByLabelText(/Password/i);
    const submitButton = screen.getByRole("button", { name: /Sign in/i });

    // Type valid credentials
    await userEvent.type(emailInput, "<EMAIL>");
    await userEvent.type(passwordInput, "password123");

    // Submit form
    fireEvent.click(submitButton);

    // Check if signIn was called with correct parameters
    await waitFor(() => {
      expect(signIn).toHaveBeenCalledWith("credentials", {
        email: "<EMAIL>",
        password: "password123",
        redirect: false,
      });
    });
  });

  it("shows error message on failed login", async () => {
    // Mock failed sign in
    (signIn as jest.Mock).mockResolvedValue({ error: "Invalid credentials" });

    render(<LoginPage />);

    // Get form elements
    const emailInput = screen.getByLabelText(/Email/i);
    const passwordInput = screen.getByLabelText(/Password/i);
    const submitButton = screen.getByRole("button", { name: /Sign in/i });

    // Type credentials
    await userEvent.type(emailInput, "<EMAIL>");
    await userEvent.type(passwordInput, "wrongpassword");

    // Submit form
    fireEvent.click(submitButton);

    // Check for error message
    await waitFor(() => {
      expect(screen.getByText(/Invalid email or password/i)).toBeInTheDocument();
    });
  });

  it("toggles password visibility when the toggle button is clicked", async () => {
    render(<LoginPage />);

    // Get password input and toggle button
    const passwordInput = screen.getByLabelText(/Password/i);
    const toggleButton = screen.getByRole("button", { name: /Show password/i });

    // Initially password should be hidden
    expect(passwordInput).toHaveAttribute("type", "password");

    // Click the toggle button to show password
    fireEvent.click(toggleButton);

    // Password should now be visible
    expect(passwordInput).toHaveAttribute("type", "text");

    // Toggle button should now show the "hide" icon
    const hideButton = screen.getByRole("button", { name: /Hide password/i });
    expect(hideButton).toBeInTheDocument();

    // Click again to hide password
    fireEvent.click(hideButton);

    // Password should be hidden again
    expect(passwordInput).toHaveAttribute("type", "password");
  });
});
