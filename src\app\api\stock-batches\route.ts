import { NextRequest, NextResponse } from "next/server";
import { z } from "zod";
import { prisma } from "@/lib/prisma";
import { verifyAuthToken } from "@/lib/auth-utils";

// GET /api/stock-batches - Get stock batches with filtering
export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const auth = await verifyAuthToken(request);

    if (!auth.authenticated) {
      return NextResponse.json(
        { error: auth.error },
        { status: auth.status }
      );
    }

    // Get query parameters
    const url = new URL(request.url);
    const productId = url.searchParams.get("productId");
    const supplierId = url.searchParams.get("supplierId");
    const status = url.searchParams.get("status");
    const location = url.searchParams.get("location"); // 'warehouse' or 'store'
    const expiringDays = url.searchParams.get("expiringDays"); // Get batches expiring in X days
    const page = parseInt(url.searchParams.get("page") || "1");
    const limit = parseInt(url.searchParams.get("limit") || "20");
    const skip = (page - 1) * limit;

    // Build filter conditions
    const where: any = {};

    if (productId) {
      where.productId = productId;
    }

    if (supplierId) {
      where.productSupplier = {
        supplierId: supplierId
      };
    }

    if (status) {
      where.status = status;
    }

    if (location === 'warehouse') {
      where.warehouseStockId = { not: null };
      where.storeStockId = null;
    } else if (location === 'store') {
      where.storeStockId = { not: null };
      where.warehouseStockId = null;
    }

    // Filter by expiring batches
    if (expiringDays) {
      const days = parseInt(expiringDays);
      if (!isNaN(days)) {
        const futureDate = new Date();
        futureDate.setDate(futureDate.getDate() + days);
        
        where.expiryDate = {
          lte: futureDate,
          gte: new Date() // Not already expired
        };
        where.status = 'ACTIVE'; // Only active batches
      }
    }

    // Get stock batches with pagination
    const [stockBatches, total] = await Promise.all([
      prisma.stockBatch.findMany({
        where,
        include: {
          product: {
            select: {
              id: true,
              name: true,
              sku: true,
              basePrice: true,
            }
          },
          productSupplier: {
            include: {
              supplier: {
                select: {
                  id: true,
                  name: true,
                  contactPerson: true,
                }
              }
            }
          },
          purchaseOrder: {
            select: {
              id: true,
              orderDate: true,
              status: true,
            }
          },
          warehouseStock: {
            select: {
              id: true,
              quantity: true,
            }
          },
          storeStock: {
            select: {
              id: true,
              quantity: true,
            }
          }
        },
        skip,
        take: limit,
        orderBy: [
          { expiryDate: 'asc' }, // Expiring first
          { receivedDate: 'desc' }, // Then newest first
        ],
      }),
      prisma.stockBatch.count({ where }),
    ]);

    // Calculate summary statistics
    const stats = {
      totalBatches: total,
      activeBatches: stockBatches.filter(batch => batch.status === 'ACTIVE').length,
      expiringBatches: stockBatches.filter(batch => {
        if (!batch.expiryDate || batch.status !== 'ACTIVE') return false;
        const daysUntilExpiry = Math.ceil((batch.expiryDate.getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24));
        return daysUntilExpiry <= 30; // Expiring in 30 days
      }).length,
      totalValue: stockBatches.reduce((sum, batch) => 
        sum + (Number(batch.remainingQuantity) * Number(batch.purchasePrice)), 0
      ),
      totalQuantity: stockBatches.reduce((sum, batch) => 
        sum + Number(batch.remainingQuantity), 0
      ),
    };

    return NextResponse.json({
      stockBatches,
      stats,
      pagination: {
        total,
        page,
        limit,
        pages: Math.ceil(total / limit),
      },
    });
  } catch (error) {
    console.error("Error fetching stock batches:", error);
    return NextResponse.json(
      { error: "Failed to fetch stock batches", message: (error as Error).message },
      { status: 500 }
    );
  }
}

// POST /api/stock-batches - Create a new stock batch (manual entry)
export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const auth = await verifyAuthToken(request);

    if (!auth.authenticated) {
      return NextResponse.json(
        { error: auth.error },
        { status: auth.status }
      );
    }

    // Check permissions
    const hasPermission = ["SUPER_ADMIN", "WAREHOUSE_ADMIN"].includes(auth.user.role);
    if (!hasPermission) {
      return NextResponse.json(
        { error: "Unauthorized - Insufficient permissions" },
        { status: 403 }
      );
    }

    // Get request body
    const body = await request.json();

    // Validate data
    const stockBatchSchema = z.object({
      productId: z.string().min(1, { message: "Product ID is required" }),
      productSupplierId: z.string().min(1, { message: "Product Supplier ID is required" }),
      batchNumber: z.string().optional(),
      expiryDate: z.string().optional().transform(val => val ? new Date(val) : null),
      quantity: z.number().positive({ message: "Quantity must be positive" }),
      purchasePrice: z.number().positive({ message: "Purchase price must be positive" }),
      location: z.enum(['warehouse', 'store']).default('warehouse'),
      notes: z.string().optional(),
    });

    const validationResult = stockBatchSchema.safeParse(body);
    if (!validationResult.success) {
      return NextResponse.json(
        { error: "Validation failed", issues: validationResult.error.issues },
        { status: 400 }
      );
    }

    const data = validationResult.data;

    // Verify product and supplier relationship exists
    const productSupplier = await prisma.productSupplier.findUnique({
      where: { id: data.productSupplierId },
      include: {
        product: { select: { id: true, name: true } },
        supplier: { select: { id: true, name: true } }
      }
    });

    if (!productSupplier) {
      return NextResponse.json(
        { error: "Product-supplier relationship not found" },
        { status: 404 }
      );
    }

    // Get or create stock location
    let warehouseStockId = null;
    let storeStockId = null;

    if (data.location === 'warehouse') {
      const warehouseStock = await prisma.warehouseStock.upsert({
        where: { productId: data.productId },
        update: {
          quantity: { increment: data.quantity },
          lastUpdated: new Date(),
        },
        create: {
          productId: data.productId,
          quantity: data.quantity,
          minThreshold: 0,
          lastUpdated: new Date(),
        },
      });
      warehouseStockId = warehouseStock.id;
    } else {
      const storeStock = await prisma.storeStock.upsert({
        where: { productId: data.productId },
        update: {
          quantity: { increment: data.quantity },
          lastUpdated: new Date(),
        },
        create: {
          productId: data.productId,
          quantity: data.quantity,
          minThreshold: 0,
          lastUpdated: new Date(),
        },
      });
      storeStockId = storeStock.id;
    }

    // Create stock batch
    const stockBatch = await prisma.stockBatch.create({
      data: {
        productId: data.productId,
        productSupplierId: data.productSupplierId,
        batchNumber: data.batchNumber || `MANUAL-${Date.now()}`,
        expiryDate: data.expiryDate,
        quantity: data.quantity,
        remainingQuantity: data.quantity,
        purchasePrice: data.purchasePrice,
        warehouseStockId,
        storeStockId,
        status: 'ACTIVE',
        notes: data.notes,
      },
      include: {
        product: {
          select: {
            id: true,
            name: true,
            sku: true,
          }
        },
        productSupplier: {
          include: {
            supplier: {
              select: {
                id: true,
                name: true,
              }
            }
          }
        }
      }
    });

    // Create stock history entry
    await prisma.stockHistory.create({
      data: {
        productId: data.productId,
        productSupplierId: data.productSupplierId,
        batchId: stockBatch.id,
        warehouseStockId,
        storeStockId,
        previousQuantity: 0,
        newQuantity: data.quantity,
        changeQuantity: data.quantity,
        source: "MANUAL_ENTRY",
        notes: `Manual stock batch entry - Batch: ${stockBatch.batchNumber}`,
        userId: auth.user.id,
      },
    });

    // Log activity
    await prisma.activityLog.create({
      data: {
        userId: auth.user.id,
        action: "CREATE_STOCK_BATCH",
        details: `Created stock batch ${stockBatch.batchNumber} for ${productSupplier.product.name} from ${productSupplier.supplier.name}`,
      },
    });

    return NextResponse.json({
      message: "Stock batch created successfully",
      stockBatch
    }, { status: 201 });
  } catch (error) {
    console.error("Error creating stock batch:", error);
    return NextResponse.json(
      { error: "Failed to create stock batch", message: (error as Error).message },
      { status: 500 }
    );
  }
}
