// Import the mocked functions
const { hashPassword, verifyPassword } = require('@/auth');

describe('Password Utilities', () => {
  const testPassword = 'TestPassword123!';

  it('should hash a password', async () => {
    const hashedPassword = await hashPassword(testPassword);

    // Hashed password should be a string
    expect(typeof hashedPassword).toBe('string');

    // Hashed password should be different from the original
    expect(hashedPassword).not.toBe(testPassword);

    // Hashed password should be of reasonable length (bcrypt hashes are ~60 chars)
    expect(hashedPassword.length).toBeGreaterThan(50);
  });

  it('should verify a correct password', async () => {
    const hashedPassword = await hashPassword(testPassword);
    const isValid = await verifyPassword(testPassword, hashedPassword);

    expect(isValid).toBe(true);
  });

  it('should reject an incorrect password', async () => {
    const hashedPassword = await hashPassword(testPassword);
    const isValid = await verifyPassword('WrongPassword123!', hashedPassword);

    expect(isValid).toBe(false);
  });

  it('should generate different hashes for the same password', async () => {
    const hash1 = await hashPassword(testPassword);
    const hash2 = await hashPassword(testPassword);

    // Each hash should be different due to different salts
    expect(hash1).not.toBe(hash2);

    // But both should verify correctly
    expect(await verifyPassword(testPassword, hash1)).toBe(true);
    expect(await verifyPassword(testPassword, hash2)).toBe(true);
  });
});
