"use client";

import { MainLayout } from "@/components/layout/MainLayout";
import { PageHeader } from "@/components/layout/PageHeader";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { LatestActivityLogs } from "@/components/dashboard/LatestActivityLogs";
import { LatestProducts } from "@/components/dashboard/LatestProducts";
import { LatestTransactions } from "@/components/dashboard/LatestTransactions";
import { ProductCount } from "@/components/dashboard/ProductCount";
import { TotalRevenueCard, TotalSalesCard, useSalesStats } from "@/components/dashboard/SalesStats";
import { TodaySalesCard, TodayRevenueCard, useTodayStats } from "@/components/dashboard/TodayStats";
import { ActiveUsersCard } from "@/components/dashboard/ActiveUsersCard";
import { Alert, AlertTitle, AlertDescription } from "@/components/ui/alert";
import { useClientAuth } from "@/hooks/use-client-auth";
import { formatCurrency } from "@/lib/utils";
import { TrendingUp, TrendingDown, BarChart, PieChart, Beaker } from "lucide-react";
import { DashboardSalesTrendsChart } from "@/components/dashboard/DashboardSalesTrendsChart";

export default function DashboardPage() {
  const { user } = useClientAuth();
  const isDeveloper = user?.role === "DEVELOPER";
  const { stats, isLoading } = useSalesStats();
  const { stats: todayStats } = useTodayStats();

  return (
    <MainLayout>
      <PageHeader title="Dashboard" />

      {isDeveloper && (
        <Alert className="mb-6 border-blue-500 bg-blue-50 text-blue-800 dark:bg-blue-900 dark:text-blue-100">
          <Beaker className="h-4 w-4" />
          <AlertTitle>Developer Mode</AlertTitle>
          <AlertDescription>
            You are logged in with a Developer account. You only have access to the Development
            section. Navigate to the API Tests page to access development tools.
          </AlertDescription>
        </Alert>
      )}

      {!isDeveloper ? (
        <>
          <div className="grid gap-4 md:grid-cols-3 lg:grid-cols-6">
            {/* Total Revenue Card */}
            <TotalRevenueCard stats={stats} />

            {/* Today's Revenue Card */}
            <TodayRevenueCard stats={todayStats} />

            {/* Total Sales Card */}
            <TotalSalesCard stats={stats} />

            {/* Today's Sales Card */}
            <TodaySalesCard stats={todayStats} />

            {/* Total Products Card */}
            <ProductCount />

            {/* Active Users Card */}
        </>
            <ActiveUsersCard />
          </div>

          <div className="mt-6 grid gap-6 md:grid-cols-2">
            {/* Sales Trends Chart */}
            <DashboardSalesTrendsChart className="col-span-1" />

            {/* Total Subscriber */}
            <Card className="col-span-1">
              <CardHeader className="flex flex-row items-center justify-between pb-2">
                <CardTitle className="text-sm font-medium">Total Subscriber</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">24,473</div>
                <div className="flex items-center gap-2 pt-1 text-xs">
                  <span className="flex items-center text-green-500">
                    <TrendingUp className="mr-1 h-3 w-3" />
                    +6.3%
                  </span>
                  <span className="text-muted-foreground">vs last period</span>
                </div>
                <div className="mt-4 h-[200px] w-full">
                  {/* Placeholder for chart */}
                  <div className="flex h-full w-full items-end justify-between gap-2">
                    <div className="h-[20%] w-8 rounded-t-md bg-gray-200"></div>
                    <div className="h-[30%] w-8 rounded-t-md bg-gray-200"></div>
                    <div className="h-[25%] w-8 rounded-t-md bg-gray-200"></div>
                    <div className="h-[60%] w-8 rounded-t-md bg-indigo-500"></div>
                    <div className="h-[90%] w-8 rounded-t-md bg-indigo-500"></div>
                    <div className="h-[70%] w-8 rounded-t-md bg-indigo-500"></div>
                    <div className="h-[40%] w-8 rounded-t-md bg-gray-200"></div>
                    <div className="h-[30%] w-8 rounded-t-md bg-gray-200"></div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Latest Activity Logs, Latest Products, and Latest Transactions */}
          <div className="mt-6 grid gap-6 md:grid-cols-3">
            <LatestActivityLogs />
            <LatestProducts />
            <LatestTransactions />
          </div>
      ) : (
        <Card className="mt-6">
          <CardHeader>
            <CardTitle className="flex items-center">
              <Beaker className="mr-2 h-5 w-5" />
              Developer Tools
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="mb-4">
              As a Developer, you have restricted access to the application. You can only access the
              Development section which contains API testing tools and other developer utilities.
            </p>
            <Button
              onClick={() => (window.location.href = "/tests")}
              className="bg-black text-white hover:bg-gray-800"
            >
              Go to API Tests
            </Button>
          </CardContent>
        </Card>
      )}
    </MainLayout>
  );
}
