import { NextResponse } from "next/server";
import type { NextRequest } from "next/server";
import { jwtVerify } from "jose";

export async function middleware(request: NextRequest) {
  // Get the session token from cookies
  const token = request.cookies.get("session-token");
  const pathname = request.nextUrl.pathname;

  // Initialize session
  let session = null;

  // Verify the token if it exists
  if (token) {
    try {
      const { payload } = await jwtVerify(
        token.value,
        new TextEncoder().encode(process.env.NEXTAUTH_SECRET || "fallback-secret")
      );

      // Create session object
      session = {
        user: {
          id: payload.id,
          name: payload.name,
          email: payload.email,
          role: payload.role,
        }
      };
    } catch (error) {
      console.error("Token verification failed:", error);
      // Invalid token, session remains null
    }
  }

  // Public routes that don't require authentication but should redirect to dashboard if authenticated
  const authRedirectRoutes = ["/login", "/simple-login", "/client-login"];

  // Public routes that don't require authentication and should not redirect
  const publicRoutes = [
    "/api/auth",
    "/api/login",
    "/_next",
    "/favicon.ico",
    "/api/backup/history",
    "/api/backup",
    "/api/categories",
    "/api/units",
    "/api/products",
    "/api/suppliers",
    "/api/inventory",
    "/api/tests",
    "/tests",
    // Add all API routes that might be needed for testing
    "/api/transactions",
    "/api/users",
    "/api/activity-logs",
    "/api/notifications",
    "/api/conversations",
    "/api/setup"
  ];

  // Check if the route is a login page
  const isLoginRoute = authRedirectRoutes.some((route) =>
    pathname === route || pathname.startsWith(route + "/")
  );

  // Check if the route is a public route that should be accessible without auth
  const isPublicRoute = publicRoutes.some((route) =>
    pathname === route || pathname.startsWith(route + "/")
  );

  // If it's a public route, allow access without authentication
  if (isPublicRoute) {
    console.log(`[Middleware] Public route: ${pathname}, allowing access`);
    return NextResponse.next();
  }

  // If user is authenticated and trying to access login page, redirect to dashboard
  if (isLoginRoute && session?.user) {
    console.log(`[Middleware] Already authenticated, redirecting to dashboard. Path: ${pathname}`);
    return NextResponse.redirect(new URL("/dashboard", request.url));
  }

  // If it's a login route and user is not authenticated, allow access
  if (isLoginRoute && !session?.user) {
    console.log(`[Middleware] Login route for unauthenticated user, allowing access. Path: ${pathname}`);
    return NextResponse.next();
  }

  // If user is authenticated and trying to access dashboard but is a cashier, redirect to POS
  if (pathname === "/dashboard" && session?.user?.role === "CASHIER") {
    console.log(`[Middleware] Cashier trying to access dashboard, redirecting to POS`);
    return NextResponse.redirect(new URL("/pos", request.url));
  }

  // Handle the home page - redirect based on role
  if (pathname === "/") {
    if (session?.user) {
      // If user is a cashier, redirect to POS page
      if (session.user.role === "CASHIER") {
        console.log(`[Middleware] Home page access by cashier, redirecting to POS`);
        return NextResponse.redirect(new URL("/pos", request.url));
      } else {
        // For other roles, redirect to dashboard
        console.log(`[Middleware] Home page access by authenticated user, redirecting to dashboard`);
        return NextResponse.redirect(new URL("/dashboard", request.url));
      }
    } else {
      console.log(`[Middleware] Home page access by unauthenticated user, redirecting to login`);
      return NextResponse.redirect(new URL("/login", request.url));
    }
  }

  // If user is not authenticated, handle based on route type
  if (!session?.user) {
    // Allow API routes to handle their own authentication
    if (pathname.startsWith("/api/")) {
      console.log(`[Middleware] API route accessed without authentication: ${pathname} - allowing endpoint to handle auth`);
      return NextResponse.next();
    }

    // Redirect non-API routes to login
    console.log(`[Middleware] Not authenticated, redirecting to login. Path: ${pathname}`);
    const url = new URL("/login", request.url);
    url.searchParams.set("callbackUrl", encodeURI(request.url));
    return NextResponse.redirect(url);
  }

  console.log(`[Middleware] Authenticated as ${session.user.email}, role: ${session.user.role}, accessing: ${pathname}`);

  // Special handling for DEVELOPER role - can only access /tests routes and related API endpoints
  if (session.user.role === "DEVELOPER") {
    console.log(`[Middleware] Developer role accessing: ${pathname}`);

    // Allow access to all API routes for testing purposes
    if (pathname.startsWith("/api/")) {
      console.log(`[Middleware] Developer role allowed access to API route: ${pathname}`);
      return NextResponse.next();
    }

    // Allow access to tests routes
    if (pathname.startsWith("/tests")) {
      console.log(`[Middleware] Developer role allowed access to tests route: ${pathname}`);
      return NextResponse.next();
    }

    // Allow access to dashboard for navigation
    if (pathname === "/dashboard") {
      return NextResponse.next();
    }

    // Allow access to settings for basic functionality
    if (pathname === "/settings") {
      return NextResponse.next();
    }

    // Redirect to tests page for any other route
    console.log(`[Middleware] Developer role restricted from accessing: ${pathname}`);
    return NextResponse.redirect(new URL("/tests", request.url));
  }

  // Role-based access control for specific routes (for non-developer roles)

  // Restrict POS access to only CASHIER role
  if (pathname === "/pos" || pathname.startsWith("/pos/")) {
    if (session.user.role !== "CASHIER") {
      console.log(`[Middleware] Non-cashier role (${session.user.role}) attempting to access POS, redirecting to access denied`);
      return NextResponse.redirect(new URL("/access-denied?resource=pos&requiredRole=CASHIER", request.url));
    }
  }

  // Finance routes - accessible by SUPER_ADMIN and FINANCE_ADMIN
  const financeRoutes = [
    "/admin/revenue-targets",
    "/admin/cash-audit",
    "/admin/analytics",
    "/admin/cash-drawers"
  ];

  const isFinanceRoute = financeRoutes.some(route => pathname.startsWith(route));

  if (isFinanceRoute && !["SUPER_ADMIN", "FINANCE_ADMIN"].includes(session.user.role as string)) {
    console.log(`[Middleware] Unauthorized access to finance route: ${pathname} by role: ${session.user.role}`);
    return NextResponse.redirect(new URL("/access-denied?resource=finance&requiredRole=SUPER_ADMIN,FINANCE_ADMIN", request.url));
  }

  // Other admin routes - only accessible by SUPER_ADMIN
  if (pathname.startsWith("/admin") && !isFinanceRoute &&
      !["SUPER_ADMIN"].includes(session.user.role as string)) {
    console.log(`[Middleware] Unauthorized access to admin route: ${pathname} by role: ${session.user.role}`);
    return NextResponse.redirect(new URL("/access-denied?resource=admin&requiredRole=SUPER_ADMIN", request.url));
  }

  if (pathname.startsWith("/backup") &&
      !["SUPER_ADMIN", "FINANCE_ADMIN"].includes(session.user.role as string)) {
    return NextResponse.redirect(new URL("/dashboard", request.url));
  }

  // Protect development routes - only accessible by DEVELOPER role
  if (pathname.startsWith("/tests") && session.user.role !== "DEVELOPER") {
    return NextResponse.redirect(new URL("/dashboard", request.url));
  }

  // Allow access to the requested resource
  // Add user information to headers for API routes
  const response = NextResponse.next();
  if (session?.user && pathname.startsWith('/api/')) {
    response.headers.set('x-user-id', session.user.id as string);
    response.headers.set('x-user-role', session.user.role as string);
    response.headers.set('x-user-email', session.user.email as string);
  }
  return response;
}

export const config = {
  matcher: [
    /*
     * Match all request paths except:
     * - _next (Next.js internals)
     * - static files (build-time assets)
     * - favicon.ico (browser icon)
     * - public folder
     * - public files with extensions
     */
    "/((?!_next|static|favicon.ico|.*\\..*$).*)",
    "/api/:path*",
  ],
};
