import { NextRequest, NextResponse } from 'next/server';
import { PrismaClient } from '@/generated/prisma';
import { verifyAuthToken } from '@/lib/auth-utils';
import { z } from 'zod';

// Create a singleton instance of PrismaClient to prevent too many connections
const globalForPrisma = globalThis as unknown as { prisma: PrismaClient };
const prisma = globalForPrisma.prisma || new PrismaClient();
if (process.env.NODE_ENV !== "production") globalForPrisma.prisma = prisma;

// Schema for creating a return
const createReturnSchema = z.object({
  transactionId: z.string().min(1, 'Transaction ID is required'),
  customerId: z.string().nullable(), // Allow null for walk-in customers
  reason: z.string().min(1, 'Reason is required'),
  notes: z.string().optional(),
  items: z.array(z.object({
    productId: z.string().min(1, 'Product ID is required'),
    quantity: z.number().positive('Quantity must be greater than 0'),
    unitPrice: z.number().positive('Unit price must be greater than 0'),
    subtotal: z.number().positive('Subtotal must be greater than 0'),
  })).min(1, 'At least one item is required'),
});

// GET /api/returns - List returns with filtering and pagination
export async function GET(request: NextRequest) {
  try {
    const auth = await verifyAuthToken(request);
    if (!auth.authenticated || !auth.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = ['SUPER_ADMIN', 'WAREHOUSE_ADMIN', 'FINANCE_ADMIN'].includes(auth.user.role);
    if (!hasPermission) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 });
    }

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const status = searchParams.get('status');
    const search = searchParams.get('search');

    const skip = (page - 1) * limit;

    // Build where clause
    const where: any = {};
    if (status) {
      where.status = status;
    }
    if (search) {
      where.OR = [
        { reason: { contains: search, mode: 'insensitive' } },
        { customer: { name: { contains: search, mode: 'insensitive' } } },
        { transaction: { id: { contains: search, mode: 'insensitive' } } },
        // Also search for "walk-in" when customer is null
        ...(search.toLowerCase().includes('walk') ? [{ customerId: null }] : []),
      ];
    }

    const [returns, total] = await Promise.all([
      prisma.return.findMany({
        where,
        include: {
          customer: true,
          transaction: true,
          items: {
            include: {
              product: true,
            },
          },
        },
        orderBy: { createdAt: 'desc' },
        skip,
        take: limit,
      }),
      prisma.return.count({ where }),
    ]);

    return NextResponse.json({
      returns,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    });
  } catch (error) {
    console.error('Error fetching returns:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

// POST /api/returns - Create new return
export async function POST(request: NextRequest) {
  try {
    console.log('[API] POST /api/returns - Start');

    const auth = await verifyAuthToken(request);
    if (!auth.authenticated || !auth.user) {
      console.log('[API] Authentication failed');
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const canCreateReturn = ['SUPER_ADMIN', 'WAREHOUSE_ADMIN', 'CASHIER'].includes(auth.user.role);
    if (!canCreateReturn) {
      console.log('[API] Insufficient permissions for role:', auth.user.role);
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 });
    }

    const body = await request.json();
    console.log('[API] Request body:', JSON.stringify(body, null, 2));

    const validatedData = createReturnSchema.parse(body);
    console.log('[API] Validation successful');

    // Verify transaction exists and is completed
    const transaction = await prisma.transaction.findUnique({
      where: { id: validatedData.transactionId },
      include: { items: true },
    });

    if (!transaction) {
      return NextResponse.json({ error: 'Transaction not found' }, { status: 404 });
    }

    if (transaction.status !== 'COMPLETED') {
      return NextResponse.json({ error: 'Can only return items from completed transactions' }, { status: 400 });
    }

    // Validate return items against transaction items
    for (const returnItem of validatedData.items) {
      const transactionItem = transaction.items.find(item => item.productId === returnItem.productId);
      if (!transactionItem) {
        return NextResponse.json({ 
          error: `Product ${returnItem.productId} was not in the original transaction` 
        }, { status: 400 });
      }
      if (returnItem.quantity > transactionItem.quantity) {
        return NextResponse.json({ 
          error: `Return quantity exceeds original purchase quantity for product ${returnItem.productId}` 
        }, { status: 400 });
      }
    }

    // Calculate total
    const total = validatedData.items.reduce((sum, item) => sum + item.subtotal, 0);

    // Create return with items
    console.log('[API] Creating return with data:', {
      transactionId: validatedData.transactionId,
      customerId: validatedData.customerId,
      reason: validatedData.reason,
      notes: validatedData.notes,
      total,
      itemsCount: validatedData.items.length,
    });

    const newReturn = await prisma.return.create({
      data: {
        transactionId: validatedData.transactionId,
        customerId: validatedData.customerId, // Allow null for walk-in customers
        reason: validatedData.reason,
        notes: validatedData.notes,
        total,
        items: {
          create: validatedData.items,
        },
      },
      include: {
        customer: true,
        transaction: true,
        items: {
          include: {
            product: true,
          },
        },
      },
    });

    console.log('[API] Return created successfully:', newReturn.id);
    return NextResponse.json(newReturn, { status: 201 });
  } catch (error) {
    if (error instanceof z.ZodError) {
      console.error('[API] Validation error:', error.errors);
      // Provide more user-friendly error messages
      const errorMessages = error.errors.map(err => {
        if (err.path.includes('reason')) {
          return 'Reason for return is required';
        }
        if (err.path.includes('items')) {
          if (err.message.includes('At least one item')) {
            return 'At least one item is required for the return';
          }
          return 'Please check the return item details';
        }
        if (err.path.includes('transactionId')) {
          return 'Transaction ID is required';
        }
        return err.message;
      });
      return NextResponse.json({
        error: 'Validation error',
        message: errorMessages[0] || 'Please check your input data',
        details: error.errors
      }, { status: 400 });
    }
    console.error('[API] Error creating return:', error);
    console.error('[API] Error stack:', error instanceof Error ? error.stack : 'No stack trace');
    return NextResponse.json({
      error: 'Internal server error',
      message: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
