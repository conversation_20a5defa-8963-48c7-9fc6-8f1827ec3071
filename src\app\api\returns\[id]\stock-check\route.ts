import { NextRequest, NextResponse } from 'next/server';
import { PrismaClient } from '@/generated/prisma';
import { verifyAuthToken } from '@/lib/auth-utils';

const prisma = new PrismaClient();

// GET /api/returns/[id]/stock-check - Check stock availability for return items
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    
    const auth = await verifyAuthToken(request);
    if (!auth.authenticated || !auth.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = ['SUPER_ADMIN', 'WAREHOUSE_ADMIN', 'FINANCE_ADMIN', 'CASHIER'].includes(auth.user.role);
    if (!hasPermission) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 });
    }

    // Get return with items
    const returnRecord = await prisma.return.findUnique({
      where: { id },
      include: {
        items: {
          include: {
            product: {
              include: {
                storeStock: true,
                unit: true,
              },
            },
          },
        },
      },
    });

    if (!returnRecord) {
      return NextResponse.json({ error: 'Return not found' }, { status: 404 });
    }

    // Check stock availability for each item
    const stockCheck = {
      returnId: returnRecord.id,
      items: [],
      overallStockStatus: 'SUFFICIENT', // SUFFICIENT, INSUFFICIENT, MIXED
      canProcessReplacement: true,
      totalItemsChecked: returnRecord.items.length,
      itemsWithSufficientStock: 0,
      itemsWithInsufficientStock: 0,
    };

    for (const item of returnRecord.items) {
      const currentStock = item.product.storeStock?.quantity || 0;
      const returnedQuantity = Number(item.quantity);
      const hasSufficientStock = Number(currentStock) >= returnedQuantity;

      const itemStockInfo = {
        productId: item.product.id,
        productName: item.product.name,
        productSku: item.product.sku,
        unitName: item.product.unit?.name || 'units',
        unitAbbreviation: item.product.unit?.abbreviation || 'pcs',
        returnedQuantity: returnedQuantity,
        currentStock: Number(currentStock),
        requiredStock: returnedQuantity,
        availableStock: Number(currentStock),
        hasSufficientStock: hasSufficientStock,
        stockShortage: hasSufficientStock ? 0 : returnedQuantity - Number(currentStock),
        stockStatus: hasSufficientStock ? 'SUFFICIENT' : 'INSUFFICIENT',
      };

      stockCheck.items.push(itemStockInfo);

      if (hasSufficientStock) {
        stockCheck.itemsWithSufficientStock++;
      } else {
        stockCheck.itemsWithInsufficientStock++;
        stockCheck.canProcessReplacement = false;
      }
    }

    // Determine overall stock status
    if (stockCheck.itemsWithInsufficientStock === 0) {
      stockCheck.overallStockStatus = 'SUFFICIENT';
    } else if (stockCheck.itemsWithSufficientStock === 0) {
      stockCheck.overallStockStatus = 'INSUFFICIENT';
    } else {
      stockCheck.overallStockStatus = 'MIXED';
    }

    // Add summary information
    const summary = {
      canProcessImmediateReplacement: stockCheck.canProcessReplacement,
      requiresRestocking: !stockCheck.canProcessReplacement,
      totalShortageItems: stockCheck.itemsWithInsufficientStock,
      message: stockCheck.canProcessReplacement 
        ? 'All items have sufficient stock for immediate replacement'
        : stockCheck.overallStockStatus === 'MIXED'
        ? 'Some items have insufficient stock for immediate replacement'
        : 'All items have insufficient stock for immediate replacement',
    };

    return NextResponse.json({
      ...stockCheck,
      summary,
      checkedAt: new Date().toISOString(),
    });

  } catch (error) {
    console.error('Error checking stock for return:', error);
    return NextResponse.json({ 
      error: 'Failed to check stock availability', 
      message: (error as Error).message 
    }, { status: 500 });
  }
}
