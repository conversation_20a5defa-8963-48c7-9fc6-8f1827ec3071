/**
 * API Testing Script
 *
 * This script tests the product management API routes.
 * Run this script in the browser console after logging in as an admin.
 */

// Configuration
const API_BASE_URL = window.location.origin;
let testResults = [];
let createdIds = {
  category: null,
  unit: null,
  product: null
};

// Helper function to log test results
function logResult(testName, success, response, error = null) {
  const result = {
    test: testName,
    success,
    response: response ? JSON.stringify(response, null, 2) : null,
    error: error ? (error.message || JSON.stringify(error)) : null,
    timestamp: new Date().toISOString()
  };

  testResults.push(result);

  console.log(
    `%c${success ? '✅ PASS' : '❌ FAIL'}: ${testName}`,
    `color: ${success ? 'green' : 'red'}; font-weight: bold;`
  );

  if (response) {
    console.log('Response:', response);
  }

  if (error) {
    console.error('Error:', error);
  }

  return result;
}

// Helper function to make API requests
async function apiRequest(endpoint, method = 'GET', data = null) {
  try {
    console.log(`Making API request to ${endpoint} with method ${method}`);

    const response = await fetch(`${API_BASE_URL}${endpoint}`, {
      method,
      headers: {
        'Content-Type': 'application/json'
      },
      body: data ? JSON.stringify(data) : undefined,
      credentials: 'include' // Important for sending cookies
    });

    console.log(`Received response from ${endpoint} with status ${response.status}`);

    // Clone the response before reading it (since response body can only be read once)
    const responseClone = response.clone();

    // Check content type to avoid parsing HTML as JSON
    const contentType = response.headers.get('content-type');
    console.log(`Response content type: ${contentType}`);

    let responseData;
    let responseText = '';

    try {
      // First try to get the text content regardless of content type
      responseText = await responseClone.text();

      // Log a preview of the response text for debugging
      console.log(`Response text preview: ${responseText.substring(0, 100)}${responseText.length > 100 ? '...' : ''}`);

      // Then try to parse as JSON if it looks like JSON
      if (contentType && contentType.includes('application/json') ||
          (responseText.trim().startsWith('{') && responseText.trim().endsWith('}')) ||
          (responseText.trim().startsWith('[') && responseText.trim().endsWith(']'))) {
        try {
          responseData = JSON.parse(responseText);
        } catch (jsonError) {
          console.warn(`Failed to parse response as JSON despite content type: ${contentType}`);
          responseData = {
            nonJsonResponse: true,
            contentType: contentType || 'unknown',
            textPreview: responseText.substring(0, 150) + (responseText.length > 150 ? '...' : ''),
            parseError: jsonError.message
          };

          // If the response is not OK, throw an error with the text preview
          if (!response.ok) {
            throw new Error(`API Error (${response.status}): Failed to parse JSON - ${responseData.textPreview}`);
          }
        }
      } else {
        // For non-JSON responses, just use the text
        responseData = {
          nonJsonResponse: true,
          contentType: contentType || 'unknown',
          textPreview: responseText.substring(0, 150) + (responseText.length > 150 ? '...' : '')
        };

        // If the response is not OK, throw an error with the text preview
        if (!response.ok) {
          throw new Error(`API Error (${response.status}): Non-JSON response - ${responseData.textPreview}`);
        }
      }
    } catch (textError) {
      // If we can't even get the text, create a fallback response
      console.error('Error reading response text:', textError);
      responseData = {
        nonJsonResponse: true,
        contentType: contentType || 'unknown',
        error: 'Failed to read response body',
        errorDetails: textError.message
      };

      if (!response.ok) {
        throw new Error(`API Error (${response.status}): ${textError.message}`);
      }
    }

    // For JSON responses, check if response is OK
    if (!response.ok && responseData && !responseData.nonJsonResponse) {
      throw new Error(`API Error: ${responseData.error || response.statusText}`);
    }

    return responseData;
  } catch (error) {
    console.error(`API request to ${endpoint} failed:`, error);
    throw error;
  }
}

// Test Category API
async function testCategoryAPI() {
  console.log('%c🧪 Testing Category API', 'color: blue; font-weight: bold;');

  try {
    // Test GET /api/categories
    const getCategories = await apiRequest('/api/categories');
    logResult('GET /api/categories', true, getCategories);

    // Test POST /api/categories
    const categoryData = {
      name: `Test Category ${Date.now()}`,
      description: 'This is a test category created by the API test script'
    };

    const createCategory = await apiRequest('/api/categories', 'POST', categoryData);
    const createResult = logResult('POST /api/categories', true, createCategory);

    // Save the created category ID for later tests
    createdIds.category = createCategory.category.id;

    // Test GET /api/categories/:id
    const getCategory = await apiRequest(`/api/categories/${createdIds.category}`);
    logResult('GET /api/categories/:id', true, getCategory);

    // Test PATCH /api/categories/:id
    const updateData = {
      description: 'Updated description for test category'
    };

    const updateCategory = await apiRequest(`/api/categories/${createdIds.category}`, 'PATCH', updateData);
    logResult('PATCH /api/categories/:id', true, updateCategory);

    // We'll test DELETE after all other tests are complete
  } catch (error) {
    logResult('Category API Tests', false, null, error);
  }
}

// Test Unit API
async function testUnitAPI() {
  console.log('%c🧪 Testing Unit API', 'color: blue; font-weight: bold;');

  try {
    // Test GET /api/units
    const getUnits = await apiRequest('/api/units');
    logResult('GET /api/units', true, getUnits);

    // Test POST /api/units
    const unitData = {
      name: `Test Unit ${Date.now()}`,
      abbreviation: 'TU',
      description: 'This is a test unit created by the API test script'
    };

    const createUnit = await apiRequest('/api/units', 'POST', unitData);
    logResult('POST /api/units', true, createUnit);

    // Save the created unit ID for later tests
    createdIds.unit = createUnit.unit.id;

    // Test GET /api/units/:id
    const getUnit = await apiRequest(`/api/units/${createdIds.unit}`);
    logResult('GET /api/units/:id', true, getUnit);

    // Test PATCH /api/units/:id
    const updateData = {
      description: 'Updated description for test unit'
    };

    const updateUnit = await apiRequest(`/api/units/${createdIds.unit}`, 'PATCH', updateData);
    logResult('PATCH /api/units/:id', true, updateUnit);

    // We'll test DELETE after all other tests are complete
  } catch (error) {
    logResult('Unit API Tests', false, null, error);
  }
}

// Test Product API
async function testProductAPI() {
  console.log('%c🧪 Testing Product API', 'color: blue; font-weight: bold;');

  try {
    // Test GET /api/products
    const getProducts = await apiRequest('/api/products');
    logResult('GET /api/products', true, getProducts);

    // Test POST /api/products
    const productData = {
      name: `Test Product ${Date.now()}`,
      description: 'This is a test product created by the API test script',
      sku: `TST-${Date.now()}`,
      barcode: `BAR-${Date.now()}`,
      categoryId: createdIds.category,
      unitId: createdIds.unit,
      basePrice: 19.99,
      friendPrice: 17.99,
      familyPrice: 15.99,
      active: true
    };

    const createProduct = await apiRequest('/api/products', 'POST', productData);
    logResult('POST /api/products', true, createProduct);

    // Save the created product ID for later tests
    createdIds.product = createProduct.product.id;

    // Test GET /api/products/:id
    const getProduct = await apiRequest(`/api/products/${createdIds.product}`);
    logResult('GET /api/products/:id', true, getProduct);

    // Test PATCH /api/products/:id
    const updateData = {
      description: 'Updated description for test product',
      basePrice: 24.99
    };

    const updateProduct = await apiRequest(`/api/products/${createdIds.product}`, 'PATCH', updateData);
    logResult('PATCH /api/products/:id', true, updateProduct);

    // We'll test DELETE after all other tests are complete
  } catch (error) {
    logResult('Product API Tests', false, null, error);
  }
}

// Test DELETE operations
async function testDeleteOperations() {
  console.log('%c🧪 Testing DELETE Operations', 'color: blue; font-weight: bold;');

  try {
    // Test DELETE /api/products/:id
    if (createdIds.product) {
      const deleteProduct = await apiRequest(`/api/products/${createdIds.product}`, 'DELETE');
      logResult('DELETE /api/products/:id', true, deleteProduct);
    }

    // Test DELETE /api/units/:id
    if (createdIds.unit) {
      const deleteUnit = await apiRequest(`/api/units/${createdIds.unit}`, 'DELETE');
      logResult('DELETE /api/units/:id', true, deleteUnit);
    }

    // Test DELETE /api/categories/:id
    if (createdIds.category) {
      const deleteCategory = await apiRequest(`/api/categories/${createdIds.category}`, 'DELETE');
      logResult('DELETE /api/categories/:id', true, deleteCategory);
    }
  } catch (error) {
    logResult('DELETE Operations', false, null, error);
  }
}

// Run all tests
async function runAllTests() {
  console.log('%c🧪 Starting API Tests', 'color: blue; font-size: 16px; font-weight: bold;');
  testResults = [];
  createdIds = { category: null, unit: null, product: null };

  try {
    await testCategoryAPI();
    await testUnitAPI();
    await testProductAPI();
    await testDeleteOperations();

    // Print summary
    const passCount = testResults.filter(r => r.success).length;
    const failCount = testResults.filter(r => !r.success).length;

    console.log(
      `%c🧪 Test Summary: ${passCount} passed, ${failCount} failed`,
      `color: ${failCount === 0 ? 'green' : 'red'}; font-size: 16px; font-weight: bold;`
    );

    return {
      summary: {
        total: testResults.length,
        passed: passCount,
        failed: failCount
      },
      results: testResults,
      createdIds
    };
  } catch (error) {
    console.error('Error running tests:', error);
    return {
      error: error.message,
      results: testResults
    };
  }
}

// Export the test runner
window.runApiTests = runAllTests;

console.log('%c🧪 API Test Script Loaded', 'color: blue; font-size: 16px; font-weight: bold;');
console.log('Run tests by executing: window.runApiTests()');
