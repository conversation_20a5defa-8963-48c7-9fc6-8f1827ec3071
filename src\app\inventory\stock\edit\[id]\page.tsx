"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { MainLayout } from "@/components/layout/MainLayout";
import { PageHeader } from "@/components/layout/PageHeader";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
  CardFooter,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Loader2 } from "lucide-react";
import { toast } from "sonner";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";

interface Product {
  id: string;
  name: string;
  sku: string;
  category?: {
    id: string;
    name: string;
  } | null;
  unit?: {
    id: string;
    name: string;
    abbreviation: string;
  } | null;
}

interface StockItem {
  id: string;
  productId: string;
  quantity: number;
  minThreshold: number;
  maxThreshold?: number | null;
  lastUpdated: string;
  product: Product;
}

// Define form schema
const stockFormSchema = z.object({
  quantity: z
    .number({
      required_error: "Please enter a quantity",
    })
    .min(0, "Quantity must be 0 or greater"),
  minThreshold: z
    .number({
      required_error: "Please enter a minimum threshold",
    })
    .min(0, "Minimum threshold must be 0 or greater"),
  maxThreshold: z.number().min(0, "Maximum threshold must be 0 or greater").optional().nullable(),
});

export default function EditStockPage({ params }: { params: { id: string } }) {
  const router = useRouter();
  const [stockItem, setStockItem] = useState<StockItem | null>(null);
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [stockType, setStockType] = useState<"STORE" | "WAREHOUSE">("STORE");

  // Initialize form
  const form = useForm<z.infer<typeof stockFormSchema>>({
    resolver: zodResolver(stockFormSchema),
    defaultValues: {
      quantity: 0,
      minThreshold: 5,
      maxThreshold: null,
    },
  });

  // Fetch stock item data
  useEffect(() => {
    const fetchStockItem = async () => {
      setLoading(true);
      try {
        // Check URL for type parameter
        const urlParams = new URLSearchParams(window.location.search);
        const typeParam = urlParams.get("type");

        if (typeParam === "warehouse") {
          // Fetch warehouse stock
          const response = await fetch(`/api/inventory/warehouse-stock?id=${params.id}`);
          const data = await response.json();

          if (data.warehouseStock && data.warehouseStock.length > 0) {
            setStockItem(data.warehouseStock[0]);
            setStockType("WAREHOUSE");

            // Set form values
            form.setValue("quantity", Number(data.warehouseStock[0].quantity));
            form.setValue("minThreshold", Number(data.warehouseStock[0].minThreshold));
            form.setValue(
              "maxThreshold",
              data.warehouseStock[0].maxThreshold
                ? Number(data.warehouseStock[0].maxThreshold)
                : null
            );
          } else {
            toast.error("Warehouse stock item not found");
            router.push("/inventory/stock");
          }
        } else {
          // Fetch store stock (default)
          const response = await fetch(`/api/inventory/store-stock?id=${params.id}`);
          const data = await response.json();

          if (data.storeStock && data.storeStock.length > 0) {
            setStockItem(data.storeStock[0]);
            setStockType("STORE");

            // Set form values
            form.setValue("quantity", Number(data.storeStock[0].quantity));
            form.setValue("minThreshold", Number(data.storeStock[0].minThreshold));
            form.setValue(
              "maxThreshold",
              data.storeStock[0].maxThreshold ? Number(data.storeStock[0].maxThreshold) : null
            );
          } else {
            // If not found in store stock and no type was specified, try warehouse stock
            if (!typeParam) {
              const warehouseResponse = await fetch(
                `/api/inventory/warehouse-stock?id=${params.id}`
              );
              const warehouseData = await warehouseResponse.json();

              if (warehouseData.warehouseStock && warehouseData.warehouseStock.length > 0) {
                setStockItem(warehouseData.warehouseStock[0]);
                setStockType("WAREHOUSE");

                // Set form values
                form.setValue("quantity", Number(warehouseData.warehouseStock[0].quantity));
                form.setValue("minThreshold", Number(warehouseData.warehouseStock[0].minThreshold));
                form.setValue(
                  "maxThreshold",
                  warehouseData.warehouseStock[0].maxThreshold
                    ? Number(warehouseData.warehouseStock[0].maxThreshold)
                    : null
                );
              } else {
                toast.error("Stock item not found");
                router.push("/inventory/stock");
              }
            } else {
              toast.error("Store stock item not found");
              router.push("/inventory/stock");
            }
          }
        }
      } catch (error) {
        console.error("Error fetching stock item:", error);
        toast.error("Failed to load stock item");
        router.push("/inventory/stock");
      } finally {
        setLoading(false);
      }
    };

    fetchStockItem();
  }, [params.id, router, form]);

  // Handle form submission
  const onSubmit = async (values: z.infer<typeof stockFormSchema>) => {
    if (!stockItem) return;

    setSubmitting(true);
    try {
      const endpoint =
        stockType === "STORE"
          ? `/api/inventory/store-stock/${stockItem.id}`
          : `/api/inventory/warehouse-stock/${stockItem.id}`;

      const response = await fetch(endpoint, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          quantity: values.quantity,
          minThreshold: values.minThreshold,
          maxThreshold: values.maxThreshold,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to update stock");
      }

      toast.success("Stock updated successfully");
      router.push("/inventory/stock");
    } catch (error) {
      console.error("Error updating stock:", error);
      toast.error(error instanceof Error ? error.message : "Failed to update stock");
    } finally {
      setSubmitting(false);
    }
  };

  return (
    <MainLayout>
      <PageHeader
        title="Edit Stock"
        actions={
          <Button variant="outline" onClick={() => router.push("/inventory/stock")}>
            Cancel
          </Button>
        }
      />
      <div className="max-w-2xl mx-auto">
        <Card>
          <CardHeader>
            <CardTitle>
              {stockItem ? `${stockItem.product.name} (${stockItem.product.sku})` : "Loading..."}
            </CardTitle>
            <CardDescription>
              {stockType === "STORE" ? "Store" : "Warehouse"} inventory details
            </CardDescription>
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className="flex justify-center items-center py-8">
                <Loader2 className="h-8 w-8 animate-spin text-primary" />
              </div>
            ) : stockItem ? (
              <Form {...form}>
                <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <FormField
                      control={form.control}
                      name="quantity"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Quantity</FormLabel>
                          <FormControl>
                            <Input
                              type="number"
                              step="0.01"
                              min="0"
                              onChange={(e) => field.onChange(parseFloat(e.target.value))}
                              value={field.value}
                            />
                          </FormControl>
                          <FormDescription>Current stock quantity</FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="minThreshold"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Minimum Threshold</FormLabel>
                          <FormControl>
                            <Input
                              type="number"
                              step="0.01"
                              min="0"
                              onChange={(e) => field.onChange(parseFloat(e.target.value))}
                              value={field.value}
                            />
                          </FormControl>
                          <FormDescription>Low stock warning level</FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="maxThreshold"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Maximum Threshold (Optional)</FormLabel>
                          <FormControl>
                            <Input
                              type="number"
                              step="0.01"
                              min="0"
                              onChange={(e) => {
                                const value =
                                  e.target.value === "" ? null : parseFloat(e.target.value);
                                field.onChange(value);
                              }}
                              value={field.value === null ? "" : field.value}
                            />
                          </FormControl>
                          <FormDescription>Maximum stock level</FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <div className="flex justify-end space-x-4 pt-4">
                    <Button
                      type="button"
                      variant="outline"
                      onClick={() => router.push("/inventory/stock")}
                    >
                      Cancel
                    </Button>
                    <Button type="submit" disabled={submitting}>
                      {submitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                      Update Stock
                    </Button>
                  </div>
                </form>
              </Form>
            ) : (
              <div className="text-center py-8">
                <p className="text-muted-foreground">Stock item not found</p>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </MainLayout>
  );
}
