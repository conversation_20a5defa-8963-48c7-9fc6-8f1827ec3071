import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/auth";
import { jwtVerify } from "jose";

// GET /api/activity-logs/latest - Get the latest 5 activity logs
export async function GET(request: NextRequest) {
  try {
    // Get the token from the request cookies directly
    const token = request.cookies.get("session-token");

    if (!token) {
      console.log("[API] /api/activity-logs/latest - No session token found");
      return NextResponse.json(
        { error: "Unauthorized - No session token" },
        { status: 403 }
      );
    }

    // Verify the token
    let userRole = "";
    try {
      const { payload } = await jwtVerify(
        token.value,
        new TextEncoder().encode(process.env.NEXTAUTH_SECRET || "fallback-secret")
      );

      console.log("[API] /api/activity-logs/latest - Token verified, user role:", payload.role);

      userRole = payload.role as string;

      // Check if user has super admin role
      if (userRole !== "SUPER_ADMIN") {
        console.log("[API] /api/activity-logs/latest - User does not have SUPER_ADMIN role:", userRole);
        return NextResponse.json(
          { error: "Unauthorized - Insufficient permissions" },
          { status: 403 }
        );
      }
    } catch (jwtError) {
      console.error("[API] /api/activity-logs/latest - JWT verification failed:", jwtError);
      return NextResponse.json(
        { error: "Unauthorized - Invalid token" },
        { status: 403 }
      );
    }

    // Get the latest 5 logs
    const logs = await prisma.activityLog.findMany({
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            role: true,
          },
        },
      },
      orderBy: {
        timestamp: "desc",
      },
      take: 5,
    });

    return NextResponse.json({ logs });
  } catch (error) {
    console.error("Error fetching latest activity logs:", error);
    return NextResponse.json(
      { error: "Failed to fetch latest activity logs", message: error.message },
      { status: 500 }
    );
  }
}
