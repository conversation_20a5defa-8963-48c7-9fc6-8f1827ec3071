import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/auth";
import { TransferStatus } from "@/generated/prisma";

// Helper function to verify the authentication token
async function verifyAuthToken(request: NextRequest) {
  // Get the session token from cookies
  const token = request.cookies.get("session-token");

  // Check if token exists
  if (!token) {
    console.log("[API] /api/inventory/simple-transfers/[id]/complete - No session token found");
    return {
      authenticated: false,
      error: "Unauthorized - No session token",
      status: 403,
      user: null
    };
  }

  // Verify the token
  try {
    const { payload } = await import("jose").then(({ jwtVerify }) =>
      jwtVerify(
        token.value,
        new TextEncoder().encode(process.env.NEXTAUTH_SECRET || "fallback-secret")
      )
    );

    return {
      authenticated: true,
      user: {
        id: payload.id as string,
        name: payload.name as string,
        email: payload.email as string,
        role: payload.role as string
      }
    };
  } catch (error) {
    console.error("[API] /api/inventory/simple-transfers/[id]/complete - JWT verification failed:", error);
    return {
      authenticated: false,
      error: "Unauthorized - Invalid token",
      status: 403,
      user: null
    };
  }
}

// POST /api/inventory/simple-transfers/[id]/complete - Complete a simple stock transfer
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    console.log("[API] POST /api/inventory/simple-transfers/[id]/complete - Start");
    console.log("[API] Transfer ID:", params.id);

    // Check authentication
    const auth = await verifyAuthToken(request);

    if (!auth.authenticated) {
      return NextResponse.json(
        { error: auth.error },
        { status: auth.status }
      );
    }

    // Check if user has permission to complete transfers
    const hasPermission = ["SUPER_ADMIN", "WAREHOUSE_ADMIN"].includes(auth.user.role);
    if (!hasPermission) {
      return NextResponse.json(
        { error: "Unauthorized - Insufficient permissions" },
        { status: 403 }
      );
    }

    // Get request body
    let body;
    try {
      body = await request.json();
    } catch (error) {
      return NextResponse.json(
        { error: "Invalid request body", message: "Could not parse JSON body" },
        { status: 400 }
      );
    }

    // Get the transfer
    const transfer = await prisma.simpleStockTransfer.findUnique({
      where: { id: params.id },
      include: {
        product: {
          include: {
            storeStock: true,
            warehouseStock: true
          }
        }
      }
    });

    if (!transfer) {
      return NextResponse.json(
        { error: "Transfer not found" },
        { status: 404 }
      );
    }

    // Check if transfer is in APPROVED status
    if (transfer.status !== TransferStatus.APPROVED) {
      return NextResponse.json(
        { error: `Transfer cannot be completed because it is in ${transfer.status} status` },
        { status: 400 }
      );
    }

    // Get the current stock levels directly from the database
    // This ensures we have the most up-to-date values and separate objects
    const storeStock = await prisma.storeStock.findUnique({
      where: { productId: transfer.productId }
    });

    const warehouseStock = await prisma.warehouseStock.findUnique({
      where: { productId: transfer.productId }
    });

    console.log("[API] Current store stock:", storeStock);
    console.log("[API] Current warehouse stock:", warehouseStock);

    // Determine source and destination based on transfer direction
    const sourceIsStore = transfer.fromStore;
    const destinationIsStore = transfer.toStore;

    // Check if source stock exists
    if (sourceIsStore && !storeStock) {
      return NextResponse.json(
        { error: "Source store stock not found" },
        { status: 404 }
      );
    }

    if (!sourceIsStore && !warehouseStock) {
      return NextResponse.json(
        { error: "Source warehouse stock not found" },
        { status: 404 }
      );
    }

    // Check if destination stock exists
    if (destinationIsStore && !storeStock) {
      return NextResponse.json(
        { error: "Destination store stock not found" },
        { status: 404 }
      );
    }

    if (!destinationIsStore && !warehouseStock) {
      return NextResponse.json(
        { error: "Destination warehouse stock not found" },
        { status: 404 }
      );
    }

    // Check if source has enough stock
    const sourceQuantity = sourceIsStore
      ? Number(storeStock!.quantity)
      : Number(warehouseStock!.quantity);

    if (sourceQuantity < Number(transfer.quantity)) {
      return NextResponse.json(
        {
          error: `Insufficient stock in ${sourceIsStore ? "store" : "warehouse"}`,
          available: sourceQuantity,
          required: Number(transfer.quantity)
        },
        { status: 400 }
      );
    }

    // Start a transaction to update both stocks
    console.log("[API] Starting transaction to update stock quantities");

    // Log the current stock levels before the transaction
    console.log("[API] Current store stock before transaction:", storeStock ? storeStock.quantity.toString() : "N/A");
    console.log("[API] Current warehouse stock before transaction:", warehouseStock ? warehouseStock.quantity.toString() : "N/A");

    const updatedTransfer = await prisma.$transaction(async (tx) => {
      let updatedSourceStock;
      let updatedDestinationStock;

      // Decrease source stock
      if (sourceIsStore) {
        console.log("[API] Decreasing store stock by", transfer.quantity.toString());
        updatedSourceStock = await tx.storeStock.update({
          where: { productId: transfer.productId },
          data: {
            quantity: {
              decrement: transfer.quantity
            },
            lastUpdated: new Date()
          }
        });
        console.log("[API] Updated store stock:", updatedSourceStock.quantity.toString());
      } else {
        console.log("[API] Decreasing warehouse stock by", transfer.quantity.toString());
        updatedSourceStock = await tx.warehouseStock.update({
          where: { productId: transfer.productId },
          data: {
            quantity: {
              decrement: transfer.quantity
            },
            lastUpdated: new Date()
          }
        });
        console.log("[API] Updated warehouse stock:", updatedSourceStock.quantity.toString());
      }

      // Increase destination stock
      if (destinationIsStore) {
        console.log("[API] Increasing store stock by", transfer.quantity.toString());
        updatedDestinationStock = await tx.storeStock.update({
          where: { productId: transfer.productId },
          data: {
            quantity: {
              increment: transfer.quantity
            },
            lastUpdated: new Date()
          }
        });
        console.log("[API] Updated store stock:", updatedDestinationStock.quantity.toString());
      } else {
        console.log("[API] Increasing warehouse stock by", transfer.quantity.toString());
        updatedDestinationStock = await tx.warehouseStock.update({
          where: { productId: transfer.productId },
          data: {
            quantity: {
              increment: transfer.quantity
            },
            lastUpdated: new Date()
          }
        });
        console.log("[API] Updated warehouse stock:", updatedDestinationStock.quantity.toString());
      }

      console.log("[API] Stock quantities updated successfully in transaction");

      // Get the previous quantities for history records
      const sourceQuantityBefore = sourceIsStore
        ? Number(storeStock!.quantity)
        : Number(warehouseStock!.quantity);

      const destinationQuantityBefore = destinationIsStore
        ? Number(storeStock!.quantity)
        : Number(warehouseStock!.quantity);

      // Calculate the new quantities after transfer
      const sourceQuantityAfter = sourceQuantityBefore - Number(transfer.quantity);
      const destinationQuantityAfter = destinationQuantityBefore + Number(transfer.quantity);

      // Calculate the change quantities (absolute values)
      const sourceChangeQuantity = Number(transfer.quantity);
      const destinationChangeQuantity = Number(transfer.quantity);

      console.log("[API] Source quantity before:", sourceQuantityBefore);
      console.log("[API] Source quantity after:", sourceQuantityAfter);
      console.log("[API] Source change quantity:", sourceChangeQuantity);
      console.log("[API] Destination quantity before:", destinationQuantityBefore);
      console.log("[API] Destination quantity after:", destinationQuantityAfter);
      console.log("[API] Destination change quantity:", destinationChangeQuantity);

      // Create stock history records for source
      await tx.stockHistory.create({
        data: {
          productId: transfer.productId,
          source: "TRANSFER",
          notes: `Transfer #${transfer.id} from ${sourceIsStore ? "Store" : "Warehouse"}`,
          userId: auth.user.id,
          storeStockId: sourceIsStore ? storeStock!.id : null,
          warehouseStockId: !sourceIsStore ? warehouseStock!.id : null,
          previousQuantity: sourceQuantityBefore,
          newQuantity: sourceQuantityAfter,
          changeQuantity: sourceChangeQuantity,
          referenceId: transfer.id,
          referenceType: "SIMPLE_STOCK_TRANSFER"
        }
      });

      // Create stock history records for destination
      await tx.stockHistory.create({
        data: {
          productId: transfer.productId,
          source: "TRANSFER",
          notes: `Transfer #${transfer.id} to ${destinationIsStore ? "Store" : "Warehouse"}`,
          userId: auth.user.id,
          storeStockId: destinationIsStore ? storeStock!.id : null,
          warehouseStockId: !destinationIsStore ? warehouseStock!.id : null,
          previousQuantity: destinationQuantityBefore,
          newQuantity: destinationQuantityAfter,
          changeQuantity: destinationChangeQuantity,
          referenceId: transfer.id,
          referenceType: "SIMPLE_STOCK_TRANSFER"
        }
      });

      // Update the transfer status
      console.log("[API] Updating transfer status to COMPLETED");
      return tx.simpleStockTransfer.update({
        where: { id: params.id },
        data: {
          status: TransferStatus.COMPLETED,
          completedAt: new Date(),
          notes: body.notes || transfer.notes
        },
        include: {
          product: {
            include: {
              category: true,
              unit: true
            }
          },
          requestedBy: {
            select: {
              id: true,
              name: true,
              email: true,
              role: true
            }
          },
          approvedBy: {
            select: {
              id: true,
              name: true,
              email: true,
              role: true
            }
          }
        }
      });
    });

    console.log("[API] Transaction completed successfully");

    // Verify the stock was updated by fetching the current values
    const updatedStoreStock = await prisma.storeStock.findUnique({
      where: { productId: transfer.productId }
    });

    const updatedWarehouseStock = await prisma.warehouseStock.findUnique({
      where: { productId: transfer.productId }
    });

    console.log("[API] Store stock after transaction:", updatedStoreStock ? updatedStoreStock.quantity.toString() : "N/A");
    console.log("[API] Warehouse stock after transaction:", updatedWarehouseStock ? updatedWarehouseStock.quantity.toString() : "N/A");

    // Log activity
    await prisma.activityLog.create({
      data: {
        userId: auth.user.id,
        action: "COMPLETE_SIMPLE_STOCK_TRANSFER",
        details: `Completed stock transfer #${transfer.id} for ${transfer.product.name}: ${transfer.quantity} units from ${transfer.fromStore ? "Store" : "Warehouse"} to ${transfer.toStore ? "Store" : "Warehouse"}`,
      }
    });

    return NextResponse.json({ transfer: updatedTransfer });
  } catch (error) {
    console.error("Error completing simple stock transfer:", error);

    // Get detailed error information
    let errorMessage = "Failed to complete simple stock transfer";
    let errorDetails = null;
    let statusCode = 500;

    if (error instanceof Error) {
      errorMessage = error.message;
      errorDetails = (error as any).stack;

      // Check for specific error types
      if ((error as any).code === 'P2025') {
        errorMessage = "Record not found. The transfer may have been deleted.";
        statusCode = 404;
      } else if ((error as any).code === 'P2002') {
        errorMessage = "Unique constraint violation. This operation conflicts with existing data.";
        statusCode = 409;
      } else if ((error as any).code === 'P2003') {
        errorMessage = "Foreign key constraint violation. Referenced record does not exist.";
        statusCode = 400;
      }
    }

    console.error("Error details:", {
      message: errorMessage,
      details: errorDetails,
      code: (error as any).code,
      statusCode
    });

    return NextResponse.json(
      {
        error: errorMessage,
        details: errorDetails,
        code: (error as any).code
      },
      { status: statusCode }
    );
  }
}
