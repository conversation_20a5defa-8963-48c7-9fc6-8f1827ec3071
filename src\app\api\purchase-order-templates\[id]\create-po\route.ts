import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { prisma } from '@/lib/prisma';
import { verifyAuthToken } from '@/lib/auth-utils';

// Schema for creating PO from template
const createPOFromTemplateSchema = z.object({
  orderDate: z.string().min(1, { message: "Order date is required" }).transform(val => new Date(val)),
  notes: z.string().optional(),
  adjustments: z.array(z.object({
    productId: z.string(),
    quantity: z.number().positive().optional(),
    unitPrice: z.number().positive().optional(),
  })).optional(),
});

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication
    const auth = await verifyAuthToken(request);
    if (!auth.authenticated) {
      return NextResponse.json({ error: auth.error }, { status: auth.status });
    }

    // Only allow SUPER_ADMIN and WAREHOUSE_ADMIN to create POs from templates
    if (!["SUPER_ADMIN", "WAREHOUSE_ADMIN"].includes(auth.user.role)) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    const templateId = params.id;

    // Parse and validate request body
    const body = await request.json();
    const validatedData = createPOFromTemplateSchema.parse(body);

    // Fetch template with all related data
    const template = await prisma.purchaseOrderTemplate.findUnique({
      where: { id: templateId, isActive: true },
      include: {
        supplier: true,
        items: {
          include: {
            product: {
              include: {
                category: true,
                unit: true,
              },
            },
          },
        },
      },
    });

    if (!template) {
      return NextResponse.json(
        { error: "Purchase order template not found or inactive" },
        { status: 404 }
      );
    }

    // Prepare items for the new PO, applying any adjustments
    const adjustmentsMap = new Map(
      (validatedData.adjustments || []).map(adj => [adj.productId, adj])
    );

    const poItems = template.items.map(templateItem => {
      const adjustment = adjustmentsMap.get(templateItem.productId);
      return {
        productId: templateItem.productId,
        quantity: adjustment?.quantity ?? Number(templateItem.quantity),
        unitPrice: adjustment?.unitPrice ?? Number(templateItem.unitPrice),
      };
    });

    // Calculate totals
    const subtotal = poItems.reduce((sum, item) => sum + (item.quantity * item.unitPrice), 0);
    const tax = template.taxPercentage ? (subtotal * Number(template.taxPercentage)) / 100 : 0;
    const total = subtotal + tax;

    // Create purchase order from template
    const purchaseOrder = await prisma.purchaseOrder.create({
      data: {
        supplierId: template.supplierId,
        orderDate: validatedData.orderDate,
        subtotal,
        tax,
        taxPercentage: template.taxPercentage,
        total,
        notes: validatedData.notes || template.notes,
        status: 'DRAFT',
        createdById: auth.user.id,
        items: {
          create: poItems.map(item => ({
            productId: item.productId,
            quantity: item.quantity,
            unitPrice: item.unitPrice,
            subtotal: item.quantity * item.unitPrice,
          })),
        },
      },
      include: {
        supplier: true,
        createdBy: {
          select: { id: true, name: true, email: true },
        },
        items: {
          include: {
            product: {
              include: {
                category: true,
                unit: true,
              },
            },
          },
        },
      },
    });

    return NextResponse.json({
      message: "Purchase order created successfully from template",
      purchaseOrder,
      templateUsed: {
        id: template.id,
        name: template.name,
      },
    }, { status: 201 });
  } catch (error) {
    console.error("Error creating purchase order from template:", error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Validation failed", details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: "Failed to create purchase order from template", message: (error as Error).message },
      { status: 500 }
    );
  }
}
