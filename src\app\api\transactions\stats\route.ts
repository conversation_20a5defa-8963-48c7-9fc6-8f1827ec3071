import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/auth";
import { verifyAuthToken } from "@/lib/auth-utils";

export async function GET(req: NextRequest) {
  try {
    // Check authentication
    const auth = await verifyAuthToken(req);
    if (!auth.authenticated) {
      return NextResponse.json({ error: auth.error }, { status: auth.status });
    }

    // Get query parameters for date filtering
    const url = new URL(req.url);
    const dateParam = url.searchParams.get("date");

    // Set up date ranges based on parameters
    let currentPeriodStart: Date;
    let currentPeriodEnd: Date;
    let previousPeriodStart: Date;
    let previousPeriodEnd: Date;

    if (dateParam) {
      // Import the utility function for date processing
      const { processDateRange, DEFAULT_TIMEZONE, toZonedTime } = await import("@/lib/utils");

      // If a specific date is provided, use it for filtering
      const selectedDate = new Date(dateParam);

      // Process current period (the selected date) with proper timezone handling
      const { start: start1, end: end1 } = processDateRange(selectedDate, selectedDate);
      currentPeriodStart = start1 || new Date(selectedDate);
      currentPeriodEnd = end1 || new Date(selectedDate);

      // Previous period: the day before the selected date
      const previousDate = new Date(selectedDate);
      previousDate.setDate(previousDate.getDate() - 1);

      // Process previous period with proper timezone handling
      const { start: start2, end: end2 } = processDateRange(previousDate, previousDate);
      previousPeriodStart = start2 || previousDate;
      previousPeriodEnd = end2 || previousDate;

      // Log the processed dates for debugging
      console.log("[API] /api/transactions/stats - Date ranges:", {
        currentPeriod: {
          start: currentPeriodStart.toISOString(),
          end: currentPeriodEnd.toISOString()
        },
        previousPeriod: {
          start: previousPeriodStart.toISOString(),
          end: previousPeriodEnd.toISOString()
        }
      });
    } else {
      // Default: compare last 30 days with previous 30 days
      const currentDate = new Date();

      // Current period: last 30 days
      currentPeriodEnd = new Date(currentDate);
      currentPeriodStart = new Date(currentDate);
      currentPeriodStart.setDate(currentPeriodStart.getDate() - 30);

      // Previous period: 30-60 days ago
      previousPeriodEnd = new Date(currentPeriodStart);
      previousPeriodEnd.setDate(previousPeriodEnd.getDate() - 1);
      previousPeriodStart = new Date(previousPeriodEnd);
      previousPeriodStart.setDate(previousPeriodStart.getDate() - 30);
    }

    // Get total revenue and sales count for current period
    const currentPeriodStats = await prisma.transaction.aggregate({
      where: {
        createdAt: {
          gte: currentPeriodStart,
          lte: currentPeriodEnd,
        },
        paymentStatus: "PAID", // Only count paid transactions
      },
      _sum: {
        total: true,
      },
      _count: {
        id: true,
      },
    });

    // Get total revenue and sales count for previous period
    const previousPeriodStats = await prisma.transaction.aggregate({
      where: {
        createdAt: {
          gte: previousPeriodStart,
          lte: previousPeriodEnd,
        },
        paymentStatus: "PAID", // Only count paid transactions
      },
      _sum: {
        total: true,
      },
      _count: {
        id: true,
      },
    });

    // Calculate current period values
    const totalRevenue = Number(currentPeriodStats._sum.total || 0);
    const totalSales = currentPeriodStats._count.id || 0;

    // Calculate previous period values
    const previousRevenue = Number(previousPeriodStats._sum.total || 0);
    const previousSales = previousPeriodStats._count.id || 0;

    // Calculate percentage changes
    let revenueChange = 0;
    let salesChange = 0;

    if (previousRevenue > 0) {
      revenueChange = ((totalRevenue - previousRevenue) / previousRevenue) * 100;
    }

    if (previousSales > 0) {
      salesChange = ((totalSales - previousSales) / previousSales) * 100;
    }

    return NextResponse.json({
      totalRevenue,
      totalSales,
      revenueChange,
      salesChange,
    });
  } catch (error) {
    console.error("Error fetching transaction statistics:", error);
    return NextResponse.json(
      { error: "Failed to fetch transaction statistics" },
      { status: 500 }
    );
  }
}
