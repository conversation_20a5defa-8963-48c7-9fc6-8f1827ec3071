import { NextRequest, NextResponse } from "next/server";
import { prisma, hashPassword, verifyPassword } from "@/auth";
import { z } from "zod";
import { cookies } from "next/headers";
import { SignJWT } from "jose";
import { nanoid } from "nanoid";

// Define login schema for validation
const loginSchema = z.object({
  email: z.string().email({ message: "Invalid email address" }),
  password: z.string().min(6, { message: "Password must be at least 6 characters" }),
});

// POST /api/login - Custom login endpoint
export async function POST(request: NextRequest) {
  try {
    const data = await request.json();
    console.log("Login attempt with:", { email: data.email });

    // Validate input data
    const validationResult = loginSchema.safeParse(data);
    if (!validationResult.success) {
      console.log("Validation failed:", validationResult.error);
      return NextResponse.json(
        { error: "Validation failed", details: validationResult.error.format() },
        { status: 400 }
      );
    }

    // Find user by email
    console.log("Looking for user with email:", data.email);
    const user = await prisma.user.findUnique({
      where: { email: data.email },
    });

    console.log("User found:", user ? "Yes" : "No");

    if (!user || !user.password || !user.active) {
      console.log("User not found or inactive");
      return NextResponse.json(
        { error: "Invalid email or password" },
        { status: 401 }
      );
    }

    // Verify password
    console.log("Verifying password...");
    const passwordMatch = await verifyPassword(data.password, user.password);
    console.log("Password match:", passwordMatch ? "Yes" : "No");

    if (!passwordMatch) {
      console.log("Password does not match");
      return NextResponse.json(
        { error: "Invalid email or password" },
        { status: 401 }
      );
    }

    // Log activity
    console.log("Creating activity log entry");
    await prisma.activityLog.create({
      data: {
        userId: user.id,
        action: "LOGIN",
        details: "User logged in via custom API",
      },
    });

    // Create a session token
    const token = await new SignJWT({
      id: user.id,
      name: user.name,
      email: user.email,
      role: user.role,
    })
      .setProtectedHeader({ alg: "HS256" })
      .setJti(nanoid())
      .setIssuedAt()
      .setExpirationTime("24h")
      .sign(new TextEncoder().encode(process.env.NEXTAUTH_SECRET || "fallback-secret"));

    // Set cookie
    const cookieStore = cookies();
    await cookieStore.set("session-token", token, {
      httpOnly: true,
      secure: process.env.NODE_ENV === "production",
      sameSite: "lax",
      maxAge: 60 * 60 * 24, // 1 day
      path: "/",
    });

    console.log("Login successful");
    return NextResponse.json({
      success: true,
      user: {
        id: user.id,
        name: user.name,
        email: user.email,
        role: user.role,
      },
    });
  } catch (error: any) {
    console.error("Login error:", error);
    return NextResponse.json(
      { error: "An error occurred", message: error.message },
      { status: 500 }
    );
  }
}
