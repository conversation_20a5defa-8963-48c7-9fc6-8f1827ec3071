"use client";

import { useState, useEffect } from "react";
import { usePOS } from "@/contexts/POSContext";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  ChevronDown,
  ChevronRight,
  DollarSign,
  Monitor,
  Wallet,
  AlertTriangle,
  Shield,
  Lock,
} from "lucide-react";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { toast } from "sonner";

interface DrawerSession {
  id: string;
  openingBalance: number;
  businessDate: string;
  drawer: {
    name: string;
  };
  terminal: {
    name: string;
  };
}

export function DrawerInfo() {
  const { focusSearchInput } = usePOS();
  const [isOpen, setIsOpen] = useState(false);
  const [drawerSession, setDrawerSession] = useState<DrawerSession | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [cashSales, setCashSales] = useState(0);
  const [showCloseModal, setShowCloseModal] = useState(false);
  const [isClosing, setIsClosing] = useState(false);

  // Cash reconciliation state
  const [actualCashAmount, setActualCashAmount] = useState<string>("");
  const [discrepancyReason, setDiscrepancyReason] = useState<string>("");
  const [discrepancyCategory, setDiscrepancyCategory] = useState<string>("");

  // Opening balance state
  const [showOpenModal, setShowOpenModal] = useState(false);
  const [openingBalance, setOpeningBalance] = useState<string>("");
  const [isOpening, setIsOpening] = useState(false);

  // Enhanced security state
  const [showReAuthDialog, setShowReAuthDialog] = useState(false);
  const [reAuthPassword, setReAuthPassword] = useState("");
  const [isReAuthenticating, setIsReAuthenticating] = useState(false);
  const [largeDiscrepancyDetected, setLargeDiscrepancyDetected] = useState(false);
  const LARGE_DISCREPANCY_THRESHOLD = 50000; // IDR 50,000 threshold

  useEffect(() => {
    fetchDrawerSession();
  }, []);

  // Separate effect for transaction completion listener and periodic refresh
  useEffect(() => {
    const handleTransactionCompleted = (event: CustomEvent) => {
      console.log("Transaction completed, refreshing drawer info...", event.detail);

      // Only refresh if we have an active drawer session
      if (drawerSession?.id) {
        fetchCashSalesForSession(drawerSession.id);
      }
    };

    // Add event listener for real-time updates
    window.addEventListener("transactionCompleted", handleTransactionCompleted as EventListener);

    // Set up periodic refresh as backup (every 30 seconds)
    const intervalId = setInterval(() => {
      if (drawerSession?.id) {
        fetchCashSalesForSession(drawerSession.id);
      }
    }, 30000); // 30 seconds

    // Cleanup event listener and interval on unmount
    return () => {
      window.removeEventListener(
        "transactionCompleted",
        handleTransactionCompleted as EventListener
      );
      clearInterval(intervalId);
    };
  }, [drawerSession?.id]); // Re-run when drawer session changes

  const fetchDrawerSession = async (context = "general") => {
    console.log(`DrawerInfo: Fetching drawer session (context: ${context})`);
    setIsLoading(true);
    try {
      const response = await fetch("/api/drawer-sessions/current", {
        cache: "no-cache",
        headers: {
          "Cache-Control": "no-cache",
        },
      });
      if (response.ok) {
        const data = await response.json();
        console.log(`DrawerInfo: Fetched drawer session (context: ${context}):`, data);
        setDrawerSession(data.session);

        // Fetch cash sales for the current session
        if (data.session) {
          fetchCashSalesForSession(data.session.id);
        }
      } else {
        console.error(
          `Failed to fetch drawer session (context: ${context}):`,
          response.status,
          response.statusText
        );
      }
    } catch (error) {
      console.error(`Error fetching drawer session (context: ${context}):`, error);
    } finally {
      setIsLoading(false);
    }
  };

  const fetchCashSalesForSession = async (sessionId: string) => {
    try {
      // Fetch cash sales for the specific drawer session
      const response = await fetch(`/api/drawer-sessions/${sessionId}/cash-sales`, {
        cache: "no-cache",
        headers: {
          "Cache-Control": "no-cache",
        },
      });
      if (response.ok) {
        const data = await response.json();
        console.log("Updated cash sales:", data.totalCashSales);
        setCashSales(data.totalCashSales || 0);
      }
    } catch (error) {
      console.error("Error fetching cash sales:", error);
    }
  };

  // Method to refresh cash sales data (can be called externally)
  const refreshCashSales = () => {
    if (drawerSession?.id) {
      fetchCashSalesForSession(drawerSession.id);
    }
  };

  // Initialize session timeout for drawer operations
  const initializeSessionTimeout = async () => {
    try {
      const response = await fetch("/api/session-timeout/drawer_session", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          timeoutMinutes: 30,
          warningMinutes: 5,
        }),
      });

      if (response.ok) {
        console.log("Session timeout initialized for drawer operations");
      }
    } catch (error) {
      console.error("Failed to initialize session timeout:", error);
    }
  };

  const handleOpenDrawer = () => {
    setOpeningBalance("");
    setShowOpenModal(true);
  };

  const handleConfirmOpenDrawer = async () => {
    const balance = parseFloat(openingBalance);

    if (isNaN(balance) || balance < 0) {
      toast.error("Please enter a valid opening balance");
      return;
    }

    // Prevent duplicate submissions
    if (isOpening) {
      return;
    }

    setIsOpening(true);

    try {
      // Get user's assigned drawer and terminal
      const drawerResponse = await fetch("/api/users/current/drawer");

      if (!drawerResponse.ok) {
        const errorText = await drawerResponse.text();
        console.error(
          "DrawerInfo: Failed to get drawer information:",
          drawerResponse.status,
          errorText
        );
        toast.error("Failed to get drawer information");
        return;
      }

      const drawerData = await drawerResponse.json();

      if (!drawerData.drawer) {
        console.error("DrawerInfo: No drawer assigned to user");
        toast.error("No drawer assigned to your account");
        return;
      }

      const today = new Date().toISOString().split("T")[0];
      const requestBody = {
        drawerId: drawerData.drawer.id,
        terminalId: drawerData.drawer.terminal.id,
        businessDate: today,
        openingBalance: balance,
      };

      const response = await fetch("/api/drawer-sessions", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(requestBody),
      });

      // Clone the response to handle potential parsing issues
      const responseClone = response.clone();
      const responseText = await responseClone.text();

      try {
        if (response.ok) {
          // Parse the response text as JSON instead of using response.json()
          const result = JSON.parse(responseText);

          // Validate the response structure
          if (!result || !result.session || !result.session.id) {
            console.error("DrawerInfo: Invalid response structure:", result);
            toast.error("Invalid response from server");

            // Still try to refresh in case the session was created
            setTimeout(() => {
              fetchDrawerSession("after-invalid-response-structure");
            }, 1000);
            return;
          }

          toast.success("Drawer opened successfully");
          setShowOpenModal(false);
          setOpeningBalance("");

          // Initialize session timeout for drawer operations
          await initializeSessionTimeout();

          // Dispatch custom event to notify other components about drawer session creation
          window.dispatchEvent(
            new CustomEvent("drawerSessionCreated", {
              detail: {
                sessionId: result.session.id,
                drawerId: result.session.drawerId,
                terminalId: result.session.terminalId,
              },
            })
          );

          // Refresh the drawer session with a small delay to ensure database consistency
          setTimeout(() => {
            fetchDrawerSession("after-successful-creation");
          }, 500);
        } else {
          // Parse error response from the text we already have
          try {
            const errorData = JSON.parse(responseText);
            console.error("DrawerInfo: Drawer session creation failed:", {
              status: response.status,
              statusText: response.statusText,
              error: errorData,
            });
            toast.error(errorData.error || "Failed to create drawer session");
          } catch (errorParseError) {
            console.error("DrawerInfo: Could not parse error response:", errorParseError);
            toast.error(
              `Failed to create drawer session (${response.status}: ${response.statusText})`
            );
          }
        }
      } catch (parseError) {
        console.error("DrawerInfo: Error parsing response:", parseError);

        // If the response was supposed to be successful but we couldn't parse it,
        // the session might have been created anyway
        if (response.ok) {
          toast.error("Server response parsing failed, checking if drawer was opened...");

          // Check if the session was actually created
          setTimeout(() => {
            fetchDrawerSession("after-parse-error-ok-response");
          }, 1000);
        } else {
          toast.error(
            `Failed to create drawer session (${response.status}: ${response.statusText})`
          );
        }
      }
    } catch (error) {
      console.error("DrawerInfo: Error opening drawer:", error);

      if (error instanceof TypeError && error.message.includes("fetch")) {
        toast.error("Network error: Unable to connect to server");
      } else if (error instanceof Error) {
        toast.error(`Failed to open drawer: ${error.message}`);
      } else {
        toast.error("Failed to open drawer: Unknown error");
      }
    } finally {
      setIsOpening(false);
    }
  };

  const handleOpenCloseModal = () => {
    if (!drawerSession) return;

    // Set default actual cash amount to expected balance
    const expectedBalance = Number(drawerSession.openingBalance) + Number(cashSales);
    setActualCashAmount(expectedBalance.toString());
    setDiscrepancyReason("");
    setDiscrepancyCategory("");
    setLargeDiscrepancyDetected(false);
    setShowCloseModal(true);
  };

  // Check for large discrepancy and handle re-authentication
  const checkLargeDiscrepancy = (actualAmount: number, expectedBalance: number) => {
    const discrepancy = Math.abs(actualAmount - expectedBalance);
    const isLarge = discrepancy >= LARGE_DISCREPANCY_THRESHOLD;
    setLargeDiscrepancyDetected(isLarge);
    return isLarge;
  };

  // Handle re-authentication for large discrepancies
  const handleReAuthentication = async () => {
    if (!reAuthPassword.trim()) {
      toast.error("Please enter your password");
      return;
    }

    setIsReAuthenticating(true);
    try {
      // Proceed with drawer close including re-auth password
      await performDrawerClose(true);
    } catch (error) {
      console.error("Re-authentication failed:", error);
    } finally {
      setIsReAuthenticating(false);
    }
  };

  const handleCloseDrawer = async () => {
    console.log("handleCloseDrawer called");

    if (!drawerSession) {
      console.log("No drawer session found");
      toast.error("No active drawer session found");
      return;
    }

    console.log("Drawer session:", drawerSession);
    console.log("Actual cash amount:", actualCashAmount);

    const actualAmount = parseFloat(actualCashAmount);
    if (isNaN(actualAmount) || actualAmount < 0) {
      console.log("Invalid actual amount:", actualAmount);
      toast.error("Please enter a valid cash amount");
      return;
    }

    const expectedBalance = Number(drawerSession.openingBalance) + Number(cashSales);
    const discrepancy = actualAmount - expectedBalance;

    console.log("Validation passed:", {
      actualAmount,
      expectedBalance,
      discrepancy,
      discrepancyReason: discrepancyReason.trim(),
    });

    // Check if discrepancy exists and reason is required
    if (discrepancy !== 0 && !discrepancyReason.trim()) {
      console.log("Discrepancy exists but no reason provided");
      toast.error("Please provide a reason for the cash discrepancy");
      return;
    }

    // Check for large discrepancy (enhanced security feature)
    const isLargeDiscrepancy = checkLargeDiscrepancy(actualAmount, expectedBalance);

    if (isLargeDiscrepancy) {
      console.log("Large discrepancy detected, requiring re-authentication");
      setShowReAuthDialog(true);
      return;
    }

    // Proceed with normal drawer close
    await performDrawerClose(false);
  };

  // Perform the actual drawer close operation
  const performDrawerClose = async (includeReAuth: boolean) => {
    if (!drawerSession) return;

    const actualAmount = parseFloat(actualCashAmount);
    const expectedBalance = Number(drawerSession.openingBalance) + Number(cashSales);
    const discrepancy = actualAmount - expectedBalance;

    console.log("Starting drawer close process...");
    setIsClosing(true);

    try {
      const requestBody: any = {
        actualClosingBalance: actualAmount,
        notes: discrepancyReason.trim() || undefined,
        discrepancyCategory: discrepancyCategory || undefined,
      };

      // Include re-authentication password for large discrepancies
      if (includeReAuth && reAuthPassword) {
        requestBody.reAuthPassword = reAuthPassword;
      }

      console.log("Closing drawer with request:", {
        sessionId: drawerSession.id,
        body: requestBody,
        expectedBalance,
        discrepancy,
        includeReAuth,
        hasReAuthPassword: !!reAuthPassword,
      });

      console.log("Making fetch request to:", `/api/drawer-sessions/${drawerSession.id}/close`);

      const response = await fetch(`/api/drawer-sessions/${drawerSession.id}/close`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(requestBody),
      });

      console.log("Close drawer response received:", {
        status: response.status,
        statusText: response.statusText,
        ok: response.ok,
        headers: Object.fromEntries(response.headers.entries()),
      });

      if (response.ok) {
        try {
          const result = await response.json();
          console.log("Drawer closed successfully:", result);

          toast.success("Drawer closed successfully");
          setDrawerSession(null);
          setCashSales(0);
          setShowCloseModal(false);
          setActualCashAmount("");
          setDiscrepancyReason("");
          setDiscrepancyCategory("");

          // Reset security state
          setShowReAuthDialog(false);
          setReAuthPassword("");
          setLargeDiscrepancyDetected(false);

          // Dispatch custom event to notify other components about drawer session closure
          window.dispatchEvent(
            new CustomEvent("drawerSessionClosed", {
              detail: {
                sessionId: drawerSession.id,
              },
            })
          );
        } catch (parseError) {
          console.log("Error parsing success response:", parseError);
          toast.error("Drawer closed but response parsing failed");
        }
      } else {
        try {
          const errorData = await response.json();
          console.log("Close drawer API error:", {
            status: response.status,
            statusText: response.statusText,
            error: errorData,
          });

          // Handle re-authentication requirement for large discrepancies
          if (response.status === 403 && errorData.requireReAuth) {
            console.log("Re-authentication required for large discrepancy");
            if (includeReAuth) {
              // Re-authentication failed
              toast.error(errorData.error || "Invalid password. Please try again.");
            } else {
              // Large discrepancy detected, show re-auth dialog
              setShowReAuthDialog(true);
              setLargeDiscrepancyDetected(true);
              toast.error("Large discrepancy detected. Re-authentication required.");
            }
          } else {
            toast.error(errorData.error || "Failed to close drawer");
          }
        } catch (parseError) {
          console.log("Error parsing error response:", parseError);
          toast.error(`Failed to close drawer (${response.status}: ${response.statusText})`);
        }
      }
    } catch (error) {
      console.error("Error closing drawer:", {
        error,
        message: error instanceof Error ? error.message : String(error),
        stack: error instanceof Error ? error.stack : undefined,
        name: error instanceof Error ? error.name : undefined,
      });

      if (error instanceof TypeError && error.message.includes("fetch")) {
        toast.error("Network error: Unable to connect to server");
      } else if (error instanceof Error) {
        toast.error(`Failed to close drawer: ${error.message}`);
      } else {
        toast.error("Failed to close drawer: Unknown error");
      }
    } finally {
      console.log("Drawer close process finished, resetting isClosing state");
      setIsClosing(false);
    }
  };

  const expectedBalance = drawerSession
    ? Number(drawerSession.openingBalance) + Number(cashSales)
    : 0;

  // Handle clicks on the cash drawer card title area for focus management
  const handleCardHeaderClick = (e: React.MouseEvent) => {
    const target = e.target as HTMLElement;

    // Check if the click was on the chevron arrow button or its SVG elements
    const isChevronClick =
      target.closest("svg") === e.currentTarget.querySelector("svg:last-child") ||
      target.tagName.toLowerCase() === "svg" ||
      target.tagName.toLowerCase() === "path";

    // If click was on title area (not the arrow), focus the search input
    if (!isChevronClick) {
      console.log("DrawerInfo: Title area clicked, focusing search input");
      // Small delay to ensure the click event completes before focusing
      setTimeout(() => {
        focusSearchInput(100);
      }, 50);
    }
  };

  return (
    <Card>
      <Collapsible open={isOpen} onOpenChange={setIsOpen}>
        <CollapsibleTrigger asChild>
          <CardHeader className="cursor-pointer hover:bg-gray-50" onClick={handleCardHeaderClick}>
            <CardTitle className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <Wallet className="h-5 w-5" />
                <span>Cash Drawer Info</span>
              </div>
              {isOpen ? <ChevronDown className="h-4 w-4" /> : <ChevronRight className="h-4 w-4" />}
            </CardTitle>
          </CardHeader>
        </CollapsibleTrigger>
        <CollapsibleContent>
          <CardContent>
            {isLoading ? (
              <div className="text-center py-4">
                <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600 mx-auto"></div>
                <p className="text-sm text-gray-600 mt-2">Loading drawer info...</p>
              </div>
            ) : drawerSession ? (
              <div className="space-y-4">
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600 flex items-center">
                      <Monitor className="h-4 w-4 mr-1" />
                      Terminal:
                    </span>
                    <span className="font-medium">{drawerSession.terminal.name}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600 flex items-center">
                      <Wallet className="h-4 w-4 mr-1" />
                      Drawer:
                    </span>
                    <span className="font-medium">{drawerSession.drawer.name}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600 flex items-center">
                      <DollarSign className="h-4 w-4 mr-1" />
                      Opening Balance:
                    </span>
                    <span className="font-medium">
                      Rp {Number(drawerSession.openingBalance).toLocaleString("id-ID")}
                    </span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">Cash Sales:</span>
                    <span className="font-medium text-green-600">
                      Rp {Number(cashSales).toLocaleString("id-ID")}
                    </span>
                  </div>
                  <div className="flex items-center justify-between border-t pt-2">
                    <span className="text-sm font-medium">Expected Balance:</span>
                    <span className="font-bold">
                      Rp {Number(expectedBalance).toLocaleString("id-ID")}
                    </span>
                  </div>
                </div>

                <Button
                  onClick={handleOpenCloseModal}
                  variant="outline"
                  className="w-full text-red-600 hover:text-red-700 hover:bg-red-50"
                >
                  Close Drawer
                </Button>
              </div>
            ) : (
              <div className="text-center py-4 space-y-3">
                <p className="text-sm text-gray-600">No active drawer session</p>
                <Button
                  onClick={handleOpenDrawer}
                  variant="outline"
                  className="w-full text-green-600 hover:text-green-700 hover:bg-green-50"
                >
                  Open Drawer
                </Button>
              </div>
            )}
          </CardContent>
        </CollapsibleContent>
      </Collapsible>

      {/* Close Drawer Modal with Cash Reconciliation */}
      <Dialog open={showCloseModal} onOpenChange={setShowCloseModal}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>Close Drawer - Cash Reconciliation</DialogTitle>
            <DialogDescription>
              Please count the actual cash in the drawer and enter the amount below. This will end
              your current session.
            </DialogDescription>
          </DialogHeader>

          {drawerSession && (
            <div className="space-y-4">
              {/* Summary Section */}
              <div className="space-y-2 p-3 bg-gray-50 rounded-lg">
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">Opening Balance:</span>
                  <span>Rp {Number(drawerSession.openingBalance).toLocaleString("id-ID")}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">Cash Sales:</span>
                  <span className="text-green-600">
                    Rp {Number(cashSales).toLocaleString("id-ID")}
                  </span>
                </div>
                <div className="flex justify-between font-bold border-t pt-2">
                  <span>Expected Balance:</span>
                  <span>
                    Rp{" "}
                    {(Number(drawerSession.openingBalance) + Number(cashSales)).toLocaleString(
                      "id-ID"
                    )}
                  </span>
                </div>
              </div>

              {/* Actual Cash Amount Input */}
              <div className="space-y-2">
                <Label htmlFor="actualCash">Actual Cash Amount *</Label>
                <Input
                  id="actualCash"
                  type="number"
                  step="0.01"
                  min="0"
                  value={actualCashAmount}
                  onChange={(e) => setActualCashAmount(e.target.value)}
                  placeholder="Enter actual cash amount"
                  className="text-right"
                />
              </div>

              {/* Discrepancy Display */}
              {actualCashAmount && !isNaN(parseFloat(actualCashAmount)) && (
                <div className="p-3 rounded-lg border">
                  {(() => {
                    const actualAmount = parseFloat(actualCashAmount);
                    const expectedBalance =
                      Number(drawerSession.openingBalance) + Number(cashSales);
                    const discrepancy = actualAmount - expectedBalance;

                    if (discrepancy === 0) {
                      return (
                        <div className="flex items-center text-green-600">
                          <DollarSign className="h-4 w-4 mr-2" />
                          <span className="font-medium">Perfect match! No discrepancy.</span>
                        </div>
                      );
                    } else {
                      const isLargeDiscrepancy =
                        Math.abs(discrepancy) >= LARGE_DISCREPANCY_THRESHOLD;

                      return (
                        <div className="space-y-2">
                          <div
                            className={`flex items-center ${isLargeDiscrepancy ? "text-red-600" : "text-orange-600"}`}
                          >
                            <AlertTriangle className="h-4 w-4 mr-2" />
                            <span className="font-medium">
                              {discrepancy > 0 ? "Cash Surplus" : "Cash Shortage"}: Rp{" "}
                              {Math.abs(discrepancy).toLocaleString("id-ID")}
                            </span>
                          </div>
                          {isLargeDiscrepancy && (
                            <Alert className="border-red-200 bg-red-50">
                              <Shield className="h-4 w-4 text-red-600" />
                              <AlertDescription className="text-red-800">
                                <strong>Large Discrepancy Detected!</strong>
                                <br />
                                This discrepancy exceeds Rp{" "}
                                {LARGE_DISCREPANCY_THRESHOLD.toLocaleString("id-ID")} and will
                                require password re-authentication for security purposes.
                              </AlertDescription>
                            </Alert>
                          )}
                        </div>
                      );
                    }
                  })()}
                </div>
              )}

              {/* Discrepancy Details (shown only when there's a discrepancy) */}
              {actualCashAmount &&
                !isNaN(parseFloat(actualCashAmount)) &&
                (() => {
                  const actualAmount = parseFloat(actualCashAmount);
                  const expectedBalance = Number(drawerSession.openingBalance) + Number(cashSales);
                  const discrepancy = actualAmount - expectedBalance;
                  return discrepancy !== 0;
                })() && (
                  <div className="space-y-4">
                    <div className="space-y-2">
                      <Label htmlFor="discrepancyCategory">Discrepancy Category</Label>
                      <Select value={discrepancyCategory} onValueChange={setDiscrepancyCategory}>
                        <SelectTrigger>
                          <SelectValue placeholder="Select category (optional)" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="COUNTING_ERROR">Counting Error</SelectItem>
                          <SelectItem value="SYSTEM_ERROR">System Error</SelectItem>
                          <SelectItem value="CASH_SHORTAGE">Cash Shortage</SelectItem>
                          <SelectItem value="CASH_SURPLUS">Cash Surplus</SelectItem>
                          <SelectItem value="REGISTER_ERROR">Register Error</SelectItem>
                          <SelectItem value="TRAINING_ERROR">Training Error</SelectItem>
                          <SelectItem value="PROCEDURAL_ERROR">Procedural Error</SelectItem>
                          <SelectItem value="THEFT_SUSPECTED">Theft Suspected</SelectItem>
                          <SelectItem value="UNKNOWN">Unknown</SelectItem>
                          <SelectItem value="OTHER">Other</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="discrepancyReason">Reason for Discrepancy *</Label>
                      <Textarea
                        id="discrepancyReason"
                        value={discrepancyReason}
                        onChange={(e) => setDiscrepancyReason(e.target.value)}
                        placeholder="Please explain the reason for the cash discrepancy..."
                        rows={3}
                      />
                    </div>
                  </div>
                )}
            </div>
          )}

          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => {
                setShowCloseModal(false);
                setActualCashAmount("");
                setDiscrepancyReason("");
                setDiscrepancyCategory("");
              }}
              disabled={isClosing}
            >
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={() => {
                console.log("Close Drawer button clicked");
                handleCloseDrawer();
              }}
              disabled={(() => {
                const disabled =
                  isClosing ||
                  !actualCashAmount ||
                  isNaN(parseFloat(actualCashAmount)) ||
                  (actualCashAmount &&
                    !isNaN(parseFloat(actualCashAmount)) &&
                    (() => {
                      const actualAmount = parseFloat(actualCashAmount);
                      const expectedBalance = drawerSession
                        ? Number(drawerSession.openingBalance) + Number(cashSales)
                        : 0;
                      const discrepancy = actualAmount - expectedBalance;
                      return discrepancy !== 0 && !discrepancyReason.trim();
                    })());

                console.log("Close Drawer button disabled state:", {
                  disabled,
                  isClosing,
                  actualCashAmount,
                  isValidAmount: !isNaN(parseFloat(actualCashAmount || "")),
                  discrepancyCheck:
                    actualCashAmount && !isNaN(parseFloat(actualCashAmount))
                      ? (() => {
                          const actualAmount = parseFloat(actualCashAmount);
                          const expectedBalance = drawerSession
                            ? Number(drawerSession.openingBalance) + Number(cashSales)
                            : 0;
                          const discrepancy = actualAmount - expectedBalance;
                          return {
                            discrepancy,
                            needsReason: discrepancy !== 0 && !discrepancyReason.trim(),
                          };
                        })()
                      : null,
                });

                return disabled;
              })()}
            >
              {isClosing ? "Closing..." : "Close Drawer"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Open Drawer Modal with Opening Balance */}
      <Dialog
        open={showOpenModal}
        onOpenChange={(open) => {
          // Prevent closing the modal while opening drawer
          if (!open && isOpening) {
            console.log("DrawerInfo: Preventing modal close while drawer is being opened");
            return;
          }
          setShowOpenModal(open);
          if (!open) {
            // Reset form when modal is closed
            setOpeningBalance("");
          }
        }}
      >
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>Open Drawer - Set Opening Balance</DialogTitle>
            <DialogDescription>
              Please count the cash in the drawer and enter the opening balance to start your
              session.
            </DialogDescription>
          </DialogHeader>

          <form
            id="open-drawer-form"
            onSubmit={(e) => {
              e.preventDefault();
              if (
                !isOpening &&
                openingBalance &&
                !isNaN(parseFloat(openingBalance)) &&
                parseFloat(openingBalance) >= 0
              ) {
                handleConfirmOpenDrawer();
              }
            }}
            className="space-y-4"
          >
            <div className="space-y-2">
              <Label htmlFor="openingBalance">Opening Balance *</Label>
              <Input
                id="openingBalance"
                type="number"
                step="0.01"
                min="0"
                value={openingBalance}
                onChange={(e) => setOpeningBalance(e.target.value)}
                placeholder="Enter opening cash amount"
                className="text-right"
                autoFocus
                disabled={isOpening}
              />
              <p className="text-xs text-gray-500">
                Count all cash in the drawer and enter the total amount
              </p>
            </div>
          </form>

          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => {
                setShowOpenModal(false);
                setOpeningBalance("");
              }}
              disabled={isOpening}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              form="open-drawer-form"
              onClick={handleConfirmOpenDrawer}
              disabled={
                isOpening ||
                !openingBalance ||
                isNaN(parseFloat(openingBalance)) ||
                parseFloat(openingBalance) < 0
              }
            >
              {isOpening ? "Opening..." : "Open Drawer"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Re-authentication Dialog for Large Discrepancies */}
      <Dialog open={showReAuthDialog} onOpenChange={setShowReAuthDialog}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Shield className="h-5 w-5 text-red-600" />
              Security Re-authentication Required
            </DialogTitle>
            <DialogDescription>
              A large cash discrepancy has been detected. For security purposes, please enter your
              password to proceed with closing the drawer.
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            {/* Discrepancy Summary */}
            {drawerSession && actualCashAmount && (
              <Alert className="border-red-200 bg-red-50">
                <AlertTriangle className="h-4 w-4 text-red-600" />
                <AlertDescription>
                  <div className="space-y-1">
                    <div className="font-medium text-red-800">Large Discrepancy Detected</div>
                    <div className="text-sm text-red-700">
                      Expected: Rp{" "}
                      {(Number(drawerSession.openingBalance) + Number(cashSales)).toLocaleString(
                        "id-ID"
                      )}
                      <br />
                      Actual: Rp {parseFloat(actualCashAmount).toLocaleString("id-ID")}
                      <br />
                      Discrepancy: Rp{" "}
                      {Math.abs(
                        parseFloat(actualCashAmount) -
                          (Number(drawerSession.openingBalance) + Number(cashSales))
                      ).toLocaleString("id-ID")}
                    </div>
                  </div>
                </AlertDescription>
              </Alert>
            )}

            {/* Password Input */}
            <div className="space-y-2">
              <Label htmlFor="reAuthPassword">Your Password</Label>
              <Input
                id="reAuthPassword"
                type="password"
                value={reAuthPassword}
                onChange={(e) => setReAuthPassword(e.target.value)}
                placeholder="Enter your password to confirm"
                onKeyDown={(e) => {
                  if (e.key === "Enter" && reAuthPassword.trim()) {
                    handleReAuthentication();
                  }
                }}
                autoFocus
              />
              <p className="text-xs text-gray-500">
                This additional security step is required for discrepancies over Rp{" "}
                {LARGE_DISCREPANCY_THRESHOLD.toLocaleString("id-ID")}.
              </p>
            </div>
          </div>

          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => {
                setShowReAuthDialog(false);
                setReAuthPassword("");
              }}
              disabled={isReAuthenticating}
            >
              Cancel
            </Button>
            <Button
              onClick={handleReAuthentication}
              disabled={isReAuthenticating || !reAuthPassword.trim()}
              className="bg-red-600 hover:bg-red-700"
            >
              {isReAuthenticating ? (
                <>
                  <Lock className="h-4 w-4 mr-2 animate-spin" />
                  Authenticating...
                </>
              ) : (
                <>
                  <Shield className="h-4 w-4 mr-2" />
                  Confirm & Close Drawer
                </>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </Card>
  );
}
