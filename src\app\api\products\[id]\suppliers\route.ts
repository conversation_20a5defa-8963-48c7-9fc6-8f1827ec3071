import { NextRequest, NextResponse } from "next/server";
import { z } from "zod";
import { prisma } from "@/lib/prisma";
import { verifyAuthToken } from "@/lib/auth-utils";

// ProductSupplier schema for validation
const productSupplierSchema = z.object({
  supplierId: z.string().min(1, { message: "Supplier ID is required" }),
  supplierProductCode: z.string().optional(),
  supplierProductName: z.string().optional(),
  purchasePrice: z.number().positive({ message: "Purchase price must be positive" }),
  minimumOrderQuantity: z.number().positive().optional(),
  leadTimeDays: z.number().int().min(0).optional(),
  isPreferred: z.boolean().default(false),
  isActive: z.boolean().default(true),
  notes: z.string().optional(),
});

// GET /api/products/[id]/suppliers - Get all suppliers for a product
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication
    const auth = await verifyAuthToken(request);

    if (!auth.authenticated) {
      return NextResponse.json(
        { error: auth.error },
        { status: auth.status }
      );
    }

    const productId = params.id;

    // Verify product exists
    const product = await prisma.product.findUnique({
      where: { id: productId },
      select: { id: true, name: true, sku: true }
    });

    if (!product) {
      return NextResponse.json(
        { error: "Product not found" },
        { status: 404 }
      );
    }

    // Get query parameters for filtering
    const url = new URL(request.url);
    const activeOnly = url.searchParams.get("active") === "true";
    const preferredOnly = url.searchParams.get("preferred") === "true";

    // Build filter conditions
    const where: any = { productId };
    
    if (activeOnly) {
      where.isActive = true;
    }
    
    if (preferredOnly) {
      where.isPreferred = true;
    }

    // Get product suppliers
    const productSuppliers = await prisma.productSupplier.findMany({
      where,
      include: {
        supplier: {
          select: {
            id: true,
            name: true,
            contactPerson: true,
            phone: true,
            email: true,
            address: true,
          }
        }
      },
      orderBy: [
        { isPreferred: 'desc' }, // Preferred suppliers first
        { purchasePrice: 'asc' }, // Then by price (lowest first)
        { createdAt: 'asc' }, // Then by creation date
      ],
    });

    return NextResponse.json({
      product,
      suppliers: productSuppliers,
      count: productSuppliers.length
    });
  } catch (error) {
    console.error("Error fetching product suppliers:", error);
    return NextResponse.json(
      { error: "Failed to fetch product suppliers", message: (error as Error).message },
      { status: 500 }
    );
  }
}

// POST /api/products/[id]/suppliers - Add a supplier to a product
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication
    const auth = await verifyAuthToken(request);

    if (!auth.authenticated) {
      return NextResponse.json(
        { error: auth.error },
        { status: auth.status }
      );
    }

    // Check permissions
    const hasPermission = ["SUPER_ADMIN", "WAREHOUSE_ADMIN"].includes(auth.user.role);
    if (!hasPermission) {
      return NextResponse.json(
        { error: "Unauthorized - Insufficient permissions" },
        { status: 403 }
      );
    }

    const productId = params.id;

    // Verify product exists
    const product = await prisma.product.findUnique({
      where: { id: productId },
      select: { id: true, name: true }
    });

    if (!product) {
      return NextResponse.json(
        { error: "Product not found" },
        { status: 404 }
      );
    }

    // Get request body
    const body = await request.json();

    // Validate data
    const validationResult = productSupplierSchema.safeParse(body);
    if (!validationResult.success) {
      return NextResponse.json(
        { error: "Validation failed", issues: validationResult.error.issues },
        { status: 400 }
      );
    }

    const data = validationResult.data;

    // Verify supplier exists
    const supplier = await prisma.supplier.findUnique({
      where: { id: data.supplierId },
      select: { id: true, name: true }
    });

    if (!supplier) {
      return NextResponse.json(
        { error: "Supplier not found" },
        { status: 404 }
      );
    }

    // Check if relationship already exists
    const existingRelationship = await prisma.productSupplier.findUnique({
      where: {
        productId_supplierId: {
          productId,
          supplierId: data.supplierId
        }
      }
    });

    if (existingRelationship) {
      return NextResponse.json(
        { error: "Product-supplier relationship already exists" },
        { status: 409 }
      );
    }

    // If this is set as preferred, unset other preferred suppliers for this product
    if (data.isPreferred) {
      await prisma.productSupplier.updateMany({
        where: {
          productId,
          isPreferred: true
        },
        data: {
          isPreferred: false
        }
      });
    }

    // Create the product-supplier relationship
    const productSupplier = await prisma.productSupplier.create({
      data: {
        productId,
        supplierId: data.supplierId,
        supplierProductCode: data.supplierProductCode,
        supplierProductName: data.supplierProductName,
        purchasePrice: data.purchasePrice,
        minimumOrderQuantity: data.minimumOrderQuantity,
        leadTimeDays: data.leadTimeDays,
        isPreferred: data.isPreferred,
        isActive: data.isActive,
        notes: data.notes,
      },
      include: {
        supplier: {
          select: {
            id: true,
            name: true,
            contactPerson: true,
            phone: true,
            email: true,
          }
        }
      }
    });

    // Log activity
    await prisma.activityLog.create({
      data: {
        userId: auth.user.id,
        action: "ADD_PRODUCT_SUPPLIER",
        details: `Added supplier ${supplier.name} to product ${product.name}`,
      },
    });

    return NextResponse.json({
      message: "Supplier added to product successfully",
      productSupplier
    }, { status: 201 });
  } catch (error) {
    console.error("Error adding supplier to product:", error);
    return NextResponse.json(
      { error: "Failed to add supplier to product", message: (error as Error).message },
      { status: 500 }
    );
  }
}
