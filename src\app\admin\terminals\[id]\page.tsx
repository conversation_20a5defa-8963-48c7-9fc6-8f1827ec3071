"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { toast } from "sonner";
import { MainLayout } from "@/components/layout/MainLayout";
import { PageHeader } from "@/components/layout/PageHeader";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Loader2, ArrowLeft, Edit, Link as LinkIcon, Link2Off } from "lucide-react";
import { formatDate, formatCurrency } from "@/lib/utils";
import Link from "next/link";
import { useAuth } from "@/hooks/use-auth";
import React from "react";

interface Terminal {
  id: string;
  name: string;
  ipAddress?: string;
  macAddress?: string;
  location?: string;
  description?: string;
  isActive: boolean;
  drawerId?: string;
  createdAt: string;
  updatedAt: string;
  drawer?: {
    id: string;
    name: string;
    location?: string;
  };
  drawerSessions: DrawerSession[];
}

interface DrawerSession {
  id: string;
  businessDate: string;
  openingBalance: number;
  expectedClosingBalance?: number;
  actualClosingBalance?: number;
  discrepancy?: number;
  openedAt: string;
  closedAt?: string;
  status: "OPEN" | "CLOSED" | "RECONCILED";
  user: {
    id: string;
    name: string;
  };
}

export default function TerminalDetailPage({ params }: { params: Promise<{ id: string }> }) {
  const { id } = React.use(params);
  const router = useRouter();
  const { user, isLoading: authLoading } = useAuth();
  const [terminal, setTerminal] = useState<Terminal | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // Fetch terminal details
  const fetchTerminal = async () => {
    setIsLoading(true);

    try {
      const response = await fetch(`/api/terminals/${id}`);
      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || "Failed to fetch terminal");
      }

      setTerminal(data.terminal);
    } catch (err) {
      toast.error(err instanceof Error ? err.message : "An unknown error occurred");
      console.error("Error fetching terminal:", err);
    } finally {
      setIsLoading(false);
    }
  };

  // Load terminal on mount
  useEffect(() => {
    fetchTerminal();
  }, [id]);

  // Check if user has permission to manage terminals
  const canManageTerminals = user && ["SUPER_ADMIN", "FINANCE_ADMIN"].includes(user.role);

  return (
    <MainLayout>
      <PageHeader
        heading={terminal ? terminal.name : "Terminal Details"}
        subheading={
          terminal ? `View details for ${terminal.name}` : "Loading terminal information..."
        }
        actions={
          <div className="flex space-x-2">
            <Button variant="outline" onClick={() => router.back()}>
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back
            </Button>
            {canManageTerminals && terminal && (
              <Button onClick={() => router.push(`/admin/terminals?edit=${terminal.id}`)}>
                <Edit className="h-4 w-4 mr-2" />
                Edit Terminal
              </Button>
            )}
          </div>
        }
      />

      {isLoading ? (
        <div className="flex justify-center items-center py-8">
          <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
        </div>
      ) : !terminal ? (
        <div className="text-center py-8 text-muted-foreground">Terminal not found</div>
      ) : (
        <div className="grid gap-6 md:grid-cols-2">
          <Card>
            <CardHeader>
              <CardTitle>Terminal Information</CardTitle>
              <CardDescription>Details about this terminal</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <div className="text-sm font-medium text-muted-foreground">Name</div>
                    <div>{terminal.name}</div>
                  </div>
                  <div>
                    <div className="text-sm font-medium text-muted-foreground">Status</div>
                    <div>
                      <Badge variant={terminal.isActive ? "success" : "destructive"}>
                        {terminal.isActive ? "Active" : "Inactive"}
                      </Badge>
                    </div>
                  </div>
                </div>

                <div>
                  <div className="text-sm font-medium text-muted-foreground">IP Address</div>
                  <div>{terminal.ipAddress || "Not specified"}</div>
                </div>

                <div>
                  <div className="text-sm font-medium text-muted-foreground">MAC Address</div>
                  <div>{terminal.macAddress || "Not specified"}</div>
                </div>

                <div>
                  <div className="text-sm font-medium text-muted-foreground">Location</div>
                  <div>{terminal.location || "Not specified"}</div>
                </div>

                <div>
                  <div className="text-sm font-medium text-muted-foreground">Description</div>
                  <div>{terminal.description || "No description"}</div>
                </div>

                <div>
                  <div className="text-sm font-medium text-muted-foreground">Created At</div>
                  <div>{formatDate(new Date(terminal.createdAt))}</div>
                </div>

                <div>
                  <div className="text-sm font-medium text-muted-foreground">Last Updated</div>
                  <div>{formatDate(new Date(terminal.updatedAt))}</div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Assigned Drawer</CardTitle>
              <CardDescription>Cash drawer assigned to this terminal</CardDescription>
            </CardHeader>
            <CardContent>
              {terminal.drawer ? (
                <div className="space-y-4">
                  <div className="flex items-center">
                    <LinkIcon className="h-5 w-5 mr-2 text-blue-500" />
                    <span className="text-lg font-medium">{terminal.drawer.name}</span>
                  </div>

                  {terminal.drawer.location && (
                    <div>
                      <div className="text-sm font-medium text-muted-foreground">Location</div>
                      <div>{terminal.drawer.location}</div>
                    </div>
                  )}

                  <div className="pt-2">
                    <Button
                      variant="outline"
                      onClick={() => router.push(`/admin/cash-drawers/${terminal.drawer?.id}`)}
                    >
                      View Drawer Details
                    </Button>
                  </div>
                </div>
              ) : (
                <div className="flex flex-col items-center justify-center py-8 text-center">
                  <Link2Off className="h-12 w-12 text-muted-foreground mb-4" />
                  <div className="text-muted-foreground">No drawer assigned to this terminal</div>
                  {canManageTerminals && (
                    <Button
                      variant="outline"
                      className="mt-4"
                      onClick={() => router.push(`/admin/terminals?assign=${terminal.id}`)}
                    >
                      Assign a Drawer
                    </Button>
                  )}
                </div>
              )}
            </CardContent>
          </Card>

          <Card className="md:col-span-2">
            <CardHeader>
              <CardTitle>Drawer Sessions</CardTitle>
              <CardDescription>Recent drawer sessions on this terminal</CardDescription>
            </CardHeader>
            <CardContent>
              {terminal.drawerSessions.length === 0 ? (
                <div className="text-center py-8 text-muted-foreground">
                  No drawer sessions found for this terminal
                </div>
              ) : (
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Business Date</TableHead>
                      <TableHead>Opened By</TableHead>
                      <TableHead>Opened At</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Opening Balance</TableHead>
                      <TableHead>Closing Balance</TableHead>
                      <TableHead>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {terminal.drawerSessions.map((session) => (
                      <TableRow key={session.id}>
                        <TableCell>{formatDate(new Date(session.businessDate))}</TableCell>
                        <TableCell>{session.user.name}</TableCell>
                        <TableCell>{formatDate(new Date(session.openedAt))}</TableCell>
                        <TableCell>
                          <Badge
                            variant={
                              session.status === "OPEN"
                                ? "default"
                                : session.status === "CLOSED"
                                  ? "secondary"
                                  : "success"
                            }
                          >
                            {session.status}
                          </Badge>
                        </TableCell>
                        <TableCell>{formatCurrency(session.openingBalance)}</TableCell>
                        <TableCell>
                          {session.actualClosingBalance
                            ? formatCurrency(session.actualClosingBalance)
                            : "-"}
                        </TableCell>
                        <TableCell>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => router.push(`/admin/drawer-sessions/${session.id}`)}
                          >
                            View
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              )}
            </CardContent>
          </Card>
        </div>
      )}
    </MainLayout>
  );
}
