# Git Workflow Guide

## 🌳 Branching Strategy

We follow a phase-based branching strategy aligned with our development phases:

### Main Branches

- `main` - Production-ready code
- `develop` - Integration branch for feature development

### Feature Branches

Create feature branches from `develop` using the following naming convention:

```
feature/phase-<number>/<feature-name>
Example: feature/phase-1/auth-system
```

### Other Branches

- `hotfix/*` - For urgent production fixes
- `release/*` - For release preparation

## 🔄 Development Workflow

1. **Start New Feature**

   ```bash
   git checkout develop
   git pull origin develop
   git checkout -b feature/phase-1/feature-name
   ```

2. **Regular Development**

   ```bash
   git add .
   git commit -m "feat: descriptive message"
   git push origin feature/phase-1/feature-name
   ```

3. **Prepare for Review**
   ```bash
   git fetch origin develop
   git rebase origin/develop
   git push origin feature/phase-1/feature-name
   ```

## 📝 Commit Message Convention

Follow semantic commit messages:

- `feat:` - New feature
- `fix:` - Bug fix (always confirm first that the bug is actually fixed before commiting)
- `docs:` - Documentation changes
- `style:` - Code style changes (formatting, etc)
- `refactor:` - Code refactoring
- `test:` - Adding or modifying tests
- `chore:` - Maintenance tasks

Example:

```
feat: implement user authentication system
```

## 🔒 Branch Protection Rules

### Main Branch (`main`)

- Require pull request reviews
- Require status checks to pass
- No direct pushes

### Develop Branch (`develop`)

- Require pull request reviews
- Require status checks to pass
- No direct pushes except for project maintainers

## 🔍 Code Review Guidelines

1. Review for:

   - Code quality and standards
   - Test coverage
   - Documentation updates
   - Security considerations

2. Use constructive feedback
3. Address all comments before merging

## 📦 Release Process

1. Create release branch from develop

   ```bash
   git checkout -b release/v1.x.x develop
   ```

2. Version bump and final testing

3. Merge to main and develop

   ```bash
   git checkout main
   git merge release/v1.x.x
   git tag -a v1.x.x -m "Release v1.x.x"
   git checkout develop
   git merge release/v1.x.x
   ```

4. Delete release branch
   ```bash
   git branch -d release/v1.x.x
   ```
