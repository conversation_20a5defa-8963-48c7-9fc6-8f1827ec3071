📄 Technical Specifications for POS and Inventory Management Application

🔧 Tech Stack

Frontend:

Framework: Next.js (React)

Styling: Tailwind CSS

UI Kit (optional): shadcn/ui or custom minimalist

Icons: Lucide, Heroicons

PWA Support: next-pwa

Backend:

Runtime: Node.js (via Next.js API Routes)

Database: PostgreSQL (alternative: SQLite for light offline mode)

ORM: Prisma

Auth: NextAuth / custom simple auth

File storage: Local filesystem (not cloud)

DevOps:

Deployment: Local server (LAN), via Node.js server or Docker

Backup: Daily automatic backup after the store closes

Log audit: Built-in activity logger

Export/Import: CSV/Excel

Desktop Packaging:

PWA mode: Installable on desktop & mobile

Native desktop app: Electron (.exe) wrapper for Windows

🖥️ Devices & Infrastructure

Devices used:

PC/laptop (priority)

Tablet

Browser on mobile (mobile responsive)

Architecture:

Central server (1 unit PC/local server)

Clients access via browser through local URL (e.g., http://192.168.0.X:3000)

All devices access a single centralized database over the LAN network

👥 User Roles

Super Admin / Owner:
Full access except for changing POS transactions and stock
Access to settings (users, pricing, discounts, units, etc.)

Cashier:
Focused on POS
Payment input: cash, debit, QRIS

Finance Admin:
Financial reports
PO & payment approvals

Warehouse Admin (also Purchasing):
Warehouse and store stock management
Create POs

Marketing/Delivery:
Limited to delivery or status tracking

📦 Inventory Features

Two types of stock: store stock & warehouse stock (optional warehouse)

Notifications when store stock is below the minimum threshold (push to warehouse)

Flexible unit system (pcs, kg, liters, etc. – can be manually added)

Flexible pricing system: base price, friends price, family price

Discount system (fixed value / percentage)

Due date system (customer debt)

Return module (from customer and to supplier)

Product exchange module

Cash audit and cash surplus/shortage recording

📈 Reports & Exports

Report types: Daily, Weekly, Monthly, Custom Range (via date picker)

Export formats: CSV / Excel

No direct integration with other systems (for now)

🔐 Authentication & Security

Simple login system per role

User activities recorded (audit trail)

No multilingual support (default: Indonesian)

Default timezone: GMT+7

📁 Backup & Recovery

Backup schedule: Daily after store closure

Mechanism: Automatic, stored in local folder

Backup success/failure notifications

🌐 Deployment

Local: LAN-only, not dependent on the internet

Accessible from multiple devices within the same network

Not hosted on the internet

Can be expanded to multi-store in the future

🧩 Modularization & UI

Automatic modularization based on role

Minimalist, bright, monochrome UI (reference: NextJS, shadcn)

Mobile responsive

User-friendly navigation, suitable for non-technical users

📦 Desktop App Development

PWA mode: Installable without browser chrome UI

Electron: To wrap the web app into an .exe file, installation like a native app
