import { NextRequest, NextResponse } from "next/server";

import { verifyAuthToken } from "@/lib/auth-utils";

// POST /api/auth/logout - Logout endpoint
export async function POST(request: NextRequest) {


  try {
    // Try to get user info from token before clearing it
    let userId: string | undefined;
    try {
      const authResult = await verifyAuthToken(request);
      if (authResult.authenticated && authResult.user) {
        userId = authResult.user.id;
      }
    } catch (error) {
      // Token might be invalid, but we still want to clear it
      console.log("Could not verify token during logout:", error);
    }

    // Create response and clear the session token
    const response = NextResponse.json({ success: true });

    // Clear the session token
    response.cookies.delete("session-token");

    // Log successful logout (basic logging)
    console.log("User logged out successfully, userId:", userId);

    console.log("Logout successful, cookie cleared");
    return response;
  } catch (error) {
    console.error("Logout error:", error);

    // Log logout error (basic logging)
    console.error("Logout system error:", error);

    return NextResponse.json(
      { error: "An error occurred during logout" },
      { status: 500 }
    );
  }
}
