import { NextRequest, NextResponse } from 'next/server';
import fs from 'fs';
import path from 'path';
import { addBackupHistoryEntry } from '@/lib/backup/backup-history';
import { generateBackupFilename, ensureBackupDir, getCurrentSchemaVersion } from '@/lib/backup/db-backup';
import { jwtVerify } from "jose";
import { prisma } from '@/auth';

// Default backup directory
const DEFAULT_BACKUP_DIR = path.join(process.cwd(), 'backups');

// Helper function to format file size
function formatFileSize(bytes: number): string {
  if (bytes < 1024) return bytes + ' bytes';
  else if (bytes < 1024 * 1024) return (bytes / 1024).toFixed(2) + ' KB';
  else if (bytes < 1024 * 1024 * 1024) return (bytes / (1024 * 1024)).toFixed(2) + ' MB';
  else return (bytes / (1024 * 1024 * 1024)).toFixed(2) + ' GB';
}

// POST /api/backup/upload - Upload a backup file
export async function POST(request: NextRequest) {
  try {
    // Get the session token from cookies
    const token = request.cookies.get("session-token");

    // Check if token exists
    if (!token) {
      return NextResponse.json(
        { error: "Unauthorized. You must be logged in to upload backup files." },
        { status: 401 }
      );
    }

    // Verify the token
    let payload;
    try {
      const result = await jwtVerify(
        token.value,
        new TextEncoder().encode(process.env.NEXTAUTH_SECRET || "fallback-secret")
      );
      payload = result.payload;
    } catch (error) {
      console.error("Token verification failed:", error);
      return NextResponse.json(
        { error: "Unauthorized. Invalid authentication token." },
        { status: 401 }
      );
    }

    // Only allow SUPER_ADMIN and FINANCE_ADMIN to access backup functionality
    if (!payload.role || !["SUPER_ADMIN", "FINANCE_ADMIN"].includes(payload.role as string)) {
      return NextResponse.json(
        { error: "Unauthorized. You don't have permission to upload backup files." },
        { status: 403 }
      );
    }

    // Ensure the backup directory exists
    ensureBackupDir();

    // Parse the form data
    const formData = await request.formData();
    const file = formData.get('file') as File;
    const comment = formData.get('comment') as string || 'Uploaded backup';

    if (!file) {
      return NextResponse.json(
        { error: 'No file provided' },
        { status: 400 }
      );
    }

    // Validate file type
    if (!file.name.endsWith('.sql')) {
      return NextResponse.json(
        { error: 'Only SQL files are allowed' },
        { status: 400 }
      );
    }

    // Generate a new filename to prevent overwriting existing files
    // Use a prefix to indicate this is an uploaded file
    const filename = generateBackupFilename('uploaded-backup');
    const filePath = path.join(DEFAULT_BACKUP_DIR, filename);

    // Convert the file to a buffer
    const buffer = Buffer.from(await file.arrayBuffer());

    // Write the file to disk
    fs.writeFileSync(filePath, buffer);

    // Get file stats
    const stats = fs.statSync(filePath);

    // Create metadata file with additional information
    const metadataPath = `${filePath}.json`;
    const metadata = {
      filename,
      originalFilename: file.name,
      timestamp: new Date(),
      size: stats.size,
      comment: comment,
      method: 'upload',
      isExternalUpload: true,
      format: file.name.endsWith('.sql') ? 'sql' : 'unknown',
      schemaVersion: 'unknown', // For uploaded files, we don't know the schema version
      currentSchemaVersion: getCurrentSchemaVersion(), // Store the current schema version for comparison
    };

    fs.writeFileSync(metadataPath, JSON.stringify(metadata, null, 2));

    // Log the upload operation
    addBackupHistoryEntry({
      type: 'backup',
      timestamp: new Date().toISOString(),
      filePath,
      fileName: filename,
      method: 'upload',
      success: true,
      message: `Backup file uploaded: ${file.name} (saved as ${filename})`,
      size: stats.size,
      comment,
    });

    // Log the backup upload in activity logs
    await prisma.activityLog.create({
      data: {
        userId: payload.id as string,
        action: "UPLOAD_BACKUP",
        details: `Uploaded backup file: ${file.name} (${formatFileSize(stats.size)})${comment ? ` - Comment: ${comment}` : ''}`,
      },
    });

    return NextResponse.json({
      success: true,
      message: 'Backup file uploaded successfully',
      backup: {
        filename,
        path: filePath,
        originalFilename: file.name,
        timestamp: new Date().toISOString(),
        size: stats.size,
        comment,
      },
    });
  } catch (error: any) {
    console.error('Error uploading backup:', error);

    // Log the failed upload attempt
    addBackupHistoryEntry({
      type: 'backup',
      timestamp: new Date().toISOString(),
      filePath: '',
      fileName: 'upload-failed',
      method: 'upload',
      success: false,
      message: `Upload failed: ${error?.message || String(error)}`,
    });

    return NextResponse.json(
      { error: 'Failed to upload backup', message: error?.message || String(error) },
      { status: 500 }
    );
  }
}
