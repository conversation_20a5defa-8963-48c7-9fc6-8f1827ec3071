"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  Card<PERSON>ooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { CheckCircle, AlertCircle, Loader2 } from "lucide-react";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";

export default function SetupPage() {
  const [loading, setLoading] = useState(false);
  const [email, setEmail] = useState("<EMAIL>");
  const [password, setPassword] = useState("developer123");
  const [name, setName] = useState("Developer");
  const [result, setResult] = useState<{
    success: boolean;
    message: string;
    developer?: any;
    error?: string;
  } | null>(null);

  const createAdminAccount = async () => {
    setLoading(true);
    try {
      // First, try to update the schema to add the DEVELOPER role for future use
      try {
        await fetch("/api/setup/update-schema");
      } catch (schemaError) {
        // Continue anyway, as we're creating a SUPER_ADMIN account
        console.log("Schema update attempted");
      }

      // Create an admin account using the setup API
      const response = await fetch("/api/setup/create-admin", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          name,
          email,
          password,
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || "Failed to create account");
      }

      setResult({
        success: true,
        message: data.message || "Admin account created successfully",
        developer: {
          id: data.user.id,
          name: data.user.name,
          email: data.user.email,
          role: data.user.role,
        },
      });
    } catch (error) {
      console.error("Error creating account:", error);
      setResult({
        success: false,
        message: "Failed to create admin account",
        error: error.message || "Unknown error occurred",
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="container mx-auto py-10">
      <div className="max-w-md mx-auto">
        <Card>
          <CardHeader>
            <CardTitle>System Setup</CardTitle>
            <CardDescription>
              Initialize the system with required accounts and settings
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <h3 className="text-lg font-medium">Admin Account</h3>
              <p className="text-sm text-muted-foreground">
                Create an administrator account to manage the system
              </p>

              <div className="space-y-4 mt-4">
                <div className="space-y-2">
                  <Label htmlFor="name">Name</Label>
                  <Input
                    id="name"
                    value={name}
                    onChange={(e) => setName(e.target.value)}
                    placeholder="Developer"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="email">Email</Label>
                  <Input
                    id="email"
                    type="email"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    placeholder="<EMAIL>"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="password">Password</Label>
                  <Input
                    id="password"
                    type="password"
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    placeholder="••••••••"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="role">Role</Label>
                  <div className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm">
                    Super Admin
                  </div>
                  <p className="text-xs text-muted-foreground mt-1">
                    This account will be created with Super Admin privileges, giving you full access
                    to all system features.
                  </p>
                </div>
              </div>

              {result && (
                <Alert variant={result.success ? "success" : "destructive"} className="mt-4">
                  {result.success ? (
                    <CheckCircle className="h-4 w-4" />
                  ) : (
                    <AlertCircle className="h-4 w-4" />
                  )}
                  <AlertTitle>{result.success ? "Success" : "Error"}</AlertTitle>
                  <AlertDescription>
                    {result.message}
                    {result.success && result.developer && (
                      <div className="mt-2 text-xs">
                        <p>
                          <strong>Email:</strong> {result.developer.email}
                        </p>
                        <p>
                          <strong>Password:</strong> {password}
                        </p>
                        <p>
                          <strong>Role:</strong> {result.developer.role}
                        </p>
                      </div>
                    )}
                    {!result.success && result.error && (
                      <div className="mt-2">
                        <details>
                          <summary className="cursor-pointer text-xs">Show error details</summary>
                          <pre className="mt-2 whitespace-pre-wrap text-xs">{result.error}</pre>
                        </details>
                      </div>
                    )}
                  </AlertDescription>
                </Alert>
              )}
            </div>
          </CardContent>
          <CardFooter>
            <Button
              onClick={createAdminAccount}
              disabled={loading}
              className="w-full bg-black text-white hover:bg-gray-800"
            >
              {loading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Creating Account...
                </>
              ) : (
                "Create Admin Account"
              )}
            </Button>
          </CardFooter>
        </Card>
      </div>
    </div>
  );
}
