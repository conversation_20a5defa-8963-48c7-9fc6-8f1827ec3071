import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/auth";
import { jwtVerify } from "jose";

// Helper function to verify the authentication token
async function verifyAuthToken(request: NextRequest) {
  // Get the session token from cookies
  const token = request.cookies.get("session-token");

  // Check if token exists
  if (!token) {
    return {
      authenticated: false,
      error: "Unauthorized. You must be logged in to flush conversations.",
      status: 401
    };
  }

  // Verify the token
  try {
    const { payload } = await jwtVerify(
      token.value,
      new TextEncoder().encode(process.env.NEXTAUTH_SECRET || "fallback-secret")
    );

    return {
      authenticated: true,
      user: {
        id: payload.id as string,
        name: payload.name as string,
        email: payload.email as string,
        role: payload.role as string,
      }
    };
  } catch (error) {
    console.error("Token verification failed:", error);
    return {
      authenticated: false,
      error: "Unauthorized. Invalid authentication token.",
      status: 401
    };
  }
}

// POST /api/conversations/flush - Flush old conversations
export async function POST(request: NextRequest) {
  try {
    // Check authentication - only admins can manually trigger this
    const auth = await verifyAuthToken(request);

    if (!auth.authenticated) {
      return NextResponse.json(
        { error: auth.error },
        { status: auth.status }
      );
    }

    // Only allow admins and developers to manually trigger this
    if (auth.user.role !== "SUPER_ADMIN" && auth.user.role !== "DEVELOPER") {
      return NextResponse.json(
        { error: "Only administrators can perform this action" },
        { status: 403 }
      );
    }

    // Get request body for configuration
    const body = await request.json();

    // Find conversations to delete:
    // All conversations that are not starred by any user
    const conversationsToDelete = await prisma.conversation.findMany({
      where: {
        starredBy: {
          none: {},
        },
      },
      include: {
        messages: {
          select: {
            id: true,
          },
        },
        participants: {
          select: {
            id: true,
          },
        },
      },
    });

    // Count messages and participants to be deleted
    const messageCount = conversationsToDelete.reduce(
      (count, conv) => count + conv.messages.length,
      0
    );
    const participantCount = conversationsToDelete.reduce(
      (count, conv) => count + conv.participants.length,
      0
    );

    // Delete the conversations (cascade will delete messages and participants)
    const { count } = await prisma.conversation.deleteMany({
      where: {
        id: {
          in: conversationsToDelete.map((c) => c.id),
        },
      },
    });

    // Create activity log
    await prisma.activityLog.create({
      data: {
        userId: auth.user.id,
        action: "FLUSH_CONVERSATIONS",
        details: `Flushed ${count} conversations, ${messageCount} messages, and ${participantCount} participants (all unstarred conversations)`,
      },
    });

    return NextResponse.json({
      success: true,
      flushed: {
        conversations: count,
        messages: messageCount,
        participants: participantCount,
      },
    });
  } catch (error) {
    console.error("Error flushing conversations:", error);
    return NextResponse.json(
      { error: "Failed to flush conversations", message: error.message },
      { status: 500 }
    );
  }
}
