const { PrismaClient } = require('./src/generated/prisma');
const bcrypt = require('bcryptjs');

async function hashPassword(password) {
  return await bcrypt.hash(password, 10);
}

async function main() {
  const prisma = new PrismaClient();
  
  try {
    console.log("Creating new admin user...");

    // Create admin user with a different email
    const hashedPassword = await hashPassword("admin123");

    const admin = await prisma.user.create({
      data: {
        name: "Admin User 2",
        email: "<EMAIL>",
        password: hashedPassword,
        role: "SUPER_ADMIN",
        active: true,
      },
    });

    console.log(`Created admin user: ${admin.email}`);

    // Create activity log
    await prisma.activityLog.create({
      data: {
        userId: admin.id,
        action: "SYSTEM",
        details: "Additional admin user created during troubleshooting",
      },
    });

    console.log("Admin user created successfully!");
    
    // List all users
    const users = await prisma.user.findMany();
    console.log("All users in database:");
    users.forEach(user => {
      console.log(`- ${user.email} (${user.role}, active: ${user.active})`);
    });
    
  } catch (error) {
    console.error("Error creating admin user:", error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

main();
