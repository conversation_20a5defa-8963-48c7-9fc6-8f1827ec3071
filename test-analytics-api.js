// Simple test script to check analytics API
import fetch from 'node-fetch';

async function testAnalyticsAPI() {
  try {
    console.log('Testing analytics API...');

    // Test the sales-trends endpoint
    const response = await fetch('http://localhost:3000/api/analytics/sales-trends?dateRange=30d', {
      headers: {
        'x-user-id': 'cmb22whmb0000ul3o1imdwvoe',
        'x-user-role': 'SUPER_ADMIN',
        'x-user-email': '<EMAIL>'
      }
    });

    console.log('Response status:', response.status);
    console.log('Response headers:', Object.fromEntries(response.headers.entries()));

    const data = await response.text();
    console.log('Response body:', data);

  } catch (error) {
    console.error('Error testing API:', error);
  }
}

testAnalyticsAPI();
