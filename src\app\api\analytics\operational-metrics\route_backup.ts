import { NextRequest, NextResponse } from "next/server";
import { PrismaClient } from "@/generated/prisma";
import { 
  OperationalMetricsData,
  CashOperationMetrics,
  SystemPerformanceMetrics,
  InventoryOperationMetrics,
  AuditComplianceMetrics,
  PerformanceBenchmarks
} from "@/lib/types/analytics";

// Create a singleton instance of PrismaClient to prevent too many connections
const globalForPrisma = globalThis as unknown as { prisma: PrismaClient };
const prisma = globalForPrisma.prisma || new PrismaClient();
if (process.env.NODE_ENV !== "production") globalForPrisma.prisma = prisma;

export async function GET(request: NextRequest) {
  try {
    console.log('[Operational Metrics API] Starting data fetch...');

    const { searchParams } = new URL(request.url);
    const from = searchParams.get("from");
    const to = searchParams.get("to");

    // Set default date range (last 30 days)
    const endDate = to ? new Date(to) : new Date();
    const startDate = from ? new Date(from) : new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);

    console.log('[Operational Metrics API] Date range:', { startDate, endDate });

    // Fetch all required data in parallel
    const [
      drawerSessions,
      cashReconciliations,
      cashAuditAlerts,
      activityLogs,
      transactions,
      stockAdjustments,
      users
    ] = await Promise.all([
      // Drawer Sessions
      prisma.drawerSession.findMany({
        where: {
          openedAt: { gte: startDate, lte: endDate }
        },
        select: {
          id: true,
          openedAt: true,
          closedAt: true,
          openingBalance: true,
          actualClosingBalance: true,
          discrepancy: true,
          status: true
        }
      }),

      // Cash Reconciliations
      prisma.cashReconciliation.findMany({
        where: {
          businessDate: { gte: startDate, lte: endDate }
        },
        select: {
          id: true,
          businessDate: true,
          discrepancy: true,
          status: true,
          resolutionStatus: true,
          resolvedAt: true,
          createdAt: true,
          openingBalance: true,
          expectedAmount: true,
          actualAmount: true
        }
      }),

      // Cash Audit Alerts
      prisma.cashAuditAlert.findMany({
        where: {
          createdAt: { gte: startDate, lte: endDate }
        },
        select: {
          id: true,
          alertType: true,
          severity: true,
          isResolved: true,
          resolvedAt: true,
          createdAt: true,
          threshold: true,
          actualValue: true
        }
      }),

      // Activity Logs
      prisma.activityLog.findMany({
        where: {
          timestamp: { gte: startDate, lte: endDate }
        },
        select: {
          id: true,
          userId: true,
          action: true,
          timestamp: true
        }
      }),

      // Transactions
      prisma.transaction.findMany({
        where: {
          createdAt: { gte: startDate, lte: endDate }
        },
        select: {
          id: true,
          total: true,
          createdAt: true,
          status: true,
          paymentStatus: true,
          cashierId: true
        }
      }),

      // Stock Adjustments
      prisma.stockAdjustment.findMany({
        where: {
          createdAt: { gte: startDate, lte: endDate }
        },
        select: {
          id: true,
          reason: true,
          adjustmentQuantity: true,
          createdAt: true,
          userId: true
        }
      }),

      // Active Users
      prisma.user.findMany({
        where: { active: true },
        select: {
          id: true,
          role: true,
          createdAt: true
        }
      })
    ]);

    console.log('[Operational Metrics API] Data fetched, processing metrics...');

    // Calculate Cash Operations Metrics
    const cashOperations = calculateCashOperationMetrics(
      drawerSessions,
      cashReconciliations,
      startDate,
      endDate
    );

    // Calculate System Performance Metrics
    const systemPerformance = calculateSystemPerformanceMetrics(
      activityLogs,
      transactions,
      users,
      startDate,
      endDate
    );

    // Calculate Inventory Operations Metrics
    const inventoryOps = calculateInventoryOperationMetrics(
      stockAdjustments,
      startDate,
      endDate
    );

    // Calculate Audit & Compliance Metrics
    const auditCompliance = calculateAuditComplianceMetrics(
      cashAuditAlerts,
      cashReconciliations,
      startDate,
      endDate
    );

    // Calculate Performance Benchmarks
    const performanceBenchmarks = await calculatePerformanceBenchmarks(
      transactions,
      drawerSessions,
      users,
      startDate,
      endDate
    );

    // Calculate summary metrics
    const summary = {
      totalMetrics: 25, // Total number of metrics tracked
      alertsRequiringAttention: 
        cashOperations.reconciliationPerformance.pendingCount +
        auditCompliance.securityAlerts.highSeverityAlerts +
        inventoryOps.lowStockAlerts.criticalAlerts,
      overallHealthScore: Math.round(
        (performanceBenchmarks.dailyOperationalScore.overallScore +
         (auditCompliance.complianceScores.reconciliationAccuracy || 0) +
         (systemPerformance.transactionProcessing.processingEfficiency || 0)) / 3
      ),
      lastUpdated: new Date().toISOString()
    };

    const operationalMetrics: OperationalMetricsData = {
      cashOperations,
      systemPerformance,
      inventoryOps,
      auditCompliance,
      performanceBenchmarks,
      summary
    };

    console.log('[Operational Metrics API] Metrics calculated successfully');

    return NextResponse.json({
      success: true,
      data: operationalMetrics,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('[Operational Metrics API] Error:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to fetch operational metrics',
        timestamp: new Date().toISOString()
      },
      { status: 500 }
    );
  }
}

// Helper function to calculate cash operation metrics
function calculateCashOperationMetrics(
  drawerSessions: any[],
  cashReconciliations: any[],
  startDate: Date,
  endDate: Date
): CashOperationMetrics {
  const daysDiff = Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));
  
  // Drawer Sessions Metrics
  const closedSessions = drawerSessions.filter(s => s.closedAt);
  const totalSessionDuration = closedSessions.reduce((sum, session) => {
    if (session.closedAt && session.openedAt) {
      return sum + (new Date(session.closedAt).getTime() - new Date(session.openedAt).getTime());
    }
    return sum;
  }, 0);
  
  const averageSessionDuration = closedSessions.length > 0 
    ? Math.round(totalSessionDuration / (closedSessions.length * 60 * 1000)) // in minutes
    : 0;

  // Cash Discrepancies
  const discrepancies = cashReconciliations.filter(r => Math.abs(Number(r.discrepancy)) > 0);
  const totalDiscrepancy = discrepancies.reduce((sum, r) => sum + Math.abs(Number(r.discrepancy)), 0);
  const averageDiscrepancy = discrepancies.length > 0 ? totalDiscrepancy / discrepancies.length : 0;
  
  // Large discrepancies (> 50000 IDR equivalent)
  const largeDiscrepancies = discrepancies.filter(r => Math.abs(Number(r.discrepancy)) > 50000);

  // Reconciliation Performance
  const pendingReconciliations = cashReconciliations.filter(r => r.resolutionStatus === 'PENDING');
  const resolvedReconciliations = cashReconciliations.filter(r => r.resolvedAt);
  
  const resolutionTimes = resolvedReconciliations
    .filter(r => r.resolvedAt && r.createdAt)
    .map(r => new Date(r.resolvedAt).getTime() - new Date(r.createdAt).getTime());
  
  const averageResolutionTime = resolutionTimes.length > 0
    ? Math.round(resolutionTimes.reduce((sum, time) => sum + time, 0) / (resolutionTimes.length * 60 * 60 * 1000)) // in hours
    : 0;

  // Cash Flow
  const totalCashIn = cashReconciliations.reduce((sum, r) => sum + Number(r.expectedAmount), 0);
  const totalCashOut = cashReconciliations.reduce((sum, r) => sum + Number(r.actualAmount), 0);
  const averageOpeningBalance = cashReconciliations.length > 0
    ? cashReconciliations.reduce((sum, r) => sum + Number(r.openingBalance), 0) / cashReconciliations.length
    : 0;

  return {
    drawerSessions: {
      totalSessions: drawerSessions.length,
      averageSessionDuration,
      sessionsPerDay: Math.round(drawerSessions.length / Math.max(daysDiff, 1)),
      utilizationRate: Math.round((closedSessions.length / Math.max(drawerSessions.length, 1)) * 100)
    },
    cashDiscrepancies: {
      totalDiscrepancies: discrepancies.length,
      averageDiscrepancy: Math.round(averageDiscrepancy),
      discrepancyTrend: 0, // Would need historical data to calculate
      largeDiscrepanciesCount: largeDiscrepancies.length
    },
    reconciliationPerformance: {
      pendingCount: pendingReconciliations.length,
      averageResolutionTime,
      resolutionRate: Math.round((resolvedReconciliations.length / Math.max(cashReconciliations.length, 1)) * 100),
      overdueCount: pendingReconciliations.filter(r => {
        const daysSinceCreated = (Date.now() - new Date(r.createdAt).getTime()) / (1000 * 60 * 60 * 24);
        return daysSinceCreated > 1; // Overdue if pending for more than 1 day
      }).length
    },
    cashFlow: {
      totalCashIn: Math.round(totalCashIn),
      totalCashOut: Math.round(totalCashOut),
      netCashFlow: Math.round(totalCashIn - totalCashOut),
      averageOpeningBalance: Math.round(averageOpeningBalance)
    }
  };
}

// Helper function to calculate system performance metrics
function calculateSystemPerformanceMetrics(
  activityLogs: any[],
  transactions: any[],
  users: any[],
  startDate: Date,
  endDate: Date
): SystemPerformanceMetrics {
  const daysDiff = Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));

  // User Activity
  const loginLogs = activityLogs.filter(log => log.action.includes('login') || log.action.includes('LOGIN'));
  const uniqueUsers = new Set(activityLogs.map(log => log.userId));

  // Transaction Processing
  const completedTransactions = transactions.filter(t => t.status === 'COMPLETED');
  const failedTransactions = transactions.filter(t => t.status === 'FAILED' || t.paymentStatus === 'FAILED');

  // Calculate hourly transaction distribution
  const hourlyTransactions = new Array(24).fill(0);
  transactions.forEach(t => {
    const hour = new Date(t.createdAt).getHours();
    hourlyTransactions[hour]++;
  });
  const peakHour = hourlyTransactions.indexOf(Math.max(...hourlyTransactions));
  const peakHourTransactions = Math.max(...hourlyTransactions);

  return {
    userActivity: {
      totalLogins: loginLogs.length,
      averageSessionDuration: 120, // Placeholder - would need session tracking
      peakHourActivity: peakHour,
      activeUsersCount: uniqueUsers.size
    },
    transactionProcessing: {
      averageProcessingTime: 15, // Placeholder - would need timing data
      transactionsPerHour: Math.round(transactions.length / Math.max(daysDiff * 24, 1)),
      peakHourTransactions,
      processingEfficiency: Math.round((completedTransactions.length / Math.max(transactions.length, 1)) * 100)
    },
    errorRates: {
      failedTransactions: failedTransactions.length,
      errorRate: Math.round((failedTransactions.length / Math.max(transactions.length, 1)) * 100),
      systemIssues: 0, // Would need error logging system
      recoveryTime: 5 // Placeholder
    },
    peakLoadAnalysis: {
      busiestHour: peakHour,
      concurrentUsers: Math.max(uniqueUsers.size, 1),
      systemLoad: Math.min(Math.round((transactions.length / (daysDiff * 100)) * 100), 100),
      responseTime: 250 // Placeholder
    }
  };
}

// Helper function to calculate inventory operation metrics
function calculateInventoryOperationMetrics(
  stockAdjustments: any[],
  startDate: Date,
  endDate: Date
): InventoryOperationMetrics {
  const daysDiff = Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));

  // Stock Adjustments
  const reasonCounts = stockAdjustments.reduce((acc, adj) => {
    acc[adj.reason] = (acc[adj.reason] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);

  const reasonBreakdown = Object.entries(reasonCounts).map(([reason, count]) => ({
    reason,
    count,
    percentage: Math.round((count / Math.max(stockAdjustments.length, 1)) * 100)
  }));

  return {
    stockAdjustments: {
      totalAdjustments: stockAdjustments.length,
      adjustmentFrequency: Math.round(stockAdjustments.length / Math.max(daysDiff, 1)),
      reasonBreakdown,
      adjustmentTrend: 0 // Would need historical data
    },
    stockMovement: {
      fastMovingItems: 0, // Would need product sales data
      slowMovingItems: 0, // Would need product sales data
      turnoverVelocity: 0, // Would need inventory turnover calculation
      stockoutEvents: reasonBreakdown.find(r => r.reason === 'STOCKOUT')?.count || 0
    },
    lowStockAlerts: {
      totalAlerts: 0, // Would need alert system
      criticalAlerts: 0,
      averageResponseTime: 0,
      restockEfficiency: 85 // Placeholder
    },
    transferEfficiency: {
      totalTransfers: 0, // Would need transfer data
      completionRate: 95, // Placeholder
      averageProcessingTime: 2, // Placeholder
      transferAccuracy: 98 // Placeholder
    }
  };
}

// Helper function to calculate audit compliance metrics
function calculateAuditComplianceMetrics(
  cashAuditAlerts: any[],
  cashReconciliations: any[],
  startDate: Date,
  endDate: Date
): AuditComplianceMetrics {
  // Security Alerts
  const highSeverityAlerts = cashAuditAlerts.filter(alert => alert.severity === 'HIGH' || alert.severity === 'CRITICAL');
  const resolvedAlerts = cashAuditAlerts.filter(alert => alert.isResolved);

  const resolutionTimes = resolvedAlerts
    .filter(alert => alert.resolvedAt && alert.createdAt)
    .map(alert => new Date(alert.resolvedAt).getTime() - new Date(alert.createdAt).getTime());

  const averageResolutionTime = resolutionTimes.length > 0
    ? Math.round(resolutionTimes.reduce((sum, time) => sum + time, 0) / (resolutionTimes.length * 60 * 60 * 1000))
    : 0;

  // Compliance Scores
  const accurateReconciliations = cashReconciliations.filter(r => Math.abs(Number(r.discrepancy)) <= 1000); // Within 1000 IDR
  const reconciliationAccuracy = Math.round((accurateReconciliations.length / Math.max(cashReconciliations.length, 1)) * 100);

  // Risk Indicators
  const largeDiscrepancies = cashReconciliations.filter(r => Math.abs(Number(r.discrepancy)) > 50000);
  const riskScore = Math.min(100, Math.max(0, 100 - (largeDiscrepancies.length * 10) - (highSeverityAlerts.length * 5)));

  let riskLevel: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL' = 'LOW';
  if (riskScore >= 80) riskLevel = 'LOW';
  else if (riskScore >= 60) riskLevel = 'MEDIUM';
  else if (riskScore >= 40) riskLevel = 'HIGH';
  else riskLevel = 'CRITICAL';

  // Compliance Rating
  let complianceRating = 'A';
  if (reconciliationAccuracy >= 95) complianceRating = 'A';
  else if (reconciliationAccuracy >= 90) complianceRating = 'B';
  else if (reconciliationAccuracy >= 80) complianceRating = 'C';
  else if (reconciliationAccuracy >= 70) complianceRating = 'D';
  else complianceRating = 'F';

  return {
    securityAlerts: {
      totalAlerts: cashAuditAlerts.length,
      highSeverityAlerts: highSeverityAlerts.length,
      averageResolutionTime,
      alertTrend: 0 // Would need historical data
    },
    complianceScores: {
      reconciliationAccuracy,
      auditTrailCompleteness: 95, // Placeholder
      complianceRating,
      improvementTrend: 0 // Would need historical data
    },
    riskIndicators: {
      largeDiscrepancyFrequency: largeDiscrepancies.length,
      unusualTransactionPatterns: 0, // Would need pattern analysis
      riskScore,
      riskLevel
    },
    resolutionEfficiency: {
      averageResolutionTime,
      firstTimeResolutionRate: Math.round((resolvedAlerts.length / Math.max(cashAuditAlerts.length, 1)) * 100),
      escalationRate: Math.round((highSeverityAlerts.length / Math.max(cashAuditAlerts.length, 1)) * 100),
      customerSatisfaction: 85 // Placeholder
    }
  };
}

// Helper function to calculate performance benchmarks
async function calculatePerformanceBenchmarks(
  transactions: any[],
  drawerSessions: any[],
  users: any[],
  startDate: Date,
  endDate: Date
): Promise<PerformanceBenchmarks> {
  const daysDiff = Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));
  const hoursDiff = daysDiff * 24;

  // Calculate total revenue
  const totalRevenue = transactions.reduce((sum, t) => sum + Number(t.total), 0);
  const completedTransactions = transactions.filter(t => t.status === 'COMPLETED');
  const cashiers = users.filter(u => u.role === 'CASHIER');

  // Calculate individual scores (0-100)
  const cashManagementScore = Math.min(100, Math.max(0,
    100 - (drawerSessions.filter(s => s.discrepancy && Math.abs(Number(s.discrepancy)) > 10000).length * 10)
  ));

  const systemPerformanceScore = Math.min(100, Math.max(0,
    (completedTransactions.length / Math.max(transactions.length, 1)) * 100
  ));

  const inventoryOpsScore = 85; // Placeholder - would need inventory data
  const complianceScore = 90; // Placeholder - would need compliance data

  // Overall score
  const overallScore = Math.round(
    (cashManagementScore + systemPerformanceScore + inventoryOpsScore + complianceScore) / 4
  );

  // Grade calculation
  let grade = 'F';
  if (overallScore >= 97) grade = 'A+';
  else if (overallScore >= 93) grade = 'A';
  else if (overallScore >= 90) grade = 'A-';
  else if (overallScore >= 87) grade = 'B+';
  else if (overallScore >= 83) grade = 'B';
  else if (overallScore >= 80) grade = 'B-';
  else if (overallScore >= 77) grade = 'C+';
  else if (overallScore >= 73) grade = 'C';
  else if (overallScore >= 70) grade = 'C-';
  else if (overallScore >= 60) grade = 'D';

  // Efficiency ratios
  const revenuePerHour = hoursDiff > 0 ? Math.round(totalRevenue / hoursDiff) : 0;
  const transactionsPerCashier = cashiers.length > 0 ? Math.round(transactions.length / cashiers.length) : 0;
  const costPerTransaction = transactions.length > 0 ? Math.round(1000) : 0; // Placeholder
  const profitMarginRatio = totalRevenue > 0 ? Math.round((totalRevenue * 0.3) / totalRevenue * 100) : 0; // Assuming 30% margin

  // Trend direction (placeholder - would need historical data)
  let trendDirection: 'IMPROVING' | 'STABLE' | 'DECLINING' = 'STABLE';
  if (overallScore >= 85) trendDirection = 'IMPROVING';
  else if (overallScore <= 70) trendDirection = 'DECLINING';

  return {
    dailyOperationalScore: {
      overallScore,
      scoreBreakdown: {
        cashManagement: Math.round(cashManagementScore),
        systemPerformance: Math.round(systemPerformanceScore),
        inventoryOps: Math.round(inventoryOpsScore),
        compliance: Math.round(complianceScore)
      },
      grade
    },
    trendComparisons: {
      weekOverWeek: 0, // Would need historical data
      monthOverMonth: 0, // Would need historical data
      yearOverYear: 0, // Would need historical data
      trendDirection
    },
    targetVsActual: await calculateTargetVsActual(totalRevenue, startDate, endDate),
    efficiencyRatios: {
      revenuePerHour,
      transactionsPerCashier,
      costPerTransaction,
      profitMarginRatio
    }
  };
}

// Helper function to calculate target vs actual performance with real revenue targets
async function calculateTargetVsActual(totalRevenue: number, startDate: Date, endDate: Date) {
  try {
    // Try to find an active revenue target for the period
    const currentTarget = await prisma.revenueTarget.findFirst({
      where: {
        targetType: 'MONTHLY',
        isActive: true,
        startDate: { lte: endDate },
        endDate: { gte: startDate }
      },
      orderBy: [
        { startDate: 'desc' },
        { createdAt: 'desc' }
      ]
    });

    let revenueTarget: number;
    let isDefaultTarget = false;

    if (currentTarget) {
      revenueTarget = parseFloat(currentTarget.amount.toString());
      console.log(`[Target vs Actual] Using target: ${currentTarget.name} - ${revenueTarget}`);
    } else {
      // Calculate historical-based target
      const historicalData = await calculateHistoricalAverage(startDate, endDate);
      revenueTarget = historicalData.suggestedTarget;
      isDefaultTarget = true;
      console.log(`[Target vs Actual] Using historical target: ${revenueTarget}`);
    }

    const targetAchievement = Math.round((totalRevenue / revenueTarget) * 100);
    const performanceGap = revenueTarget - totalRevenue;

    return {
      revenueTarget,
      actualRevenue: totalRevenue,
      targetAchievement,
      performanceGap,
      isDefaultTarget
    };

  } catch (error) {
    console.error('[Target vs Actual] Error:', error);
    // Fallback to default calculation
    const revenueTarget = totalRevenue * 1.1; // 10% growth target
    return {
      revenueTarget,
      actualRevenue: totalRevenue,
      targetAchievement: Math.round((totalRevenue / revenueTarget) * 100),
      performanceGap: revenueTarget - totalRevenue,
      isDefaultTarget: true
    };
  }
}

// Helper function to calculate historical average for target suggestion
async function calculateHistoricalAverage(from: Date, to: Date) {
  try {
    const periodLength = to.getTime() - from.getTime();
    const periodsToAnalyze = 6; // Analyze last 6 similar periods

    let totalRevenue = 0;
    let validPeriods = 0;

    for (let i = 1; i <= periodsToAnalyze; i++) {
      const periodStart = new Date(from.getTime() - (periodLength * i));
      const periodEnd = new Date(to.getTime() - (periodLength * i));

      const transactions = await prisma.transaction.findMany({
        where: {
          status: 'COMPLETED',
          createdAt: {
            gte: periodStart,
            lte: periodEnd
          }
        },
        select: { total: true }
      });

      const periodRevenue = transactions.reduce((sum, t) => sum + parseFloat(t.total.toString()), 0);

      if (periodRevenue > 0) {
        totalRevenue += periodRevenue;
        validPeriods++;
      }
    }

    const average = validPeriods > 0 ? totalRevenue / validPeriods : 0;
    const growthRate = 0.10; // 10% growth target
    const suggestedTarget = average * (1 + growthRate);

    console.log(`[Historical Analysis] Average: ${average}, Suggested Target: ${suggestedTarget}, Periods: ${validPeriods}`);

    return {
      average,
      suggestedTarget,
      growthRate,
      periodsAnalyzed: validPeriods
    };

  } catch (error) {
    console.error('[Historical Analysis] Error:', error);
    return {
      average: 0,
      suggestedTarget: 1000000, // Default 1M IDR
      growthRate: 0.10,
      periodsAnalyzed: 0
    };
  }
}
