import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/auth";
import { jwtVerify } from "jose";
import { z } from "zod";
import { Prisma } from "@/generated/prisma";
import { checkAndRemoveExpiredTemporaryPrices } from "@/lib/temporary-price-utils";

// Helper function to verify the authentication token
async function verifyAuthToken(request: NextRequest) {
  // Get the session token from cookies
  const token = request.cookies.get("session-token");

  // Check if token exists
  if (!token) {
    console.log("[API] /api/products/[id] - No session token found");
    return {
      authenticated: false,
      error: "Unauthorized - No session token",
      status: 403,
      user: null
    };
  }

  // Verify the token
  try {
    const { payload } = await jwtVerify(
      token.value,
      new TextEncoder().encode(process.env.NEXTAUTH_SECRET || "fallback-secret")
    );

    return {
      authenticated: true,
      user: {
        id: payload.id as string,
        name: payload.name as string,
        email: payload.email as string,
        role: payload.role as string
      }
    };
  } catch (error) {
    console.error("[API] /api/products/[id] - JWT verification failed:", error);
    return {
      authenticated: false,
      error: "Unauthorized - Invalid token",
      status: 403,
      user: null
    };
  }
}

// Product update schema for validation
const productUpdateSchema = z.object({
  name: z.string().min(2, { message: "Name must be at least 2 characters" }).optional(),
  description: z.string().optional().nullable(),
  sku: z.string().min(1, { message: "SKU is required" }).optional(),
  barcode: z.string().min(1, { message: "Barcode is required" }).optional(),
  categoryId: z.string().optional().nullable(),
  unitId: z.string().optional(),
  supplierId: z.string().optional().nullable(),
  basePrice: z.number().positive({ message: "Base price must be positive" }).optional(),
  purchasePrice: z.number().positive({ message: "Purchase price must be positive" }).optional().nullable(),
  optionalPrice1: z.number().positive({ message: "Optional price 1 must be positive" }).optional().nullable(),
  optionalPrice2: z.number().positive({ message: "Optional price 2 must be positive" }).optional().nullable(),
  discountValue: z.number().min(0, { message: "Discount value must be non-negative" }).optional().nullable(),
  discountType: z.enum(["FIXED", "PERCENTAGE"]).optional().nullable(),
  expiryDate: z.string().optional().nullable().transform(val => val ? new Date(val) : null),
  imageUrl: z.string().optional().nullable(),
  active: z.boolean().optional(),
  quantity: z.number().min(0).optional().nullable(),
});

// GET /api/products/[id] - Get a specific product
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication
    const auth = await verifyAuthToken(request);

    if (!auth.authenticated) {
      return NextResponse.json(
        { error: auth.error },
        { status: auth.status }
      );
    }

    // Check for and remove expired temporary prices for this product
    if (auth.authenticated) {
      await checkAndRemoveExpiredTemporaryPrices(auth.user.id, params.id);
    }

    // Get product
    const product = await prisma.product.findUnique({
      where: { id: params.id },
      include: {
        category: true,
        unit: true,
        supplier: true, // Keep for backward compatibility
        productSuppliers: {
          include: {
            supplier: {
              select: {
                id: true,
                name: true,
                contactPerson: true,
                phone: true,
                email: true,
                address: true,
              }
            }
          },
          orderBy: [
            { isPreferred: 'desc' },
            { purchasePrice: 'asc' }
          ]
        },
        stockBatches: {
          where: {
            status: 'ACTIVE',
            remainingQuantity: { gt: 0 }
          },
          include: {
            productSupplier: {
              include: {
                supplier: {
                  select: {
                    id: true,
                    name: true,
                  }
                }
              }
            }
          },
          orderBy: [
            { expiryDate: 'asc' },
            { receivedDate: 'asc' }
          ],
          take: 10 // Show first 10 active batches
        },
        storeStock: true,
        warehouseStock: true,
        temporaryPrice: {
          include: {
            user: {
              select: {
                id: true,
                name: true,
              },
            },
          },
        },
      },
    });

    // Check if product exists
    if (!product) {
      return NextResponse.json(
        { error: "Product not found" },
        { status: 404 }
      );
    }

    return NextResponse.json({ product });
  } catch (error) {
    console.error("Error fetching product:", error);
    return NextResponse.json(
      { error: "Failed to fetch product", message: (error as Error).message },
      { status: 500 }
    );
  }
}

// PATCH /api/products/[id] - Update a product
export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication
    const auth = await verifyAuthToken(request);

    if (!auth.authenticated) {
      return NextResponse.json(
        { error: auth.error },
        { status: auth.status }
      );
    }

    // Check if user has permission to update products
    const hasPermission = ["SUPER_ADMIN", "WAREHOUSE_ADMIN"].includes(auth.user.role);
    if (!hasPermission) {
      return NextResponse.json(
        { error: "Unauthorized - Insufficient permissions" },
        { status: 403 }
      );
    }

    // Get product
    const existingProduct = await prisma.product.findUnique({
      where: { id: params.id },
    });

    // Check if product exists
    if (!existingProduct) {
      return NextResponse.json(
        { error: "Product not found" },
        { status: 404 }
      );
    }

    // Get request body
    const body = await request.json();

    // Validate product data
    const validationResult = productUpdateSchema.safeParse(body);
    if (!validationResult.success) {
      return NextResponse.json(
        { error: "Validation failed", issues: validationResult.error.issues },
        { status: 400 }
      );
    }

    const productData = validationResult.data;

    // Check if SKU already exists (if changing SKU)
    if (productData.sku && productData.sku !== existingProduct.sku) {
      const existingSku = await prisma.product.findUnique({
        where: { sku: productData.sku },
      });

      if (existingSku) {
        return NextResponse.json(
          { error: "SKU already exists" },
          { status: 400 }
        );
      }
    }

    // Check if barcode already exists (if changing barcode)
    if (productData.barcode !== undefined && productData.barcode !== existingProduct.barcode) {
      // Check for duplicates if the barcode has changed
      const existingBarcode = await prisma.product.findUnique({
        where: { barcode: productData.barcode },
      });

      if (existingBarcode) {
        return NextResponse.json(
          { error: "Barcode already exists" },
          { status: 400 }
        );
      }
    }

    // Verify that the unitId exists (if changing unitId)
    if (productData.unitId && productData.unitId !== existingProduct.unitId) {
      const unitExists = await prisma.unit.findUnique({
        where: { id: productData.unitId },
      });

      if (!unitExists) {
        return NextResponse.json(
          { error: "Unit not found", message: "The selected unit does not exist" },
          { status: 400 }
        );
      }
    }

    // Track changes for activity log
    const changes: string[] = [];
    if (productData.name && productData.name !== existingProduct.name) {
      changes.push(`name: ${existingProduct.name} → ${productData.name}`);
    }
    if (productData.sku && productData.sku !== existingProduct.sku) {
      changes.push(`SKU: ${existingProduct.sku} → ${productData.sku}`);
    }
    if (productData.basePrice && productData.basePrice !== Number(existingProduct.basePrice)) {
      changes.push(`base price: ${existingProduct.basePrice} → ${productData.basePrice}`);
    }
    if (productData.purchasePrice && productData.purchasePrice !== Number(existingProduct.purchasePrice || 0)) {
      changes.push(`purchase price: ${existingProduct.purchasePrice || 0} → ${productData.purchasePrice}`);
    }
    if (productData.optionalPrice1 && productData.optionalPrice1 !== Number(existingProduct.optionalPrice1 || 0)) {
      changes.push(`optional price 1: ${existingProduct.optionalPrice1 || 0} → ${productData.optionalPrice1}`);
    }
    if (productData.optionalPrice2 && productData.optionalPrice2 !== Number(existingProduct.optionalPrice2 || 0)) {
      changes.push(`optional price 2: ${existingProduct.optionalPrice2 || 0} → ${productData.optionalPrice2}`);
    }
    if (productData.discountValue !== undefined && productData.discountValue !== Number(existingProduct.discountValue || 0)) {
      changes.push(`discount value: ${existingProduct.discountValue || 0} → ${productData.discountValue}`);
    }
    if (productData.discountType && productData.discountType !== existingProduct.discountType) {
      changes.push(`discount type: ${existingProduct.discountType || 'none'} → ${productData.discountType}`);
    }
    if (productData.supplierId && productData.supplierId !== existingProduct.supplierId) {
      changes.push(`supplier changed`);
    }
    if (productData.expiryDate && productData.expiryDate !== existingProduct.expiryDate) {
      changes.push(`expiry date changed`);
    }
    if (productData.active !== undefined && productData.active !== existingProduct.active) {
      changes.push(`active: ${existingProduct.active} → ${productData.active}`);
    }
    if (productData.quantity !== undefined) {
      changes.push(`stock quantity updated`);
    }

    // Prepare data for update - handle relations properly
    const updateData: any = { ...productData };

    // Remove relation IDs and prepare relation objects
    if ('categoryId' in updateData) {
      if (updateData.categoryId) {
        updateData.category = { connect: { id: updateData.categoryId } };
      } else {
        updateData.category = { disconnect: true };
      }
      delete updateData.categoryId;
    }

    if ('unitId' in updateData) {
      updateData.unit = { connect: { id: updateData.unitId } };
      delete updateData.unitId;
    }

    if ('supplierId' in updateData) {
      if (updateData.supplierId) {
        updateData.supplier = { connect: { id: updateData.supplierId } };
      } else {
        updateData.supplier = { disconnect: true };
      }
      delete updateData.supplierId;
    }

    // Remove quantity as it's handled separately
    if ('quantity' in updateData) {
      delete updateData.quantity;
    }

    // Handle discount fields - remove them from updateData and add them to a separate object
    // This is a workaround for the Prisma client not being updated
    const discountValue = updateData.discountValue;
    const discountType = updateData.discountType;

    // Remove discount fields from updateData
    delete updateData.discountValue;
    delete updateData.discountType;

    console.log("Update data:", JSON.stringify(updateData, null, 2));

    // Update product
    const product = await prisma.product.update({
      where: { id: params.id },
      data: updateData,
      include: {
        category: true,
        unit: true,
        supplier: true,
        storeStock: true,
        warehouseStock: true,
        temporaryPrice: {
          include: {
            user: {
              select: {
                id: true,
                name: true,
              },
            },
          },
        },
      },
    });

    // Update discount fields separately if they were provided
    if (discountValue !== undefined || discountType !== undefined) {
      // Create an update object for the discount fields
      const discountUpdateData: any = {};

      if (discountValue !== undefined) {
        discountUpdateData.discountValue = discountValue;
      }

      if (discountType !== undefined) {
        discountUpdateData.discountType = discountType;
      }

      // Use standard Prisma update instead of raw SQL
      await prisma.product.update({
        where: { id: product.id },
        data: discountUpdateData,
      });

      // Fetch the updated product to include the discount fields
      const updatedProduct = await prisma.product.findUnique({
        where: { id: product.id },
        include: {
          category: true,
          unit: true,
          supplier: true,
          storeStock: true,
          warehouseStock: true,
          temporaryPrice: {
            include: {
              user: {
                select: {
                  id: true,
                  name: true,
                },
              },
            },
          },
        },
      });

      if (updatedProduct) {
        Object.assign(product, updatedProduct);
      }
    }

    // Update store stock if quantity is provided
    if (productData.quantity !== undefined) {
      await prisma.storeStock.upsert({
        where: { productId: params.id },
        update: { quantity: productData.quantity },
        create: {
          productId: params.id,
          quantity: productData.quantity,
          minThreshold: 5, // Default minimum threshold
        },
      });
    }

    // Log activity
    if (changes.length > 0) {
      await prisma.activityLog.create({
        data: {
          userId: auth.user.id,
          action: "UPDATE_PRODUCT",
          details: `Updated product: ${product.name} (${changes.join(", ")})`,
        },
      });
    }

    return NextResponse.json({ product });
  } catch (error) {
    console.error("Error updating product:", error);
    return NextResponse.json(
      { error: "Failed to update product", message: (error as Error).message },
      { status: 500 }
    );
  }
}

// DELETE /api/products/[id] - Delete a product
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication
    const auth = await verifyAuthToken(request);

    if (!auth.authenticated) {
      return NextResponse.json(
        { error: auth.error },
        { status: auth.status }
      );
    }

    // Check if user has permission to delete products
    const hasPermission = ["SUPER_ADMIN", "WAREHOUSE_ADMIN"].includes(auth.user.role);
    if (!hasPermission) {
      return NextResponse.json(
        { error: "Unauthorized - Insufficient permissions" },
        { status: 403 }
      );
    }

    // Get product
    const product = await prisma.product.findUnique({
      where: { id: params.id },
    });

    // Check if product exists
    if (!product) {
      return NextResponse.json(
        { error: "Product not found" },
        { status: 404 }
      );
    }

    // Check if product is used in transactions
    const transactionItems = await prisma.transactionItem.findFirst({
      where: { productId: params.id },
    });

    if (transactionItems) {
      // Instead of deleting, mark as inactive
      const updatedProduct = await prisma.product.update({
        where: { id: params.id },
        data: { active: false },
      });

      // Log activity
      await prisma.activityLog.create({
        data: {
          userId: auth.user.id,
          action: "DEACTIVATE_PRODUCT",
          details: `Deactivated product: ${product.name} (SKU: ${product.sku}) - Cannot delete due to existing transactions`,
        },
      });

      return NextResponse.json({
        product: updatedProduct,
        message: "Product has been deactivated instead of deleted because it is used in transactions",
      });
    }

    // Delete product (this will cascade to storeStock and warehouseStock)
    await prisma.product.delete({
      where: { id: params.id },
    });

    // Log activity
    await prisma.activityLog.create({
      data: {
        userId: auth.user.id,
        action: "DELETE_PRODUCT",
        details: `Deleted product: ${product.name} (SKU: ${product.sku})`,
      },
    });

    return NextResponse.json({
      success: true,
      message: "Product deleted successfully",
    });
  } catch (error) {
    console.error("Error deleting product:", error);
    return NextResponse.json(
      { error: "Failed to delete product", message: (error as Error).message },
      { status: 500 }
    );
  }
}
