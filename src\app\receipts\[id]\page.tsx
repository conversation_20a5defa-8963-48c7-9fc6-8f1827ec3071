"use client";

import { useState, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>ader2, <PERSON><PERSON>, ArrowLeft } from "lucide-react";
import { formatCurrency, formatDate } from "@/lib/utils";
import { useStoreInfo } from "@/contexts/store-info-context";
import Link from "next/link";
import { use } from "react";
import "./print-styles.css";

interface Transaction {
  id: string;
  transactionDate: string;
  subtotal: number;
  discount: number;
  tax: number;
  total: number;
  paymentMethod: string;
  paymentStatus: string;
  cashReceived?: number;
  changeAmount?: number;
  notes?: string;
  cashier: {
    id: string;
    name: string;
  };
  customer?: {
    id: string;
    name: string;
    phone?: string;
  };
  terminal?: {
    id: string;
    name: string;
    location?: string;
  };
  items: Array<{
    id: string;
    productId: string;
    quantity: number;
    unitPrice: number;
    discount: number;
    subtotal: number;
    product: {
      id: string;
      name: string;
      sku: string;
      unit: {
        abbreviation: string;
      };
    };
  }>;
}

export default function ReceiptPage({ params }: { params: { id: string } }) {
  const { storeInfo } = useStoreInfo();
  const [transaction, setTransaction] = useState<Transaction | null>(null);
  const [businessSettings, setBusinessSettings] = useState<Record<string, string>>({});
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Unwrap params using React.use()
  const unwrappedParams = use(params as any) as { id: string };
  const id = unwrappedParams.id;

  // Fetch transaction details and business settings
  useEffect(() => {
    const fetchData = async () => {
      setIsLoading(true);
      setError(null);

      try {
        // Fetch transaction and settings in parallel
        const [transactionResponse, settingsResponse] = await Promise.all([
          fetch(`/api/transactions/${id}`),
          fetch("/api/settings"),
        ]);

        const transactionData = await transactionResponse.json();
        const settingsData = await settingsResponse.json();

        if (!transactionResponse.ok) {
          throw new Error(transactionData.error || "Failed to fetch transaction details");
        }

        setTransaction(transactionData.transaction);

        if (settingsResponse.ok) {
          setBusinessSettings(settingsData.settings || {});
        }
      } catch (err) {
        setError(err instanceof Error ? err.message : "An unknown error occurred");
        console.error("Error fetching data:", err);
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, [id]);

  // Handle print receipt
  const handlePrint = () => {
    window.print();
  };

  if (isLoading) {
    return (
      <div className="container mx-auto py-8 flex justify-center">
        <Loader2 className="h-8 w-8 animate-spin text-gray-500" />
      </div>
    );
  }

  if (error || !transaction) {
    return (
      <div className="container mx-auto py-8">
        <Alert variant="destructive" className="mb-6">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error || "Transaction not found"}</AlertDescription>
        </Alert>
        <Button variant="outline" asChild>
          <Link href="/pos">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to POS
          </Link>
        </Button>
      </div>
    );
  }

  return (
    <>
      <div className="container mx-auto py-8 max-w-2xl">
        <div className="print:hidden mb-6 flex justify-between">
          <Button variant="outline" asChild>
            <Link href="/pos">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to POS
            </Link>
          </Button>
          <Button onClick={handlePrint}>
            <Printer className="h-4 w-4 mr-2" />
            Print Receipt
          </Button>
        </div>

        <Card className="border-none print:shadow-none print:border-none print:m-0 print:p-0">
          <CardHeader className="text-center border-b pb-3 print:pb-2 print:mb-2">
            <CardTitle className="text-xl font-bold print:text-lg print:mb-1">
              {storeInfo?.storeName || businessSettings.STORE_NAME || "Next POS Store"}
            </CardTitle>
            {(storeInfo?.address || businessSettings.STORE_ADDRESS) && (
              <div className="text-sm text-gray-600 print:text-xs print:mb-0">
                {storeInfo?.address || businessSettings.STORE_ADDRESS}
              </div>
            )}
            {(storeInfo?.phone || businessSettings.STORE_PHONE) && (
              <div className="text-sm text-gray-600 print:text-xs print:mb-1">
                {storeInfo?.phone || businessSettings.STORE_PHONE}
              </div>
            )}
            {storeInfo?.email && (
              <div className="text-sm text-gray-600 print:text-xs print:mb-1">
                {storeInfo.email}
              </div>
            )}
            <div className="text-base font-semibold mt-2 pt-2 border-t print:text-sm print:mt-1 print:pt-1">
              RECEIPT
            </div>
          </CardHeader>
          <CardContent className="pt-4 print:pt-2 print:px-0">
            <div className="space-y-4 print:space-y-2">
              <div className="flex justify-between text-sm print:text-xs">
                <div>
                  <div className="font-medium print:font-normal">Receipt #</div>
                  <div className="print:text-xs">{transaction.id}</div>
                </div>
                <div className="text-right">
                  <div className="font-medium print:font-normal">Date</div>
                  <div className="print:text-xs">
                    {formatDate(new Date(transaction.transactionDate))}
                  </div>
                </div>
              </div>

              <div className="border-t border-b py-2 print:py-1">
                <div className="flex justify-between mb-1 text-sm print:text-xs">
                  <div className="font-medium print:font-normal">Cashier</div>
                  <div>{transaction.cashier.name}</div>
                </div>
                {transaction.terminal && (
                  <div className="flex justify-between mb-1 text-sm print:text-xs">
                    <div className="font-medium print:font-normal">Terminal</div>
                    <div>
                      {transaction.terminal.name}
                      {transaction.terminal.location && ` (${transaction.terminal.location})`}
                    </div>
                  </div>
                )}
                {transaction.customer && (
                  <div className="flex justify-between text-sm print:text-xs">
                    <div className="font-medium print:font-normal">Customer</div>
                    <div>{transaction.customer.name}</div>
                  </div>
                )}
              </div>

              <div>
                <div className="font-medium mb-1 print:mb-1 print:text-sm">Items</div>
                <table className="w-full text-sm print:text-xs">
                  <thead>
                    <tr className="border-b">
                      <th className="text-left py-1 print:py-0">Item</th>
                      <th className="text-right py-1 print:py-0">Qty</th>
                      <th className="text-right py-1 print:py-0">Price</th>
                      <th className="text-right py-1 print:py-0">Disc</th>
                      <th className="text-right py-1 print:py-0">Total</th>
                    </tr>
                  </thead>
                  <tbody>
                    {transaction.items.map((item) => (
                      <tr key={item.id} className="border-b">
                        <td className="py-1 print:py-0">
                          <div className="print:text-xs">{item.product.name}</div>
                          <div className="text-xs text-gray-500 print:text-xs print:hidden">
                            {item.product.sku}
                          </div>
                        </td>
                        <td className="text-right py-1 print:py-0 print:text-xs">
                          {item.quantity} {item.product.unit.abbreviation}
                        </td>
                        <td className="text-right py-1 print:py-0 print:text-xs">
                          {formatCurrency(item.unitPrice)}
                        </td>
                        <td className="text-right py-1 print:py-0 print:text-xs">
                          {item.discount > 0 ? formatCurrency(item.discount) : "-"}
                        </td>
                        <td className="text-right py-1 print:py-0 print:text-xs">
                          {formatCurrency(item.subtotal - item.discount)}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>

              <div className="border-t pt-2 print:pt-1">
                <div className="flex justify-between mb-1 text-sm print:text-xs">
                  <div>Subtotal</div>
                  <div>{formatCurrency(transaction.subtotal)}</div>
                </div>
                {transaction.discount > 0 && (
                  <div className="flex justify-between mb-1 text-sm print:text-xs">
                    <div>Discount</div>
                    <div>-{formatCurrency(transaction.discount)}</div>
                  </div>
                )}
                {transaction.tax > 0 && (
                  <div className="flex justify-between mb-1 text-sm print:text-xs">
                    <div>Tax</div>
                    <div>{formatCurrency(transaction.tax)}</div>
                  </div>
                )}
                <div className="flex justify-between font-bold text-base mt-1 pt-1 border-t print:text-sm print:mt-1 print:pt-1">
                  <div>Total</div>
                  <div>{formatCurrency(transaction.total)}</div>
                </div>
              </div>

              <div className="border-t pt-2 print:pt-1">
                <div className="flex justify-between mb-1 text-sm print:text-xs">
                  <div className="font-medium print:font-normal">Payment Method</div>
                  <div>{transaction.paymentMethod}</div>
                </div>
                <div className="flex justify-between mb-1 text-sm print:text-xs">
                  <div className="font-medium print:font-normal">Payment Status</div>
                  <div>{transaction.paymentStatus}</div>
                </div>
                {transaction.paymentMethod === "CASH" && transaction.cashReceived && (
                  <>
                    <div className="flex justify-between mb-1 text-sm print:text-xs">
                      <div className="font-medium print:font-normal">Cash Received</div>
                      <div>{formatCurrency(transaction.cashReceived)}</div>
                    </div>
                    {transaction.changeAmount !== undefined && (
                      <div className="flex justify-between mb-1 text-sm print:text-xs">
                        <div className="font-medium print:font-normal">Change</div>
                        <div>{formatCurrency(transaction.changeAmount)}</div>
                      </div>
                    )}
                  </>
                )}
              </div>

              {transaction.notes && (
                <div className="border-t pt-2 print:pt-1">
                  <div className="font-medium mb-1 text-sm print:text-xs print:font-normal">
                    Notes
                  </div>
                  <div className="text-sm print:text-xs">{transaction.notes}</div>
                </div>
              )}

              <div className="text-center text-sm text-gray-500 pt-3 border-t print:pt-2 print:text-xs">
                <div className="font-medium print:font-normal">
                  {businessSettings.RECEIPT_FOOTER || "Thank you for your purchase!"}
                </div>
                <div className="mt-1 print:mt-1">
                  {storeInfo?.storeName || businessSettings.STORE_NAME || "Next POS System"}
                </div>
                <div className="text-xs mt-1 print:mt-0">Receipt #{transaction.id}</div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </>
  );
}
