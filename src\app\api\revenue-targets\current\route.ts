import { NextRequest, NextResponse } from "next/server";
import { PrismaClient } from "@/generated/prisma";
import { verifyAuthToken } from "@/lib/auth-utils";

// Create a singleton instance of PrismaClient to prevent too many connections
const globalForPrisma = globalThis as unknown as { prisma: PrismaClient };
const prisma = globalForPrisma.prisma || new PrismaClient();
if (process.env.NODE_ENV !== "production") globalForPrisma.prisma = prisma;

// GET /api/revenue-targets/current - Get current revenue target for date range
export async function GET(request: NextRequest) {
  try {
    console.log('[Current Revenue Target API] GET request received');

    // Verify authentication
    const authResult = await verifyAuthToken(request);
    if (!authResult.authenticated || !authResult.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Check if user has permission (SUPER_ADMIN or FINANCE_ADMIN)
    if (!['SUPER_ADMIN', 'FINANCE_ADMIN'].includes(authResult.user.role)) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    const { searchParams } = new URL(request.url);
    const fromParam = searchParams.get("from");
    const toParam = searchParams.get("to");
    const targetType = searchParams.get("targetType") || "MONTHLY";

    // Default to current month if no dates provided
    const now = new Date();
    const from = fromParam ? new Date(fromParam) : new Date(now.getFullYear(), now.getMonth(), 1);
    const to = toParam ? new Date(toParam) : new Date(now.getFullYear(), now.getMonth() + 1, 0);

    console.log(`[Current Revenue Target API] Looking for ${targetType} target between ${from.toISOString()} and ${to.toISOString()}`);

    // Find the most relevant target for the given period
    const target = await prisma.revenueTarget.findFirst({
      where: {
        targetType: targetType as any,
        isActive: true,
        startDate: { lte: to },
        endDate: { gte: from }
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true
          }
        }
      },
      orderBy: [
        { startDate: 'desc' },
        { createdAt: 'desc' }
      ]
    });

    if (!target) {
      console.log('[Current Revenue Target API] No target found, calculating default');
      
      // Calculate default target based on historical data
      const historicalData = await calculateHistoricalAverage(from, to);
      
      return NextResponse.json({
        success: true,
        data: {
          id: null,
          name: `Default ${targetType} Target`,
          description: "Auto-calculated based on historical performance",
          targetType,
          startDate: from,
          endDate: to,
          amount: historicalData.suggestedTarget,
          isActive: true,
          isDefault: true,
          historicalAverage: historicalData.average,
          growthRate: historicalData.growthRate,
          createdAt: new Date(),
          updatedAt: new Date(),
          user: null
        },
        timestamp: new Date().toISOString()
      });
    }

    console.log(`[Current Revenue Target API] Found target: ${target.name}`);

    return NextResponse.json({
      success: true,
      data: {
        ...target,
        isDefault: false
      },
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('[Current Revenue Target API] Error:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to fetch current revenue target',
        timestamp: new Date().toISOString()
      },
      { status: 500 }
    );
  }
}

// Helper function to calculate historical average and suggest target
async function calculateHistoricalAverage(from: Date, to: Date) {
  try {
    // Calculate the same period in previous months/years for comparison
    const periodLength = to.getTime() - from.getTime();
    const periodsToAnalyze = 6; // Analyze last 6 similar periods
    
    let totalRevenue = 0;
    let validPeriods = 0;
    
    for (let i = 1; i <= periodsToAnalyze; i++) {
      const periodStart = new Date(from.getTime() - (periodLength * i));
      const periodEnd = new Date(to.getTime() - (periodLength * i));
      
      // Get revenue for this historical period
      const transactions = await prisma.transaction.findMany({
        where: {
          status: 'COMPLETED',
          createdAt: {
            gte: periodStart,
            lte: periodEnd
          }
        },
        select: {
          total: true
        }
      });
      
      const periodRevenue = transactions.reduce((sum, t) => sum + parseFloat(t.total.toString()), 0);
      
      if (periodRevenue > 0) {
        totalRevenue += periodRevenue;
        validPeriods++;
      }
    }
    
    const average = validPeriods > 0 ? totalRevenue / validPeriods : 0;
    
    // Apply growth rate (default 10% growth target)
    const growthRate = 0.10;
    const suggestedTarget = average * (1 + growthRate);
    
    console.log(`[Historical Analysis] Average: ${average}, Suggested Target: ${suggestedTarget}, Growth Rate: ${growthRate * 100}%`);
    
    return {
      average,
      suggestedTarget,
      growthRate,
      periodsAnalyzed: validPeriods
    };
    
  } catch (error) {
    console.error('[Historical Analysis] Error:', error);
    // Fallback to a basic calculation
    return {
      average: 0,
      suggestedTarget: 1000000, // Default 1M IDR target
      growthRate: 0.10,
      periodsAnalyzed: 0
    };
  }
}
