"use client";

import { useState, useEffect } from "react";
import { X, Search, Check } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { useClientAuth } from "@/hooks/use-client-auth";

type User = {
  id: string;
  name: string;
  email: string;
  role: string;
};

type Conversation = {
  id: string;
  title: string;
  participants: User[];
  latestMessage: {
    id: string;
    content: string;
    createdAt: string;
  } | null;
  updatedAt: string;
};

interface NewChatDialogProps {
  onClose: () => void;
  onConversationCreated: (conversation: Conversation) => void;
}

export function NewChatDialog({ onClose, onConversationCreated }: NewChatDialogProps) {
  const [users, setUsers] = useState<User[]>([]);
  const [selectedUsers, setSelectedUsers] = useState<User[]>([]);
  const [searchQuery, setSearchQuery] = useState("");
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { user } = useClientAuth();

  // Fetch users and active conversations
  const fetchUsers = async () => {
    if (!user) return;

    try {
      setLoading(true);
      setError(null);

      // Fetch all users
      const usersResponse = await fetch("/api/users?for=chat");

      if (!usersResponse.ok) {
        const errorData = await usersResponse.json();
        throw new Error(errorData.error || "Failed to fetch users");
      }

      // Fetch existing conversations
      const conversationsResponse = await fetch("/api/conversations");

      if (!conversationsResponse.ok) {
        const errorData = await conversationsResponse.json();
        throw new Error(errorData.error || "Failed to fetch conversations");
      }

      const usersData = await usersResponse.json();
      const conversationsData = await conversationsResponse.json();

      // Get IDs of users who already have one-on-one conversations with the current user
      const existingConversationUserIds = conversationsData.conversations
        .filter((conv: any) => conv.participants.length === 2) // Only one-on-one conversations
        .map((conv: any) => {
          // Find the other participant (not the current user)
          const otherParticipant = conv.participants.find((p: User) => p.id !== user.id);
          return otherParticipant ? otherParticipant.id : null;
        })
        .filter(Boolean); // Remove nulls

      // Filter out the current user, inactive users, and users with existing conversations
      const filteredUsers = usersData.users.filter(
        (u: User) =>
          u.id !== user.id && u.active !== false && !existingConversationUserIds.includes(u.id)
      );

      setUsers(filteredUsers);
    } catch (err: any) {
      setError(err.message || "An error occurred while fetching users");
      console.error("Error fetching users:", err);
    } finally {
      setLoading(false);
    }
  };

  // Create a new conversation
  const createConversation = async () => {
    if (selectedUsers.length === 0) {
      setError("Please select at least one user");
      return;
    }

    try {
      setLoading(true);
      setError(null);

      const response = await fetch("/api/conversations", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          participantIds: selectedUsers.map((u) => u.id),
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to create conversation");
      }

      const data = await response.json();
      onConversationCreated(data.conversation);
    } catch (err: any) {
      setError(err.message || "An error occurred while creating the conversation");
      console.error("Error creating conversation:", err);
    } finally {
      setLoading(false);
    }
  };

  // Toggle user selection
  const toggleUserSelection = (selectedUser: User) => {
    if (selectedUsers.some((u) => u.id === selectedUser.id)) {
      setSelectedUsers(selectedUsers.filter((u) => u.id !== selectedUser.id));
    } else {
      setSelectedUsers([...selectedUsers, selectedUser]);
    }
  };

  // Filter users based on search query
  const filteredUsers = users.filter(
    (u) =>
      u.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      u.email.toLowerCase().includes(searchQuery.toLowerCase())
  );

  // Fetch users on mount
  useEffect(() => {
    fetchUsers();
  }, [user]);

  return (
    <div className="fixed bottom-20 right-4 z-50 w-80 rounded-lg border bg-background shadow-lg">
      <div className="flex items-center justify-between border-b p-3">
        <h3 className="font-medium">New Conversation</h3>
        <Button variant="ghost" size="icon" className="h-8 w-8" onClick={onClose}>
          <X className="h-4 w-4" />
        </Button>
      </div>

      <div className="p-3">
        <div className="mb-3">
          <div className="mb-1 text-sm font-medium">Selected Users:</div>
          <div className="flex flex-wrap gap-1">
            {selectedUsers.length === 0 ? (
              <div className="text-xs text-muted-foreground">No users selected</div>
            ) : (
              selectedUsers.map((u) => (
                <div
                  key={u.id}
                  className="flex items-center gap-1 rounded-full bg-primary/10 px-2 py-1 text-xs font-medium text-primary"
                >
                  {u.name}
                  <button
                    className="ml-1 rounded-full hover:bg-primary/20"
                    onClick={() => toggleUserSelection(u)}
                  >
                    <X className="h-3 w-3" />
                  </button>
                </div>
              ))
            )}
          </div>
        </div>

        <div className="mb-3">
          <div className="relative">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              type="search"
              placeholder="Search users..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-8"
            />
          </div>
        </div>

        {error && <div className="mb-3 text-sm text-destructive">{error}</div>}

        <div className="mb-3 max-h-48 overflow-y-auto rounded-md border">
          {loading ? (
            <div className="flex items-center justify-center p-4">
              <div className="h-5 w-5 animate-spin rounded-full border-2 border-primary border-t-transparent"></div>
            </div>
          ) : filteredUsers.length === 0 ? (
            <div className="p-3 text-center text-sm text-muted-foreground">
              {searchQuery ? "No users found" : "No users available"}
            </div>
          ) : (
            filteredUsers.map((u) => (
              <button
                key={u.id}
                className={`flex w-full items-center justify-between p-2 text-left hover:bg-accent ${
                  selectedUsers.some((selected) => selected.id === u.id) ? "bg-accent" : ""
                }`}
                onClick={() => toggleUserSelection(u)}
              >
                <div>
                  <div className="text-sm font-medium">{u.name}</div>
                  <div className="text-xs text-muted-foreground">{u.email}</div>
                </div>
                {selectedUsers.some((selected) => selected.id === u.id) && (
                  <Check className="h-4 w-4 text-primary" />
                )}
              </button>
            ))
          )}
        </div>

        <Button
          className="w-full"
          onClick={createConversation}
          disabled={selectedUsers.length === 0 || loading}
        >
          {loading ? (
            <>
              <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent"></div>
              Creating...
            </>
          ) : (
            "Start Conversation"
          )}
        </Button>
      </div>
    </div>
  );
}
