"use client";

import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Target, TrendingUp, TrendingDown, Clock, RefreshCw } from "lucide-react";
import { formatCurrency } from "@/lib/utils";
import { cn } from "@/lib/utils";

interface DailySalesTargetProps {
  className?: string;
}

interface TargetData {
  id: string | null;
  name: string;
  description: string;
  targetType: string;
  startDate: string;
  endDate: string;
  amount: number;
  isActive: boolean;
  isDefault?: boolean;
  actualRevenue: number;
  progress: number;
  remainingAmount: number;
  timeProgress: number;
  status: "on-track" | "behind" | "ahead" | "critical";
}

export function DailySalesTargetChart({ className }: DailySalesTargetProps) {
  const [targetData, setTargetData] = useState<TargetData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [lastUpdated, setLastUpdated] = useState<Date>(new Date());

  const fetchTargetData = async () => {
    try {
      setIsLoading(true);
      setError(null);

      // Get today's date range
      const today = new Date();
      const startOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate());
      const endOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate(), 23, 59, 59);

      // Fetch current daily target
      const targetResponse = await fetch(
        `/api/revenue-targets/current?targetType=DAILY&from=${startOfDay.toISOString()}&to=${endOfDay.toISOString()}`
      );

      if (!targetResponse.ok) {
        throw new Error("Failed to fetch target data");
      }

      const targetResult = await targetResponse.json();
      const target = targetResult.data;

      // Fetch today's actual revenue
      const revenueResponse = await fetch("/api/transactions/today");
      if (!revenueResponse.ok) {
        throw new Error("Failed to fetch revenue data");
      }

      const revenueResult = await revenueResponse.json();
      const actualRevenue = revenueResult.data?.totalRevenue || 0;

      // Calculate progress and status
      const progress = Math.min((actualRevenue / target.amount) * 100, 100);
      const remainingAmount = Math.max(target.amount - actualRevenue, 0);

      // Calculate time progress (how much of the day has passed)
      const now = new Date();
      const startOfToday = new Date(now.getFullYear(), now.getMonth(), now.getDate());
      const endOfToday = new Date(now.getFullYear(), now.getMonth(), now.getDate(), 23, 59, 59);
      const timeElapsed = now.getTime() - startOfToday.getTime();
      const totalDayTime = endOfToday.getTime() - startOfToday.getTime();
      const timeProgress = Math.min((timeElapsed / totalDayTime) * 100, 100);

      // Determine status based on progress vs time
      let status: "on-track" | "behind" | "ahead" | "critical";
      if (progress >= 100) {
        status = "ahead";
      } else if (progress >= timeProgress * 0.8) {
        status = "on-track";
      } else if (progress >= timeProgress * 0.5) {
        status = "behind";
      } else {
        status = "critical";
      }

      setTargetData({
        ...target,
        actualRevenue,
        progress,
        remainingAmount,
        timeProgress,
        status,
      });

      setLastUpdated(new Date());
    } catch (err) {
      console.error("Error fetching target data:", err);
      setError(err instanceof Error ? err.message : "Failed to load target data");
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchTargetData();

    // Refresh every 5 minutes
    const interval = setInterval(fetchTargetData, 5 * 60 * 1000);
    return () => clearInterval(interval);
  }, []);

  const getStatusColor = (status: string) => {
    switch (status) {
      case "ahead":
        return "text-green-600";
      case "on-track":
        return "text-blue-600";
      case "behind":
        return "text-yellow-600";
      case "critical":
        return "text-red-600";
      default:
        return "text-gray-600";
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "ahead":
        return <TrendingUp className="h-4 w-4 text-green-600" />;
      case "on-track":
        return <Target className="h-4 w-4 text-blue-600" />;
      case "behind":
        return <TrendingDown className="h-4 w-4 text-yellow-600" />;
      case "critical":
        return <TrendingDown className="h-4 w-4 text-red-600" />;
      default:
        return <Target className="h-4 w-4 text-gray-600" />;
    }
  };

  const getProgressColor = (status: string) => {
    switch (status) {
      case "ahead":
        return "bg-green-500";
      case "on-track":
        return "bg-blue-500";
      case "behind":
        return "bg-yellow-500";
      case "critical":
        return "bg-red-500";
      default:
        return "bg-gray-500";
    }
  };

  if (isLoading) {
    return (
      <Card className={cn("col-span-1", className)}>
        <CardHeader className="flex flex-row items-center justify-between pb-2">
          <CardTitle className="text-sm font-medium">Daily Sales Targetz</CardTitle>
          <Target className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="animate-pulse">
            <div className="h-8 bg-gray-200 rounded mb-2"></div>
            <div className="h-4 bg-gray-200 rounded mb-4"></div>
            <div className="h-2 bg-gray-200 rounded mb-4"></div>
            <div className="h-16 bg-gray-200 rounded"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error || !targetData) {
    return (
      <Card className={cn("col-span-1", className)}>
        <CardHeader className="flex flex-row items-center justify-between pb-2">
          <CardTitle className="text-sm font-medium">Daily Sales Target</CardTitle>
          <Target className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-center py-4">
            <p className="text-sm text-red-600 mb-2">{error || "No target data available"}</p>
            <button
              onClick={fetchTargetData}
              className="text-xs text-blue-600 hover:text-blue-800 flex items-center gap-1 mx-auto"
            >
              <RefreshCw className="h-3 w-3" />
              Retry
            </button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={cn("col-span-1", className)}>
      <CardHeader className="flex flex-row items-center justify-between pb-2">
        <CardTitle className="text-m font-bold">Daily Sales Target</CardTitle>
        <div className="flex items-center gap-2">
          {getStatusIcon(targetData.status)}
          <button
            onClick={fetchTargetData}
            className="text-muted-foreground hover:text-foreground"
            title="Refresh"
          >
            <RefreshCw className="h-3 w-3" />
          </button>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {/* Current Progress */}
          <div>
            <div className="flex items-center justify-between mb-2">
              <span className="text-2xl font-bold">{formatCurrency(targetData.actualRevenue)}</span>
              <span className={cn("text-2xl font-bold ", getStatusColor(targetData.status))}>
                {targetData.progress.toFixed(1)}%
              </span>
            </div>
            <div className="text-xs text-muted-foreground mb-3">
              of {formatCurrency(targetData.amount)} target
            </div>

            {/* Progress Bar */}
            <div className="space-y-2">
              <Progress value={targetData.progress} className="h-3" />

              {/* Time Progress Indicator */}
              <div className="flex items-center gap-2 text-xs text-muted-foreground">
                <Clock className="h-3 w-3" />
                <span>{targetData.timeProgress.toFixed(0)}% of day elapsed</span>
              </div>
            </div>
          </div>

          {/* Status and Remaining */}
          <div className="pt-2 border-t">
            <div className="flex items-center justify-between text-xs">
              <span className="text-muted-foreground mb-2">Remaining:</span>
              <span className="font-medium">{formatCurrency(targetData.remainingAmount)}</span>
            </div>
            <div className="flex items-center justify-between text-xs mt-1">
              <span className="text-muted-foreground">Status:</span>
              <span className={cn("font-medium capitalize", getStatusColor(targetData.status))}>
                {targetData.status.replace("-", " ")}
              </span>
            </div>
            {targetData.isDefault && (
              <div className="text-xs text-muted-foreground mt-4">* Auto-calculated target</div>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
