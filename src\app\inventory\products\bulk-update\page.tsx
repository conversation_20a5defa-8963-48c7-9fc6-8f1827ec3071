"use client";

import { useState, useEffect } from "react";
import { MainLayout } from "@/components/layout/MainLayout";
import { PageHeader } from "@/components/layout/PageHeader";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Separator } from "@/components/ui/separator";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { ArrowLeft, Loader2 } from "lucide-react";
import Link from "next/link";
import { formatCurrency } from "@/lib/utils";

interface Product {
  id: string;
  name: string;
  sku: string;
  basePrice: number;
  optionalPrice1?: number | null;
  optionalPrice2?: number | null;
  purchasePrice?: number | null;
  active: boolean;
  category?: {
    id: string;
    name: string;
  } | null;
  temporaryPrice?: {
    id: string;
    value: number;
    type: "FIXED" | "PERCENTAGE";
    startDate: string;
    endDate: string;
  } | null;
}

interface Category {
  id: string;
  name: string;
  _count?: {
    products: number;
  };
}

export default function BulkUpdatePage() {
  const [products, setProducts] = useState<Product[]>([]);
  const [filteredProducts, setFilteredProducts] = useState<Product[]>([]);
  const [selectedProducts, setSelectedProducts] = useState<string[]>([]);
  const [selectedProductsDetails, setSelectedProductsDetails] = useState<Product[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState("");

  // Bulk update form state
  const [updateType, setUpdateType] = useState<"FIXED" | "PERCENTAGE">("FIXED");
  const [updateField, setUpdateField] = useState<"basePrice" | "optionalPrice1" | "optionalPrice2">(
    "basePrice"
  );
  const [value, setValue] = useState<number>(0);
  const [operation, setOperation] = useState<"INCREASE" | "DECREASE" | "SET">("INCREASE");
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
  const [updateMode, setUpdateMode] = useState<"PRODUCTS" | "CATEGORY">("PRODUCTS");

  // Fetch products and categories on component mount
  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      setError(null);

      try {
        // Fetch products
        const productsResponse = await fetch("/api/products?limit=500&active=true");

        if (!productsResponse.ok) {
          throw new Error("Failed to fetch products");
        }

        const productsData = await productsResponse.json();

        // Filter out products with temporary prices
        const productsWithoutTemporaryPrices = productsData.products.filter(
          (product: Product) => !product.temporaryPrice
        );

        setProducts(productsWithoutTemporaryPrices);
        setFilteredProducts(productsWithoutTemporaryPrices);

        // Fetch categories
        const categoriesResponse = await fetch("/api/categories?withProductCount=true");

        if (!categoriesResponse.ok) {
          throw new Error("Failed to fetch categories");
        }

        const categoriesData = await categoriesResponse.json();
        setCategories(categoriesData.categories);
      } catch (error) {
        console.error("Error fetching data:", error);
        setError("Failed to load data. Please try again.");
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  // Filter products based on search term
  useEffect(() => {
    if (searchTerm.trim() === "") {
      setFilteredProducts(products);
    } else {
      const term = searchTerm.toLowerCase();
      const filtered = products.filter(
        (product) =>
          product.name.toLowerCase().includes(term) ||
          product.sku.toLowerCase().includes(term) ||
          product.category?.name.toLowerCase().includes(term)
      );
      setFilteredProducts(filtered);
    }
  }, [searchTerm, products]);

  // Update selected products details when selectedProducts changes
  useEffect(() => {
    const details = products.filter((product) => selectedProducts.includes(product.id));
    setSelectedProductsDetails(details);
  }, [selectedProducts, products]);

  // Handle select all checkbox for filtered products
  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      // Add all filtered products to selection
      const filteredIds = filteredProducts.map((product) => product.id);
      setSelectedProducts((prev) => {
        // Combine previous selection with new filtered products
        const combined = [...prev, ...filteredIds];
        // Remove duplicates
        return [...new Set(combined)];
      });
    } else {
      // Remove all filtered products from selection
      const filteredIds = new Set(filteredProducts.map((product) => product.id));
      setSelectedProducts((prev) => prev.filter((id) => !filteredIds.has(id)));
    }
  };

  // Handle individual product selection
  const handleSelectProduct = (productId: string, checked: boolean) => {
    if (checked) {
      setSelectedProducts((prev) => [...prev, productId]);
    } else {
      setSelectedProducts((prev) => prev.filter((id) => id !== productId));
    }
  };

  // Handle removing a product from the selected list
  const handleRemoveSelected = (productId: string) => {
    setSelectedProducts((prev) => prev.filter((id) => id !== productId));
  };

  // Clear all selected products
  const handleClearSelected = () => {
    setSelectedProducts([]);
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (updateMode === "PRODUCTS" && selectedProducts.length === 0) {
      setError("Please select at least one product");
      return;
    }

    if (updateMode === "CATEGORY" && !selectedCategory) {
      setError("Please select a category");
      return;
    }

    setSubmitting(true);
    setError(null);
    setSuccess(null);

    try {
      // Prepare request body based on update mode
      const requestBody = {
        updateType,
        updateField,
        value,
        operation,
      };

      // Add either productIds or categoryId based on update mode
      if (updateMode === "PRODUCTS") {
        Object.assign(requestBody, { productIds: selectedProducts });
      } else {
        Object.assign(requestBody, { categoryId: selectedCategory });
      }

      const response = await fetch("/api/products/bulk-update", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(requestBody),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to update products");
      }

      const data = await response.json();

      // Success message based on update mode
      if (updateMode === "PRODUCTS") {
        setSuccess(`Successfully updated ${selectedProducts.length} products`);
      } else {
        const category = categories.find((c) => c.id === selectedCategory);
        setSuccess(`Successfully updated products in category "${category?.name}"`);
      }

      // Refresh product list
      const productsResponse = await fetch("/api/products?limit=500&active=true");
      const productsData = await productsResponse.json();

      // Filter out products with temporary prices
      const productsWithoutTemporaryPrices = productsData.products.filter(
        (product: Product) => !product.temporaryPrice
      );

      setProducts(productsWithoutTemporaryPrices);
      setFilteredProducts(productsWithoutTemporaryPrices);
    } catch (error) {
      console.error("Error updating products:", error);
      setError((error as Error).message || "Failed to update products");
    } finally {
      setSubmitting(false);
    }
  };

  return (
    <MainLayout>
      <PageHeader
        title="Bulk Price Update"
        description="Update prices for multiple products at once"
        actions={
          <Button variant="outline" asChild>
            <Link href="/inventory/products">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Products
            </Link>
          </Button>
        }
      />

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mt-6">
        {/* Update Form */}
        <Card className="md:col-span-1">
          <CardContent className="p-6">
            <form onSubmit={handleSubmit} className="space-y-4">
              {/* Update Mode Selection */}
              <div className="space-y-2">
                <Label htmlFor="updateMode">Update Mode</Label>
                <Select value={updateMode} onValueChange={(value: any) => setUpdateMode(value)}>
                  <SelectTrigger id="updateMode">
                    <SelectValue placeholder="Select update mode" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="PRODUCTS">Selected Products</SelectItem>
                    <SelectItem value="CATEGORY">By Category</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Category Selection (only shown in category mode) */}
              {updateMode === "CATEGORY" && (
                <div className="space-y-2">
                  <Label htmlFor="categorySelect">Select Category</Label>
                  <Select
                    value={selectedCategory || ""}
                    onValueChange={(value) => setSelectedCategory(value)}
                  >
                    <SelectTrigger id="categorySelect">
                      <SelectValue placeholder="Select a category" />
                    </SelectTrigger>
                    <SelectContent>
                      {categories.map((category) => (
                        <SelectItem key={category.id} value={category.id}>
                          {category.name} ({category._count?.products || 0} products)
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              )}

              <div className="space-y-2">
                <Label htmlFor="updateField">Price Field to Update</Label>
                <Select value={updateField} onValueChange={(value: any) => setUpdateField(value)}>
                  <SelectTrigger id="updateField">
                    <SelectValue placeholder="Select field" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="basePrice">Base Price</SelectItem>
                    <SelectItem value="optionalPrice1">Optional Price 1</SelectItem>
                    <SelectItem value="optionalPrice2">Optional Price 2</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="operation">Operation</Label>
                <Select value={operation} onValueChange={(value: any) => setOperation(value)}>
                  <SelectTrigger id="operation">
                    <SelectValue placeholder="Select operation" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="INCREASE">Increase by</SelectItem>
                    <SelectItem value="DECREASE">Decrease by</SelectItem>
                    <SelectItem value="SET">Set to</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="updateType">Update Type</Label>
                <Select value={updateType} onValueChange={(value: any) => setUpdateType(value)}>
                  <SelectTrigger id="updateType">
                    <SelectValue placeholder="Select type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="FIXED">Fixed Amount</SelectItem>
                    <SelectItem value="PERCENTAGE">Percentage (%)</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="value">Value</Label>
                <Input
                  id="value"
                  type="number"
                  value={value}
                  onChange={(e) => setValue(parseFloat(e.target.value) || 0)}
                  min={0}
                  step={updateType === "PERCENTAGE" ? 0.1 : 1}
                />
              </div>

              {error && (
                <Alert variant="destructive">
                  <AlertDescription>{error}</AlertDescription>
                </Alert>
              )}

              {success && (
                <Alert variant="default" className="bg-green-50 text-green-800 border-green-200">
                  <AlertDescription>{success}</AlertDescription>
                </Alert>
              )}

              <Button
                type="submit"
                className="w-full"
                disabled={
                  submitting ||
                  (updateMode === "PRODUCTS" && selectedProducts.length === 0) ||
                  (updateMode === "CATEGORY" && !selectedCategory)
                }
              >
                {submitting ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Updating...
                  </>
                ) : updateMode === "PRODUCTS" ? (
                  `Update ${selectedProducts.length} Products`
                ) : (
                  `Update Products in Category`
                )}
              </Button>
            </form>
          </CardContent>
        </Card>

        {/* Product Selection (only shown in products mode) */}
        {updateMode === "PRODUCTS" && (
          <Card className="md:col-span-2">
            <CardContent className="p-6">
              <div className="flex flex-col space-y-4">
                {/* Search and Selected Count */}
                <div className="flex flex-col md:flex-row md:items-center justify-between gap-4">
                  <div className="flex-1">
                    <Input
                      placeholder="Search products by name, SKU or category..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="w-full"
                    />
                  </div>
                  <div className="flex items-center gap-2">
                    <span className="text-sm text-muted-foreground">
                      {selectedProducts.length} selected
                    </span>
                    {selectedProducts.length > 0 && (
                      <Button variant="outline" size="sm" onClick={handleClearSelected}>
                        Clear
                      </Button>
                    )}
                  </div>
                </div>

                {/* Selected Products List */}
                {selectedProducts.length > 0 && (
                  <div className="mt-4">
                    <h4 className="text-sm font-medium mb-2">Selected Products:</h4>
                    <div className="flex flex-wrap gap-2 mb-4">
                      {selectedProductsDetails.map((product) => (
                        <div
                          key={`selected-${product.id}`}
                          className="flex items-center gap-1 bg-muted px-2 py-1 rounded-md text-sm"
                        >
                          <span>{product.name}</span>
                          <button
                            onClick={() => handleRemoveSelected(product.id)}
                            className="text-muted-foreground hover:text-destructive"
                          >
                            ×
                          </button>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                <Separator className="my-2" />

                {/* Product List with Checkboxes */}
                <div className="flex flex-col space-y-2">
                  <div className="flex items-center justify-between">
                    <h3 className="text-lg font-medium">Available Products</h3>
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="selectAll"
                        checked={
                          filteredProducts.length > 0 &&
                          filteredProducts.every((p) => selectedProducts.includes(p.id))
                        }
                        onCheckedChange={handleSelectAll}
                      />
                      <Label htmlFor="selectAll">Select All</Label>
                    </div>
                  </div>
                  <p className="text-sm text-muted-foreground italic">
                    Note: Products with temporary discounts are not available for bulk price updates
                  </p>
                </div>

                {loading ? (
                  <div className="flex justify-center items-center p-12">
                    <Loader2 className="h-8 w-8 animate-spin text-primary" />
                    <span className="ml-2">Loading products...</span>
                  </div>
                ) : filteredProducts.length === 0 ? (
                  <div className="text-center p-12">
                    <p className="text-muted-foreground">No products found matching your search</p>
                  </div>
                ) : (
                  <div className="space-y-2 max-h-[400px] overflow-y-auto pr-2 mt-4">
                    {filteredProducts.map((product) => (
                      <div
                        key={product.id}
                        className="flex items-center justify-between p-3 border rounded-md hover:bg-muted/50"
                      >
                        <div className="flex items-center space-x-3">
                          <Checkbox
                            id={`product-${product.id}`}
                            checked={selectedProducts.includes(product.id)}
                            onCheckedChange={(checked) =>
                              handleSelectProduct(product.id, !!checked)
                            }
                          />
                          <div>
                            <p className="font-medium">{product.name}</p>
                            <p className="text-sm text-muted-foreground">SKU: {product.sku}</p>
                          </div>
                        </div>
                        <div className="text-right">
                          <p className="font-medium">{formatCurrency(Number(product.basePrice))}</p>
                          <p className="text-sm text-muted-foreground">
                            {product.category?.name || "Uncategorized"}
                          </p>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </MainLayout>
  );
}
