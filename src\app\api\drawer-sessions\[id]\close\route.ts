import { NextRequest, NextResponse } from "next/server";
import { z } from "zod";
import { PrismaClient } from "@/generated/prisma";
import { verifyAuthToken } from "@/lib/auth-utils";


const prisma = new PrismaClient();

// Large discrepancy threshold (IDR 50,000)
const LARGE_DISCREPANCY_THRESHOLD = 50000;

// Drawer session close schema for validation
const closeSessionSchema = z.object({
  actualClosingBalance: z.number().min(0),
  notes: z.string().optional(),
  discrepancyCategory: z.enum([
    "COUNTING_ERROR",
    "SYSTEM_ERROR",
    "THEFT_SUSPECTED",
    "CASH_SHORTAGE",
    "CASH_SURPLUS",
    "REGISTER_ERROR",
    "TRAINING_ERROR",
    "PROCEDURAL_ERROR",
    "UNKNOWN",
    "OTHER"
  ]).optional(),
  reAuthPassword: z.string().optional(),
});

// POST /api/drawer-sessions/[id]/close - Close a drawer session
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;

    // Basic authentication for drawer close
    const auth = await verifyAuthToken(request);

    if (!auth.authenticated) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      );
    }

    // Parse and validate request body
    const body = await request.json();
    const validatedData = closeSessionSchema.parse(body);

    // Get drawer session
    const session = await prisma.drawerSession.findUnique({
      where: { id },
      include: {
        drawer: true,
        user: {
          select: {
            id: true,
            name: true,
          },
        },
      },
    });

    if (!session) {
      return NextResponse.json(
        { error: "Drawer session not found" },
        { status: 404 }
      );
    }

    // Check if session is already closed
    if (session.status !== "OPEN") {
      return NextResponse.json(
        { error: "Drawer session is already closed" },
        { status: 400 }
      );
    }

    // Only the user who opened the session or a finance admin/super admin can close it
    const hasPermission =
      session.userId === auth.user.id ||
      ["SUPER_ADMIN", "FINANCE_ADMIN"].includes(auth.user.role);

    if (!hasPermission) {
      return NextResponse.json(
        { error: "Unauthorized - You cannot close this drawer session" },
        { status: 403 }
      );
    }

    // Calculate expected closing balance
    const transactions = await prisma.transaction.findMany({
      where: {
        drawerSessionId: session.id,
        paymentMethod: "CASH",
        status: {
          not: "VOIDED",
        },
      },
      select: {
        total: true,
      },
    });

    const totalCashSales = transactions.reduce((sum, transaction) => sum + Number(transaction.total), 0);
    const expectedClosingBalance = Number(session.openingBalance) + totalCashSales;
    const discrepancy = validatedData.actualClosingBalance - expectedClosingBalance;
    const isLargeDiscrepancy = Math.abs(discrepancy) >= LARGE_DISCREPANCY_THRESHOLD;

    // For large discrepancies, require re-authentication
    if (isLargeDiscrepancy && !validatedData.reAuthPassword) {
      return NextResponse.json(
        {
          error: "Large discrepancy detected",
          requiresReAuth: true,
          discrepancy,
          threshold: LARGE_DISCREPANCY_THRESHOLD,
          message: `Discrepancy of Rp ${Math.abs(discrepancy).toLocaleString("id-ID")} exceeds threshold. Please re-enter your password to confirm.`
        },
        { status: 400 }
      );
    }

    // Verify password for large discrepancies
    if (isLargeDiscrepancy && validatedData.reAuthPassword) {
      const bcrypt = require("bcryptjs");
      const user = await prisma.user.findUnique({
        where: { id: auth.user.id },
        select: { password: true },
      });

      if (!user?.password || !await bcrypt.compare(validatedData.reAuthPassword, user.password)) {
        return NextResponse.json(
          { error: "Invalid password for large discrepancy confirmation" },
          { status: 401 }
        );
      }
    }

    // Close drawer session
    const updatedSession = await prisma.drawerSession.update({
      where: { id },
      data: {
        status: "CLOSED",
        closedAt: new Date(),
        expectedClosingBalance,
        actualClosingBalance: validatedData.actualClosingBalance,
        discrepancy,
        notes: validatedData.notes,
      },
      include: {
        drawer: true,
        user: {
          select: {
            id: true,
            name: true,
          },
        },
      },
    });

    // Create cash reconciliation record with enhanced tracking
    const reconciliation = await prisma.cashReconciliation.create({
      data: {
        userId: auth.user.id,
        businessDate: session.businessDate,
        openingBalance: session.openingBalance,
        expectedAmount: expectedClosingBalance,
        actualAmount: validatedData.actualClosingBalance,
        discrepancy,
        notes: validatedData.notes,
        status: "COMPLETED",
        discrepancyCategory: validatedData.discrepancyCategory,
        resolutionStatus: discrepancy === 0 ? "RESOLVED" : "PENDING",
      },
    });

    // Create audit alert for large discrepancies
    if (isLargeDiscrepancy) {
      await prisma.cashAuditAlert.create({
        data: {
          cashReconciliationId: reconciliation.id,
          alertType: "LARGE_DISCREPANCY",
          severity: Math.abs(discrepancy) >= 100000 ? "CRITICAL" : "HIGH",
          message: `Large discrepancy detected: Rp ${Math.abs(discrepancy).toLocaleString("id-ID")} ${discrepancy > 0 ? "surplus" : "shortage"}`,
          threshold: LARGE_DISCREPANCY_THRESHOLD,
          actualValue: Math.abs(discrepancy),
        },
      });
    }



    // Log activity with enhanced details
    await prisma.activityLog.create({
      data: {
        userId: auth.user.id,
        action: "CLOSE_DRAWER_SESSION",
        details: `Closed drawer session for ${session.drawer.name} with ${
          discrepancy === 0
            ? "no discrepancy"
            : discrepancy > 0
              ? `surplus of Rp ${discrepancy.toLocaleString("id-ID")}`
              : `shortage of Rp ${Math.abs(discrepancy).toLocaleString("id-ID")}`
        }`,
      },
    });



    return NextResponse.json({
      session: updatedSession,
      reconciliation,
      totalCashSales,
      transactionCount: transactions.length,
      securityInfo: {
        isLargeDiscrepancy,
        reAuthRequired: isLargeDiscrepancy,
        threshold: LARGE_DISCREPANCY_THRESHOLD,
      },
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Validation error", details: error.errors },
        { status: 400 }
      );
    }

    console.error("Error closing drawer session:", error);
    return NextResponse.json(
      { error: "Failed to close drawer session", message: (error as Error).message },
      { status: 500 }
    );
  }
}
