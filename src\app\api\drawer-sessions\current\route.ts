import { NextRequest, NextResponse } from "next/server";
import { PrismaClient } from "@/generated/prisma";
import { verifyAuthToken } from "@/lib/auth-utils";

const prisma = new PrismaClient();

// GET /api/drawer-sessions/current - Get current user's active drawer session
export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const auth = await verifyAuthToken(request);

    if (!auth.authenticated) {
      return NextResponse.json(
        { error: auth.error },
        { status: auth.status }
      );
    }

    // Check if user has permission to access drawer sessions
    const allowedRoles = ["CASHIER", "SUPER_ADMIN", "FINANCE_ADMIN"];
    if (!allowedRoles.includes(auth.user.role)) {
      return NextResponse.json(
        { error: "Unauthorized - Insufficient permissions to access drawer sessions" },
        { status: 403 }
      );
    }

    // Get current user's active drawer session
    const session = await prisma.drawerSession.findFirst({
      where: {
        userId: auth.user.id,
        status: "OPEN",
      },
      include: {
        drawer: true,
        terminal: true,
        user: {
          select: {
            id: true,
            name: true,
          },
        },
        _count: {
          select: {
            transactions: true,
          },
        },
      },
    });

    // If no active session, return null
    if (!session) {
      return NextResponse.json({ session: null });
    }

    // Calculate expected closing balance
    const transactions = await prisma.transaction.findMany({
      where: {
        drawerSessionId: session.id,
        paymentMethod: "CASH",
        status: {
          not: "VOIDED",
        },
      },
      select: {
        total: true,
      },
    });

    const totalCashSales = transactions.reduce((sum, transaction) => sum + Number(transaction.total), 0);
    const expectedClosingBalance = Number(session.openingBalance) + totalCashSales;

    return NextResponse.json({
      session: {
        ...session,
        expectedClosingBalance,
        totalCashSales,
        transactionCount: transactions.length,
      },
    });
  } catch (error) {
    console.error("Error fetching current drawer session:", error);
    return NextResponse.json(
      { error: "Failed to fetch current drawer session", message: (error as Error).message },
      { status: 500 }
    );
  }
}
