import { NextRequest, NextResponse } from 'next/server';
import { PrismaClient } from '@/generated/prisma';
import { verifyAuthToken } from '@/lib/auth-utils';
import { z } from 'zod';

const prisma = new PrismaClient();

const customerResolutionSchema = z.object({
  resolution: z.enum(['REPLACEMENT', 'REFUND', 'PENDING_REPLACEMENT']),
  notes: z.string().optional(),
});

// POST /api/returns/[id]/customer-resolution - Process customer resolution for completed return
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;

    const auth = await verifyAuthToken(request);
    if (!auth.authenticated || !auth.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = ['SUPER_ADMIN', 'WAREHOUSE_ADMIN', 'FINANCE_ADMIN'].includes(auth.user.role);
    if (!hasPermission) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 });
    }

    const body = await request.json();
    const { resolution, notes } = customerResolutionSchema.parse(body);

    // Check if return exists and is completed
    const existingReturn = await prisma.return.findUnique({
      where: { id },
      include: {
        transaction: {
          include: {
            items: true,
          },
        },
        items: {
          include: {
            product: {
              include: {
                storeStock: true,
                unit: true,
              },
            },
          },
        },
      },
    });

    if (!existingReturn) {
      return NextResponse.json({ error: 'Return not found' }, { status: 404 });
    }

    if (existingReturn.status !== 'COMPLETED') {
      return NextResponse.json({ 
        error: 'Can only process customer resolution for completed returns' 
      }, { status: 400 });
    }

    if (existingReturn.customerResolution && existingReturn.customerResolution !== 'PENDING_REPLACEMENT') {
      return NextResponse.json({
        error: 'Customer resolution has already been processed for this return'
      }, { status: 400 });
    }

    const result = await prisma.$transaction(async (tx) => {
      if (resolution === 'REPLACEMENT') {
        // Validate stock availability before processing replacement
        for (const item of existingReturn.items) {
          const currentStock = item.product.storeStock?.quantity || 0;
          if (Number(currentStock) < Number(item.quantity)) {
            throw new Error(`Insufficient stock for replacement of ${item.product.name}. Available: ${currentStock}, Required: ${item.quantity}`);
          }
        }
        // For replacement: Decrease store stock by the quantity of returned products
        for (const item of existingReturn.items) {
          // Get current store stock
          const storeStock = await tx.storeStock.findUnique({
            where: { productId: item.productId },
          });

          if (storeStock) {
            // Check if we have enough stock for replacement
            if (storeStock.quantity.lt(item.quantity)) {
              throw new Error(`Insufficient stock for replacement of ${item.product.name}. Available: ${storeStock.quantity}, Required: ${item.quantity}`);
            }

            // Update store stock (decrease for replacement)
            await tx.storeStock.update({
              where: { productId: item.productId },
              data: {
                quantity: { decrement: item.quantity },
                lastUpdated: new Date(),
              },
            });

            // Create stock adjustment record
            await tx.stockAdjustment.create({
              data: {
                productId: item.productId,
                storeStockId: storeStock.id,
                previousQuantity: storeStock.quantity,
                newQuantity: storeStock.quantity.sub(item.quantity),
                adjustmentQuantity: item.quantity.neg(),
                reason: 'RETURN',
                notes: `Customer resolution: Replacement for return #${existingReturn.id}`,
                userId: auth.user.id,
              },
            });

            // Create stock history record
            await tx.stockHistory.create({
              data: {
                productId: item.productId,
                storeStockId: storeStock.id,
                previousQuantity: storeStock.quantity,
                newQuantity: storeStock.quantity.sub(item.quantity),
                changeQuantity: item.quantity.neg(),
                source: 'RETURN',
                referenceId: existingReturn.id,
                referenceType: 'Return',
                notes: `Customer resolution: Replacement for return #${existingReturn.id}`,
                userId: auth.user.id,
              },
            });
          }
        }
      } else if (resolution === 'REFUND') {
        // For refund: Mark the original transaction as VOIDED and update payment status
        await tx.transaction.update({
          where: { id: existingReturn.transactionId },
          data: {
            status: 'VOIDED',
            paymentStatus: 'CANCELLED',
          },
        });

        // Create activity log for the voided transaction
        await tx.activityLog.create({
          data: {
            userId: auth.user.id,
            action: 'VOID_TRANSACTION_FOR_REFUND',
            details: `Voided transaction ${existingReturn.transactionId} due to customer refund for return #${existingReturn.id}`,
          },
        });
      } else if (resolution === 'PENDING_REPLACEMENT') {
        // For pending replacement: Mark as awaiting restock but don't process stock changes yet
        console.log(`[CUSTOMER_RESOLUTION] Marking return ${existingReturn.id} as pending replacement (awaiting restock)`);
      }

      // Update the return with customer resolution information
      const updatedReturn = await tx.return.update({
        where: { id },
        data: {
          customerResolution: resolution,
          customerResolutionNotes: notes,
          customerResolutionProcessedAt: new Date(),
          customerResolutionProcessedBy: auth.user.id,
          awaitingRestock: resolution === 'PENDING_REPLACEMENT',
        },
        include: {
          customer: true,
          transaction: true,
          items: {
            include: {
              product: true,
            },
          },
          customerResolutionProcessor: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
        },
      });

      // Create activity log for the customer resolution
      await tx.activityLog.create({
        data: {
          userId: auth.user.id,
          action: 'PROCESS_CUSTOMER_RESOLUTION',
          details: `Processed customer resolution (${resolution}) for return #${existingReturn.id}${notes ? `. Notes: ${notes}` : ''}`,
        },
      });

      return updatedReturn;
    });

    return NextResponse.json(result);
  } catch (error) {
    console.error('Error processing customer resolution:', error);
    if (error instanceof z.ZodError) {
      return NextResponse.json({ error: 'Validation error', details: error.errors }, { status: 400 });
    }
    return NextResponse.json({ 
      error: 'Failed to process customer resolution', 
      message: (error as Error).message 
    }, { status: 500 });
  }
}
