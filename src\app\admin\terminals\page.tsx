"use client";

import { useState, useEffect } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { toast } from "sonner";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { MainLayout } from "@/components/layout/MainLayout";
import { PageHeader } from "@/components/layout/PageHeader";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { But<PERSON> } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import {
  Loader2,
  Plus,
  Edit,
  Trash,
  Monitor,
  Link,
  Link2Off,
  AlertTriangle,
  Map,
} from "lucide-react";
import { useAuth } from "@/hooks/use-auth";

// Terminal schema for validation
const terminalSchema = z.object({
  name: z.string().min(2, { message: "Name must be at least 2 characters" }),
  ipAddress: z.string().optional(),
  macAddress: z.string().optional(),
  location: z.string().optional(),
  description: z.string().optional(),
  isActive: z.boolean().default(true),
  drawerId: z.string().optional().nullable(),
});

type TerminalFormValues = z.infer<typeof terminalSchema>;

interface Terminal {
  id: string;
  name: string;
  ipAddress?: string;
  macAddress?: string;
  location?: string;
  description?: string;
  isActive: boolean;
  drawerId?: string;
  createdAt: string;
  updatedAt: string;
  drawer?: {
    id: string;
    name: string;
    location?: string;
  };
}

interface CashDrawer {
  id: string;
  name: string;
  location?: string;
  isActive: boolean;
}

export default function TerminalsPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { user, isLoading: authLoading } = useAuth();
  const [terminals, setTerminals] = useState<Terminal[]>([]);
  const [drawers, setDrawers] = useState<CashDrawer[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [createDialogOpen, setCreateDialogOpen] = useState(false);
  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [assignDrawerDialogOpen, setAssignDrawerDialogOpen] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [selectedTerminal, setSelectedTerminal] = useState<Terminal | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Create form
  const createForm = useForm<TerminalFormValues>({
    resolver: zodResolver(terminalSchema),
    defaultValues: {
      name: "",
      ipAddress: "",
      macAddress: "",
      location: "",
      description: "",
      isActive: true,
    },
  });

  // Edit form
  const editForm = useForm<TerminalFormValues>({
    resolver: zodResolver(terminalSchema),
    defaultValues: {
      name: "",
      ipAddress: "",
      macAddress: "",
      location: "",
      description: "",
      isActive: true,
    },
  });

  // Assign drawer form
  const assignDrawerForm = useForm<{ drawerId: string | null }>({
    defaultValues: {
      drawerId: null,
    },
  });

  // Fetch terminals
  const fetchTerminals = async () => {
    setIsLoading(true);

    try {
      const response = await fetch("/api/terminals");
      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || "Failed to fetch terminals");
      }

      setTerminals(data.terminals);
    } catch (err) {
      toast.error(err instanceof Error ? err.message : "An unknown error occurred");
      console.error("Error fetching terminals:", err);
    } finally {
      setIsLoading(false);
    }
  };

  // Fetch drawers
  const fetchDrawers = async () => {
    try {
      const response = await fetch("/api/cash-drawers?isActive=true");
      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || "Failed to fetch cash drawers");
      }

      setDrawers(data.drawers);
    } catch (err) {
      toast.error(err instanceof Error ? err.message : "An unknown error occurred");
      console.error("Error fetching cash drawers:", err);
    }
  };

  // Load terminals and drawers on mount
  useEffect(() => {
    fetchTerminals();
    fetchDrawers();
  }, []);

  // Check for URL parameter to open create dialog
  useEffect(() => {
    const shouldCreate = searchParams.get("create");
    if (shouldCreate === "true") {
      setCreateDialogOpen(true);
      // Clean up the URL parameter but keep returnTo if it exists
      const newUrl = new URL(window.location.href);
      newUrl.searchParams.delete("create");
      window.history.replaceState({}, "", newUrl.toString());
    }
  }, [searchParams]);

  // Check if user has permission to manage terminals
  const canManageTerminals = user && ["SUPER_ADMIN", "FINANCE_ADMIN"].includes(user.role);

  // Handle create form submission
  const handleCreateSubmit = async (values: TerminalFormValues) => {
    setIsSubmitting(true);

    try {
      const response = await fetch("/api/terminals", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(values),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || "Failed to create terminal");
      }

      toast.success("Terminal created successfully");
      setCreateDialogOpen(false);
      fetchTerminals();
      createForm.reset();

      // Check if we should return to a specific page
      const returnTo = searchParams.get("returnTo");
      if (returnTo === "terminal-map") {
        router.push("/admin/terminal-map");
      }
    } catch (err) {
      toast.error(err instanceof Error ? err.message : "An unknown error occurred");
      console.error("Error creating terminal:", err);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle edit form submission
  const handleEditSubmit = async (values: TerminalFormValues) => {
    if (!selectedTerminal) return;

    setIsSubmitting(true);

    try {
      const response = await fetch(`/api/terminals/${selectedTerminal.id}`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(values),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || "Failed to update terminal");
      }

      toast.success("Terminal updated successfully");
      setEditDialogOpen(false);
      fetchTerminals();
    } catch (err) {
      toast.error(err instanceof Error ? err.message : "An unknown error occurred");
      console.error("Error updating terminal:", err);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle assign drawer form submission
  const handleAssignDrawerSubmit = async (values: { drawerId: string | null }) => {
    if (!selectedTerminal) return;

    setIsSubmitting(true);

    try {
      const response = await fetch(`/api/terminals/${selectedTerminal.id}/assign-drawer`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(values),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || "Failed to assign drawer");
      }

      toast.success(
        values.drawerId ? "Drawer assigned successfully" : "Drawer unassigned successfully"
      );
      setAssignDrawerDialogOpen(false);
      fetchTerminals();
    } catch (err) {
      toast.error(err instanceof Error ? err.message : "An unknown error occurred");
      console.error("Error assigning drawer:", err);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle delete terminal button click
  const handleDeleteClick = (terminal: Terminal) => {
    setSelectedTerminal(terminal);
    setDeleteDialogOpen(true);
  };

  // Handle delete terminal confirmation
  const handleDeleteConfirm = async () => {
    if (!selectedTerminal) return;

    setIsSubmitting(true);

    try {
      const response = await fetch(`/api/terminals/${selectedTerminal.id}`, {
        method: "DELETE",
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || "Failed to delete terminal");
      }

      toast.success("Terminal deleted successfully");
      setDeleteDialogOpen(false);
      fetchTerminals();
    } catch (err) {
      toast.error(err instanceof Error ? err.message : "An unknown error occurred");
      console.error("Error deleting terminal:", err);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle edit button click
  const handleEditClick = (terminal: Terminal) => {
    setSelectedTerminal(terminal);
    editForm.reset({
      name: terminal.name,
      ipAddress: terminal.ipAddress || "",
      macAddress: terminal.macAddress || "",
      location: terminal.location || "",
      description: terminal.description || "",
      isActive: terminal.isActive,
      drawerId: terminal.drawerId,
    });
    setEditDialogOpen(true);
  };

  // Handle assign drawer button click
  const handleAssignDrawerClick = (terminal: Terminal) => {
    setSelectedTerminal(terminal);
    assignDrawerForm.reset({
      drawerId: terminal.drawerId || null,
    });
    setAssignDrawerDialogOpen(true);
  };

  // Handle view terminal details
  const handleViewTerminal = (terminalId: string) => {
    router.push(`/admin/terminals/${terminalId}`);
  };

  return (
    <MainLayout>
      <PageHeader
        heading="Terminals"
        subheading="Manage terminals for your point of sale system"
        actions={
          <div className="flex gap-2">
            <Button variant="outline" onClick={() => router.push("/admin/terminal-map")}>
              <Map className="h-4 w-4 mr-2" />
              Terminal Map
            </Button>
            {canManageTerminals && (
              <Button onClick={() => setCreateDialogOpen(true)}>
                <Plus className="h-4 w-4 mr-2" />
                Add Terminal
              </Button>
            )}
          </div>
        }
      />

      <Card>
        <CardHeader>
          <CardTitle>Terminals</CardTitle>
          <CardDescription>View and manage all terminals in your system</CardDescription>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="flex justify-center items-center py-8">
              <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
            </div>
          ) : terminals.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              No terminals found. Click "Add Terminal" to create one.
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Name</TableHead>
                  <TableHead>IP Address</TableHead>
                  <TableHead>Location</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Assigned Drawer</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {terminals.map((terminal) => (
                  <TableRow key={terminal.id}>
                    <TableCell className="font-medium">{terminal.name}</TableCell>
                    <TableCell>{terminal.ipAddress || "-"}</TableCell>
                    <TableCell>{terminal.location || "-"}</TableCell>
                    <TableCell>
                      <Badge variant={terminal.isActive ? "success" : "destructive"}>
                        {terminal.isActive ? "Active" : "Inactive"}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      {terminal.drawer ? (
                        <span className="flex items-center">
                          <Link className="h-4 w-4 mr-1 text-blue-500" />
                          {terminal.drawer.name}
                        </span>
                      ) : (
                        <span className="flex items-center text-muted-foreground">
                          <Link2Off className="h-4 w-4 mr-1" />
                          Not assigned
                        </span>
                      )}
                    </TableCell>
                    <TableCell>
                      <div className="flex space-x-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleViewTerminal(terminal.id)}
                        >
                          <Monitor className="h-4 w-4" />
                        </Button>
                        {canManageTerminals && (
                          <>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleEditClick(terminal)}
                            >
                              <Edit className="h-4 w-4" />
                            </Button>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleAssignDrawerClick(terminal)}
                            >
                              {terminal.drawer ? (
                                <Link2Off className="h-4 w-4" />
                              ) : (
                                <Link className="h-4 w-4" />
                              )}
                            </Button>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleDeleteClick(terminal)}
                            >
                              <Trash className="h-4 w-4" />
                            </Button>
                          </>
                        )}
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>

      {/* Create Terminal Dialog */}
      <Dialog open={createDialogOpen} onOpenChange={setCreateDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Add Terminal</DialogTitle>
            <DialogDescription>
              Create a new terminal for your point of sale system
            </DialogDescription>
          </DialogHeader>
          <Form {...createForm}>
            <form onSubmit={createForm.handleSubmit(handleCreateSubmit)} className="space-y-4">
              <FormField
                control={createForm.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Name</FormLabel>
                    <FormControl>
                      <Input placeholder="Enter terminal name" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={createForm.control}
                name="ipAddress"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>IP Address</FormLabel>
                    <FormControl>
                      <Input placeholder="*************" {...field} value={field.value || ""} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={createForm.control}
                name="macAddress"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>MAC Address</FormLabel>
                    <FormControl>
                      <Input placeholder="00:1A:2B:3C:4D:5E" {...field} value={field.value || ""} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={createForm.control}
                name="location"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Location</FormLabel>
                    <FormControl>
                      <Input placeholder="Front counter" {...field} value={field.value || ""} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={createForm.control}
                name="description"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Description</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Additional details about this terminal"
                        {...field}
                        value={field.value || ""}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={createForm.control}
                name="isActive"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                    <FormControl>
                      <Checkbox checked={field.value} onCheckedChange={field.onChange} />
                    </FormControl>
                    <div className="space-y-1 leading-none">
                      <FormLabel>Active</FormLabel>
                      <FormDescription>
                        Inactive terminals cannot be used for transactions
                      </FormDescription>
                    </div>
                  </FormItem>
                )}
              />
              <Button type="submit" disabled={isSubmitting} className="w-full">
                {isSubmitting ? "Creating..." : "Create Terminal"}
              </Button>
            </form>
          </Form>
        </DialogContent>
      </Dialog>

      {/* Edit Terminal Dialog */}
      <Dialog open={editDialogOpen} onOpenChange={setEditDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Edit Terminal</DialogTitle>
            <DialogDescription>Update terminal information</DialogDescription>
          </DialogHeader>
          <Form {...editForm}>
            <form onSubmit={editForm.handleSubmit(handleEditSubmit)} className="space-y-4">
              <FormField
                control={editForm.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Name</FormLabel>
                    <FormControl>
                      <Input placeholder="Enter terminal name" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={editForm.control}
                name="ipAddress"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>IP Address</FormLabel>
                    <FormControl>
                      <Input placeholder="*************" {...field} value={field.value || ""} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={editForm.control}
                name="macAddress"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>MAC Address</FormLabel>
                    <FormControl>
                      <Input placeholder="00:1A:2B:3C:4D:5E" {...field} value={field.value || ""} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={editForm.control}
                name="location"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Location</FormLabel>
                    <FormControl>
                      <Input placeholder="Front counter" {...field} value={field.value || ""} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={editForm.control}
                name="description"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Description</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Additional details about this terminal"
                        {...field}
                        value={field.value || ""}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={editForm.control}
                name="isActive"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                    <FormControl>
                      <Checkbox checked={field.value} onCheckedChange={field.onChange} />
                    </FormControl>
                    <div className="space-y-1 leading-none">
                      <FormLabel>Active</FormLabel>
                      <FormDescription>
                        Inactive terminals cannot be used for transactions
                      </FormDescription>
                    </div>
                  </FormItem>
                )}
              />
              <Button type="submit" disabled={isSubmitting} className="w-full">
                {isSubmitting ? "Updating..." : "Update Terminal"}
              </Button>
            </form>
          </Form>
        </DialogContent>
      </Dialog>

      {/* Assign Drawer Dialog */}
      <Dialog open={assignDrawerDialogOpen} onOpenChange={setAssignDrawerDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Assign Drawer</DialogTitle>
            <DialogDescription>
              {selectedTerminal?.drawer
                ? `Change or remove the drawer assigned to ${selectedTerminal.name}`
                : `Assign a drawer to ${selectedTerminal?.name}`}
            </DialogDescription>
          </DialogHeader>
          <form
            onSubmit={assignDrawerForm.handleSubmit(handleAssignDrawerSubmit)}
            className="space-y-4"
          >
            <div className="space-y-2">
              <label className="text-sm font-medium">Cash Drawer</label>
              <Select
                value={assignDrawerForm.watch("drawerId") || "none"}
                onValueChange={(value) =>
                  assignDrawerForm.setValue("drawerId", value === "none" ? null : value)
                }
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select a cash drawer" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="none">None (Unassign)</SelectItem>
                  {drawers
                    .filter(
                      (drawer) =>
                        // Show unassigned drawers or the drawer currently assigned to this terminal
                        !drawer.terminal || drawer.terminal.id === selectedTerminal?.id
                    )
                    .map((drawer) => (
                      <SelectItem key={drawer.id} value={drawer.id}>
                        {drawer.name} {drawer.location ? `(${drawer.location})` : ""}
                        {drawer.terminal?.id === selectedTerminal?.id && " (Currently assigned)"}
                      </SelectItem>
                    ))}
                </SelectContent>
              </Select>
            </div>
            <Button type="submit" disabled={isSubmitting} className="w-full">
              {isSubmitting
                ? "Saving..."
                : assignDrawerForm.watch("drawerId")
                  ? "Assign Drawer"
                  : "Unassign Drawer"}
            </Button>
          </form>
        </DialogContent>
      </Dialog>

      {/* Delete Terminal Confirmation Dialog */}
      <Dialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5 text-red-600" />
              Delete Terminal
            </DialogTitle>
            <DialogDescription>
              Are you sure you want to delete terminal "{selectedTerminal?.name}"? This action
              cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <div className="flex flex-col gap-4">
            {selectedTerminal?.drawer && (
              <div className="p-3 bg-yellow-50 border border-yellow-200 rounded-md">
                <div className="flex items-center gap-2 text-yellow-800">
                  <AlertTriangle className="h-4 w-4" />
                  <span className="font-medium">Warning</span>
                </div>
                <p className="text-sm text-yellow-700 mt-1">
                  This terminal is currently assigned to drawer "{selectedTerminal.drawer.name}".
                  The drawer will be unassigned after deletion.
                </p>
              </div>
            )}
            <div className="flex justify-end gap-2">
              <Button
                variant="outline"
                onClick={() => setDeleteDialogOpen(false)}
                disabled={isSubmitting}
              >
                Cancel
              </Button>
              <Button variant="destructive" onClick={handleDeleteConfirm} disabled={isSubmitting}>
                {isSubmitting ? "Deleting..." : "Delete Terminal"}
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </MainLayout>
  );
}
