import { NextRequest } from "next/server";
import { jwtVerify } from "jose";

/**
 * Helper function to verify the authentication token
 * @param request The Next.js request object
 * @returns An object with authentication status and user information
 */
export async function verifyAuthToken(request: NextRequest) {
  // Get the session token from cookies
  const token = request.cookies.get("session-token");

  // Check if token exists
  if (!token) {
    return {
      authenticated: false,
      error: "Unauthorized - No session token",
      status: 403,
      user: null
    };
  }

  // Verify the token
  try {
    const { payload } = await jwtVerify(
      token.value,
      new TextEncoder().encode(process.env.NEXTAUTH_SECRET || "fallback-secret")
    );

    // Log the payload for debugging
    console.log("JWT payload:", payload);

    // Check if the payload has the expected structure
    // NextAuth JWT tokens typically have user info in the payload or in a sub field
    const userId = payload.sub || payload.id;
    const userRole = payload.role;

    if (!userId) {
      console.error("JWT payload missing user ID (sub or id field)");
      return {
        authenticated: false,
        error: "Unauthorized - Invalid token structure",
        status: 403,
        user: null
      };
    }

    return {
      authenticated: true,
      user: {
        id: userId as string,
        name: payload.name as string || "Unknown User",
        email: payload.email as string || "<EMAIL>",
        role: userRole as string || "CASHIER" // Default to CASHIER if role is missing
      }
    };
  } catch (error) {
    console.error("JWT verification failed:", error);
    return {
      authenticated: false,
      error: "Unauthorized - Invalid token",
      status: 403,
      user: null
    };
  }
}
