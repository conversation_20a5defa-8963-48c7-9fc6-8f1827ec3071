import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/auth";

// Helper function to verify the authentication token
async function verifyAuthToken(request: NextRequest) {
  // Get the session token from cookies
  const token = request.cookies.get("session-token");

  // Check if token exists
  if (!token) {
    console.log("[API] /api/products/temporary-prices/[id] - No session token found");
    return {
      authenticated: false,
      error: "Unauthorized - No session token",
      status: 403,
      user: null
    };
  }

  try {
    // Verify the token
    const response = await fetch(`${process.env.NEXTAUTH_URL}/api/auth/session`, {
      headers: {
        cookie: `session-token=${token.value}`,
      },
    });

    if (!response.ok) {
      console.log("[API] /api/products/temporary-prices/[id] - Invalid session token");
      return {
        authenticated: false,
        error: "Unauthorized - Invalid session token",
        status: 403,
        user: null
      };
    }

    const session = await response.json();

    if (!session.user) {
      console.log("[API] /api/products/temporary-prices/[id] - No user in session");
      return {
        authenticated: false,
        error: "Unauthorized - No user in session",
        status: 403,
        user: null
      };
    }

    return {
      authenticated: true,
      error: null,
      status: 200,
      user: session.user
    };
  } catch (error) {
    console.error("[API] /api/products/temporary-prices/[id] - Error verifying token:", error);
    return {
      authenticated: false,
      error: "Unauthorized - Error verifying token",
      status: 403,
      user: null
    };
  }
}

// DELETE /api/products/temporary-prices/[id] - Delete a temporary price
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication
    const auth = await verifyAuthToken(request);

    if (!auth.authenticated) {
      return NextResponse.json(
        { error: auth.error },
        { status: auth.status }
      );
    }

    // Check if user has permission to delete temporary prices
    const hasPermission = ["SUPER_ADMIN", "WAREHOUSE_ADMIN", "DEVELOPER"].includes(auth.user.role);
    if (!hasPermission) {
      return NextResponse.json(
        { error: "Unauthorized - Insufficient permissions" },
        { status: 403 }
      );
    }

    const { id } = params;

    // Get the temporary price to be deleted (for logging)
    const temporaryPrice = await prisma.temporaryPrice.findUnique({
      where: { id },
      include: {
        product: true,
      },
    });

    if (!temporaryPrice) {
      return NextResponse.json(
        { error: "Temporary price not found" },
        { status: 404 }
      );
    }

    // Delete the temporary price
    await prisma.temporaryPrice.delete({
      where: { id },
    });

    // Log activity
    await prisma.activityLog.create({
      data: {
        userId: auth.user.id,
        action: "DELETE_TEMPORARY_PRICE",
        details: `Deleted temporary price for product: ${temporaryPrice.product.name} (${temporaryPrice.product.sku})`,
      },
    });

    return NextResponse.json({
      success: true,
      message: "Temporary price deleted successfully",
    });
  } catch (error) {
    console.error("[API] DELETE /api/products/temporary-prices/[id] - Error:", error);
    return NextResponse.json(
      { error: "Failed to delete temporary price" },
      { status: 500 }
    );
  }
}
