-- CreateEnum
CREATE TYPE "CustomerResolution" AS ENUM ('REPLACEMENT', 'REFUND', 'NONE');

-- AlterTable
ALTER TABLE "Return" ADD COLUMN     "addToSupplierQueue" BOOLEAN NOT NULL DEFAULT false,
ADD COLUMN     "customerResolution" "CustomerResolution",
ADD COLUMN     "customerResolutionNotes" TEXT,
ADD COLUMN     "customerResolutionProcessedAt" TIMESTAMP(3),
ADD COLUMN     "customerResolutionProcessedBy" TEXT;

-- AddForeignKey
ALTER TABLE "Return" ADD CONSTRAINT "Return_customerResolutionProcessedBy_fkey" FOREIGN KEY ("customerResolutionProcessedBy") REFERENCES "User"("id") ON DELETE SET NULL ON UPDATE CASCADE;
