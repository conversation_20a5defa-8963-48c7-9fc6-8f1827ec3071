import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/auth";
import { z } from "zod";
import { AdjustmentReason } from "@/generated/prisma";

// Helper function to verify the authentication token
async function verifyAuthToken(request: NextRequest) {
  // Get the session token from cookies
  const token = request.cookies.get("session-token");

  // Check if token exists
  if (!token) {
    console.log("[API] /api/inventory/adjustments - No session token found");
    return {
      authenticated: false,
      error: "Unauthorized - No session token",
      status: 403,
      user: null
    };
  }

  // Verify the token
  try {
    const { payload } = await import("jose").then(({ jwtVerify }) =>
      jwtVerify(
        token.value,
        new TextEncoder().encode(process.env.NEXTAUTH_SECRET || "fallback-secret")
      )
    );

    return {
      authenticated: true,
      user: {
        id: payload.id as string,
        name: payload.name as string,
        email: payload.email as string,
        role: payload.role as string
      }
    };
  } catch (error) {
    console.error("[API] /api/inventory/adjustments - JWT verification failed:", error);
    return {
      authenticated: false,
      error: "Unauthorized - Invalid token",
      status: 403,
      user: null
    };
  }
}

// GET /api/inventory/adjustments - Get all stock adjustments
export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const auth = await verifyAuthToken(request);

    if (!auth.authenticated) {
      return NextResponse.json(
        { error: auth.error },
        { status: auth.status }
      );
    }

    // Check if user has permission to view adjustments
    const hasPermission = ["SUPER_ADMIN", "WAREHOUSE_ADMIN"].includes(auth.user.role);
    if (!hasPermission) {
      return NextResponse.json(
        { error: "Unauthorized - Insufficient permissions" },
        { status: 403 }
      );
    }

    // Parse query parameters
    const searchParams = request.nextUrl.searchParams;
    const productId = searchParams.get("productId");
    const reason = searchParams.get("reason") as AdjustmentReason | null;
    const limit = parseInt(searchParams.get("limit") || "100");
    const page = parseInt(searchParams.get("page") || "1");
    const skip = (page - 1) * limit;

    // Build the query
    const query: any = {};
    
    if (productId) {
      query.productId = productId;
    }

    if (reason) {
      query.reason = reason;
    }

    // Get total count for pagination
    const totalCount = await prisma.stockAdjustment.count({
      where: query
    });

    // Get stock adjustments with product details
    const adjustments = await prisma.stockAdjustment.findMany({
      where: query,
      include: {
        product: {
          include: {
            category: true,
            unit: true
          }
        },
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            role: true
          }
        },
        storeStock: true,
        warehouseStock: true
      },
      skip,
      take: limit,
      orderBy: {
        date: "desc"
      }
    });

    return NextResponse.json({
      adjustments,
      pagination: {
        total: totalCount,
        page,
        limit,
        pages: Math.ceil(totalCount / limit)
      }
    });
  } catch (error) {
    console.error("Error fetching stock adjustments:", error);
    return NextResponse.json(
      { error: "Failed to fetch stock adjustments", message: (error as Error).message },
      { status: 500 }
    );
  }
}

// POST /api/inventory/adjustments - Create a stock adjustment
export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const auth = await verifyAuthToken(request);

    if (!auth.authenticated) {
      return NextResponse.json(
        { error: auth.error },
        { status: auth.status }
      );
    }

    // Check if user has permission to create adjustments
    const hasPermission = ["SUPER_ADMIN", "WAREHOUSE_ADMIN"].includes(auth.user.role);
    if (!hasPermission) {
      return NextResponse.json(
        { error: "Unauthorized - Insufficient permissions" },
        { status: 403 }
      );
    }

    // Get request body
    const body = await request.json();

    // Validate adjustment data
    const adjustmentSchema = z.object({
      productId: z.string(),
      locationType: z.enum(["STORE", "WAREHOUSE"]),
      adjustmentQuantity: z.number(),
      reason: z.enum([
        "INVENTORY_COUNT", 
        "DAMAGED", 
        "EXPIRED", 
        "THEFT", 
        "LOSS", 
        "RETURN", 
        "CORRECTION", 
        "OTHER"
      ]),
      notes: z.string().optional()
    });

    const validationResult = adjustmentSchema.safeParse(body);
    if (!validationResult.success) {
      return NextResponse.json(
        { error: "Validation failed", issues: validationResult.error.issues },
        { status: 400 }
      );
    }

    const { productId, locationType, adjustmentQuantity, reason, notes } = validationResult.data;

    // Check if product exists
    const product = await prisma.product.findUnique({
      where: { id: productId }
    });

    if (!product) {
      return NextResponse.json(
        { error: "Product not found" },
        { status: 404 }
      );
    }

    // Process the adjustment based on location type
    if (locationType === "STORE") {
      // Get current store stock
      const storeStock = await prisma.storeStock.findUnique({
        where: { productId }
      });

      if (!storeStock) {
        return NextResponse.json(
          { error: "Store stock not found for this product" },
          { status: 404 }
        );
      }

      const previousQuantity = Number(storeStock.quantity);
      const newQuantity = Math.max(0, previousQuantity + adjustmentQuantity);

      // Update store stock
      const updatedStoreStock = await prisma.storeStock.update({
        where: { id: storeStock.id },
        data: {
          quantity: newQuantity,
          lastUpdated: new Date()
        }
      });

      // Create adjustment record
      const adjustment = await prisma.stockAdjustment.create({
        data: {
          productId,
          storeStockId: storeStock.id,
          previousQuantity,
          newQuantity,
          adjustmentQuantity,
          reason: reason as AdjustmentReason,
          notes,
          userId: auth.user.id
        },
        include: {
          product: {
            include: {
              category: true,
              unit: true
            }
          },
          storeStock: true
        }
      });

      // Create stock history entry
      await prisma.stockHistory.create({
        data: {
          productId,
          storeStockId: storeStock.id,
          previousQuantity,
          newQuantity,
          changeQuantity: adjustmentQuantity,
          source: "ADJUSTMENT",
          referenceId: adjustment.id,
          referenceType: "StockAdjustment",
          notes: notes || `Adjustment: ${reason}`,
          userId: auth.user.id
        }
      });

      // Log activity
      await prisma.activityLog.create({
        data: {
          userId: auth.user.id,
          action: "ADJUST_STORE_STOCK",
          details: `Adjusted store stock for ${product.name} (${product.sku}): ${adjustmentQuantity > 0 ? '+' : ''}${adjustmentQuantity} - Reason: ${reason}`,
        }
      });

      return NextResponse.json({ adjustment });
    } else if (locationType === "WAREHOUSE") {
      // Get current warehouse stock
      const warehouseStock = await prisma.warehouseStock.findUnique({
        where: { productId }
      });

      if (!warehouseStock) {
        return NextResponse.json(
          { error: "Warehouse stock not found for this product" },
          { status: 404 }
        );
      }

      const previousQuantity = Number(warehouseStock.quantity);
      const newQuantity = Math.max(0, previousQuantity + adjustmentQuantity);

      // Update warehouse stock
      const updatedWarehouseStock = await prisma.warehouseStock.update({
        where: { id: warehouseStock.id },
        data: {
          quantity: newQuantity,
          lastUpdated: new Date()
        }
      });

      // Create adjustment record
      const adjustment = await prisma.stockAdjustment.create({
        data: {
          productId,
          warehouseStockId: warehouseStock.id,
          previousQuantity,
          newQuantity,
          adjustmentQuantity,
          reason: reason as AdjustmentReason,
          notes,
          userId: auth.user.id
        },
        include: {
          product: {
            include: {
              category: true,
              unit: true
            }
          },
          warehouseStock: true
        }
      });

      // Create stock history entry
      await prisma.stockHistory.create({
        data: {
          productId,
          warehouseStockId: warehouseStock.id,
          previousQuantity,
          newQuantity,
          changeQuantity: adjustmentQuantity,
          source: "ADJUSTMENT",
          referenceId: adjustment.id,
          referenceType: "StockAdjustment",
          notes: notes || `Adjustment: ${reason}`,
          userId: auth.user.id
        }
      });

      // Log activity
      await prisma.activityLog.create({
        data: {
          userId: auth.user.id,
          action: "ADJUST_WAREHOUSE_STOCK",
          details: `Adjusted warehouse stock for ${product.name} (${product.sku}): ${adjustmentQuantity > 0 ? '+' : ''}${adjustmentQuantity} - Reason: ${reason}`,
        }
      });

      return NextResponse.json({ adjustment });
    }

    return NextResponse.json(
      { error: "Invalid location type" },
      { status: 400 }
    );
  } catch (error) {
    console.error("Error creating stock adjustment:", error);
    return NextResponse.json(
      { error: "Failed to create stock adjustment", message: (error as Error).message },
      { status: 500 }
    );
  }
}
