"use client";

import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { Badge } from "@/components/custom/badge";
import { format } from "date-fns";
import { User, AlertCircle } from "lucide-react";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { useClientAuth } from "@/hooks/use-client-auth";

// Define activity log type
type ActivityLog = {
  id: string;
  userId: string;
  action: string;
  details: string | null;
  timestamp: string;
  user: {
    id: string;
    name: string;
    email: string;
    role: string;
  };
};

export function LatestActivityLogs() {
  const [logs, setLogs] = useState<ActivityLog[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { user } = useClientAuth();

  // Fetch latest activity logs
  useEffect(() => {
    // Only fetch logs if user is a super admin
    if (user?.role === "SUPER_ADMIN") {
      const fetchLatestLogs = async () => {
        try {
          setLoading(true);
          setError(null);

          const response = await fetch("/api/activity-logs/latest");

          if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.error || "Failed to fetch latest activity logs");
          }

          const data = await response.json();
          setLogs(data.logs);
        } catch (err: any) {
          setError(err.message || "An error occurred while fetching activity logs");
          console.error("Error fetching latest activity logs:", err);
        } finally {
          setLoading(false);
        }
      };

      fetchLatestLogs();
    }
  }, [user]);

  // Get action badge color
  const getActionBadgeVariant = (action: string) => {
    switch (action) {
      case "LOGIN":
        return "default";
      case "LOGOUT":
        return "secondary";
      case "CREATE_USER":
        return "success";
      case "UPDATE_USER":
        return "warning";
      case "DEACTIVATE_USER":
        return "destructive";
      case "ACTIVATE_USER":
        return "success";
      case "CREATE_BACKUP":
        return "default";
      case "RESTORE_BACKUP":
        return "warning";
      case "DELETE_BACKUP":
        return "destructive";
      case "FLUSH_CONVERSATIONS":
        return "destructive";
      case "STAR_CONVERSATION":
        return "success";
      case "UNSTAR_CONVERSATION":
        return "secondary";
      default:
        return "outline";
    }
  };

  // Only render for super admin
  if (user?.role !== "SUPER_ADMIN") {
    return null;
  }

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between pb-3 border-b">
        <CardTitle className="text-base font-medium">Latest Activity Logs</CardTitle>
        <Badge variant="outline" className="ml-2">
          Super Admin Only
        </Badge>
      </CardHeader>
      <CardContent className="pt-4">
        {error && (
          <Alert variant="destructive" className="mb-4">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {loading ? (
          <div className="flex justify-center py-4">
            <div className="text-center text-sm text-muted-foreground">
              Loading activity logs...
            </div>
          </div>
        ) : logs.length === 0 ? (
          <div className="flex justify-center py-4">
            <div className="text-center text-sm text-muted-foreground">No activity logs found</div>
          </div>
        ) : (
          <div className="space-y-4">
            {logs.map((log) => (
              <div key={log.id} className="flex items-start gap-3 border-b pb-3 last:border-0">
                <div className="flex h-8 w-8 items-center justify-center rounded-full bg-primary/10">
                  <User className="h-4 w-4 text-primary" />
                </div>
                <div className="flex-1">
                  <div className="flex items-center justify-between">
                    <div className="font-medium">{log.user.name}</div>
                    <div className="text-xs text-muted-foreground">
                      {format(new Date(log.timestamp), "MMM d, HH:mm")}
                    </div>
                  </div>
                  <div className="mt-1 flex items-center gap-2">
                    <Badge variant={getActionBadgeVariant(log.action)}>
                      {log.action.replace(/_/g, " ")}
                    </Badge>
                    <div className="text-xs text-muted-foreground">{log.user.email}</div>
                  </div>
                  {log.details && (
                    <div className="mt-1 text-xs text-muted-foreground line-clamp-1">
                      {log.details}
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
