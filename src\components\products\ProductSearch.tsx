"use client";

import { useState, useEffect } from "react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Search, X } from "lucide-react";

interface Category {
  id: string;
  name: string;
}

export interface ProductSearchFilters {
  search: string;
  category: string;
  active: string;
  temporaryPrice?: string;
}

interface ProductSearchProps {
  onFilterChange: (filters: ProductSearchFilters) => void;
}

export function ProductSearch({ onFilterChange }: ProductSearchProps) {
  const [categories, setCategories] = useState<Category[]>([]);
  const [filters, setFilters] = useState<ProductSearchFilters>({
    search: "",
    category: "all",
    active: "all",
    temporaryPrice: "all",
  });

  // Fetch categories on component mount
  useEffect(() => {
    const fetchCategories = async () => {
      try {
        const response = await fetch("/api/categories");
        if (!response.ok) {
          throw new Error("Failed to fetch categories");
        }
        const data = await response.json();
        setCategories(data.categories);
      } catch (error) {
        console.error("Error fetching categories:", error);
      }
    };

    fetchCategories();
  }, []);

  // Handle category change - apply filter immediately
  const handleCategoryChange = (value: string) => {
    const newFilters = { ...filters, category: value };
    setFilters(newFilters);
    onFilterChange(newFilters);
  };

  // Handle active status change - apply filter immediately
  const handleActiveChange = (value: string) => {
    const newFilters = { ...filters, active: value };
    setFilters(newFilters);
    onFilterChange(newFilters);
  };

  // Handle temporary price filter change - apply filter immediately
  const handleTemporaryPriceChange = (value: string) => {
    const newFilters = { ...filters, temporaryPrice: value };
    setFilters(newFilters);
    onFilterChange(newFilters);
  };

  // Handle form submission for search
  const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();

    const formData = new FormData(e.currentTarget);
    const search = (formData.get("search") as string) || "";

    // Update filters and trigger filter change
    const newFilters = { ...filters, search };
    setFilters(newFilters);
    onFilterChange(newFilters);
  };

  // Handle clear filters
  const handleClear = () => {
    // Reset the form and filters
    const form = document.getElementById("product-search-form") as HTMLFormElement;
    if (form) {
      form.reset();
      const newFilters = { search: "", category: "all", active: "all", temporaryPrice: "all" };
      setFilters(newFilters);
      onFilterChange(newFilters);
    }
  };

  return (
    <form id="product-search-form" onSubmit={handleSubmit} className="space-y-4">
      <div className="relative">
        <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
        <Input
          type="search"
          name="search"
          placeholder="Search products by name, SKU, or barcode..."
          className="pl-9"
          defaultValue={filters.search}
        />
      </div>
      <div className="flex flex-col sm:flex-row sm:items-center gap-4">
        <div className="flex flex-col sm:flex-row sm:gap-2 w-full sm:w-3/4">
          <div className="w-full sm:w-1/3 mb-4 sm:mb-0">
            <Select name="category" value={filters.category} onValueChange={handleCategoryChange}>
              <SelectTrigger>
                <SelectValue placeholder="All Categories" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Categories</SelectItem>
                {categories.map((category) => (
                  <SelectItem key={category.id} value={category.id}>
                    {category.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          <div className="w-full sm:w-1/3 mb-4 sm:mb-0">
            <Select name="active" value={filters.active} onValueChange={handleActiveChange}>
              <SelectTrigger>
                <SelectValue placeholder="Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Products</SelectItem>
                <SelectItem value="true">Active Only</SelectItem>
                <SelectItem value="false">Inactive Only</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div className="w-full sm:w-1/3">
            <Select
              name="temporaryPrice"
              value={filters.temporaryPrice}
              onValueChange={handleTemporaryPriceChange}
            >
              <SelectTrigger>
                <SelectValue placeholder="Discount Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Products</SelectItem>
                <SelectItem value="true">With Temporary Discount</SelectItem>
                <SelectItem value="false">Without Temporary Discount</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
        <div className="w-full sm:w-1/4 flex justify-end gap-2">
          <Button
            type="button"
            variant="outline"
            onClick={handleClear}
            className="w-full sm:w-auto"
          >
            <X className="h-4 w-4 mr-2" />
            Clear
          </Button>
          <Button type="submit" className="w-full sm:w-auto">
            <Search className="h-4 w-4 mr-2" />
            Search
          </Button>
        </div>
      </div>
    </form>
  );
}
