"use client";

import { useState, useEffect } from "react";
import { MainLayout } from "@/components/layout/MainLayout";
import { PageHeader } from "@/components/layout/PageHeader";
import { Button } from "@/components/ui/button";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/custom/badge";
import { format, subDays, startOfMonth, endOfMonth, startOfDay, endOfDay } from "date-fns";
import { Search, User, Calendar as CalendarIcon, Clock } from "lucide-react";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Pagination } from "@/components/custom/pagination";
import { Calendar } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { <PERSON><PERSON><PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";

// Define activity log type
type ActivityLog = {
  id: string;
  userId: string;
  action: string;
  details: string | null;
  timestamp: string;
  user: {
    id: string;
    name: string;
    email: string;
    role: string;
  };
};

// Define pagination type
type Pagination = {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
};

export default function ActivityLogsPage() {
  const [logs, setLogs] = useState<ActivityLog[]>([]);
  const [pagination, setPagination] = useState<Pagination>({
    page: 1,
    limit: 20,
    total: 0,
    totalPages: 0,
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [actions, setActions] = useState<string[]>([]);

  // Filters
  const [selectedAction, setSelectedAction] = useState<string>("ALL");
  const [searchTerm, setSearchTerm] = useState<string>("");
  const [startDate, setStartDate] = useState<Date | undefined>(undefined);
  const [endDate, setEndDate] = useState<Date | undefined>(undefined);

  // Fetch activity logs
  const fetchLogs = async (page = 1) => {
    try {
      setLoading(true);
      setError(null);

      // Build query parameters
      const params = new URLSearchParams();
      params.append("page", page.toString());
      params.append("limit", pagination.limit.toString());

      if (selectedAction && selectedAction !== "ALL") {
        params.append("action", selectedAction);
        console.log("Client: Filtering by action:", selectedAction);
      }

      if (startDate) {
        const formattedStartDate = format(startDate, "yyyy-MM-dd");
        params.append("startDate", formattedStartDate);
        console.log("Client: Filtering by start date:", formattedStartDate);
      }

      if (endDate) {
        const formattedEndDate = format(endDate, "yyyy-MM-dd");
        params.append("endDate", formattedEndDate);
        console.log("Client: Filtering by end date:", formattedEndDate);
      }

      const url = `/api/activity-logs?${params.toString()}`;
      console.log("Client: Fetching logs with URL:", url);

      const response = await fetch(url);

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to fetch activity logs");
      }

      const data = await response.json();
      console.log("Client: Received logs:", data.logs.length);

      if (data.logs.length > 0) {
        console.log("Client: First log action:", data.logs[0].action);
        console.log("Client: First log timestamp:", new Date(data.logs[0].timestamp).toISOString());

        // If filtering by action, verify the actions in the results
        if (selectedAction && selectedAction !== "ALL") {
          const actionCounts = data.logs.reduce(
            (acc: Record<string, number>, log: ActivityLog) => {
              acc[log.action] = (acc[log.action] || 0) + 1;
              return acc;
            },
            {} as Record<string, number>
          );
          console.log("Client: Action counts in results:", actionCounts);
        }
      }

      setLogs(data.logs);
      setPagination(data.pagination);
      setActions(data.filters.actions);
    } catch (err: any) {
      setError(err.message || "An error occurred while fetching activity logs");
      console.error("Error fetching activity logs:", err);
    } finally {
      setLoading(false);
    }
  };

  // Load logs on component mount
  useEffect(() => {
    fetchLogs();
  }, []);

  // Handle page change
  const handlePageChange = (page: number) => {
    fetchLogs(page);
  };

  // Handle filter change
  const handleFilterChange = () => {
    fetchLogs(1);
  };

  // Reset filters
  const resetFilters = () => {
    // First reset all the state values
    setSelectedAction("ALL");
    setSearchTerm("");
    setStartDate(undefined);
    setEndDate(undefined);

    // Then explicitly fetch logs with reset parameters
    console.log("Client: Resetting all filters and fetching logs");

    // Use setTimeout to ensure state updates have been processed
    setTimeout(() => {
      const params = new URLSearchParams();
      params.append("page", "1");
      params.append("limit", pagination.limit.toString());

      // Log the reset operation
      console.log("Client: Fetching logs with reset filters");

      fetch(`/api/activity-logs?${params.toString()}`)
        .then((response) => {
          if (!response.ok) {
            throw new Error("Failed to fetch activity logs after reset");
          }
          return response.json();
        })
        .then((data) => {
          console.log("Client: Received logs after reset:", data.logs.length);
          setLogs(data.logs);
          setPagination(data.pagination);
          setActions(data.filters.actions);
          setLoading(false);
        })
        .catch((err) => {
          console.error("Error fetching logs after reset:", err);
          setError(err.message || "An error occurred while resetting filters");
          setLoading(false);
        });
    }, 0);

    // Set loading state
    setLoading(true);
  };

  // Quick date filter functions
  const setToday = () => {
    const today = new Date();
    setStartDate(startOfDay(today));
    setEndDate(endOfDay(today));
    // Apply filters automatically
    setTimeout(() => handleFilterChange(), 0);
  };

  const setLast7Days = () => {
    const today = new Date();
    setStartDate(startOfDay(subDays(today, 6)));
    setEndDate(endOfDay(today));
    // Apply filters automatically
    setTimeout(() => handleFilterChange(), 0);
  };

  const setThisMonth = () => {
    const today = new Date();
    setStartDate(startOfMonth(today));
    setEndDate(endOfMonth(today));
    // Apply filters automatically
    setTimeout(() => handleFilterChange(), 0);
  };

  // Get action badge color
  const getActionBadgeVariant = (action: string) => {
    // Create actions
    if (action.startsWith("CREATE_")) {
      return "success";
    }

    // Update actions
    if (action.startsWith("UPDATE_")) {
      return "warning";
    }

    // Delete/Deactivate actions
    if (
      action.startsWith("DELETE_") ||
      action.startsWith("DEACTIVATE_") ||
      action === "FLUSH_CONVERSATIONS"
    ) {
      return "destructive";
    }

    // Specific cases
    switch (action) {
      case "LOGIN":
        return "default";
      case "LOGOUT":
        return "secondary";
      case "ACTIVATE_USER":
        return "success";
      case "RESTORE_BACKUP":
        return "warning";
      case "STAR_CONVERSATION":
        return "success";
      case "UNSTAR_CONVERSATION":
        return "secondary";
      default:
        return "outline";
    }
  };

  // Filter logs by search term
  const filteredLogs = logs.filter((log) => {
    if (!searchTerm) return true;

    const searchLower = searchTerm.toLowerCase();
    return (
      log.user.name.toLowerCase().includes(searchLower) ||
      log.user.email.toLowerCase().includes(searchLower) ||
      (log.details && log.details.toLowerCase().includes(searchLower))
    );
  });

  return (
    <MainLayout>
      <PageHeader
        title="Activity Logs"
        description="View and monitor user activities in the system"
      />

      {error && (
        <Alert variant="destructive" className="mb-4">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      <div className="mb-6 grid grid-cols-1 gap-4 md:grid-cols-4">
        <div>
          <label className="mb-2 block text-sm font-medium">Action</label>
          <Select
            value={selectedAction}
            onValueChange={(value) => {
              setSelectedAction(value);
              // Don't auto-filter on action change
            }}
          >
            <SelectTrigger>
              <SelectValue placeholder="All actions" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="ALL">All actions</SelectItem>
              {actions.map((action) => (
                <SelectItem key={action} value={action}>
                  {action.replace(/_/g, " ")}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        <div>
          <div className="flex items-center justify-between mb-2">
            <label className="block text-sm font-medium">Start Date</label>
            <div className="flex space-x-1">
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button variant="ghost" size="icon" className="h-6 w-6" onClick={setToday}>
                      <Clock className="h-3.5 w-3.5" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent side="top">
                    <p className="text-xs">Today</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </div>
          </div>
          <Popover>
            <PopoverTrigger asChild>
              <Button variant="outline" className="w-full justify-start text-left font-normal">
                <CalendarIcon className="mr-2 h-4 w-4" />
                {startDate ? format(startDate, "PPP") : <span>Pick a date</span>}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0">
              <Calendar mode="single" selected={startDate} onSelect={setStartDate} initialFocus />
            </PopoverContent>
          </Popover>
        </div>

        <div>
          <label className="mb-2 block text-sm font-medium">End Date</label>
          <Popover>
            <PopoverTrigger asChild>
              <Button variant="outline" className="w-full justify-start text-left font-normal">
                <CalendarIcon className="mr-2 h-4 w-4" />
                {endDate ? format(endDate, "PPP") : <span>Pick a date</span>}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0">
              <Calendar
                mode="single"
                selected={endDate}
                onSelect={setEndDate}
                initialFocus
                disabled={(date) => (startDate ? date < startDate : false)}
              />
            </PopoverContent>
          </Popover>
        </div>

        <div>
          <label className="mb-2 block text-sm font-medium">Search</label>
          <div className="relative flex gap-2">
            <div className="relative flex-1">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                type="text"
                placeholder="Search by user or details"
                className="pl-9"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                onKeyDown={(e) => {
                  if (e.key === "Enter") {
                    handleFilterChange();
                  }
                }}
              />
            </div>
            <Button onClick={handleFilterChange} size="sm">
              Search
            </Button>
          </div>
        </div>
      </div>

      {/* Quick Date Filters */}
      <div className="mb-4 flex flex-wrap gap-2">
        <Button
          variant="outline"
          size="sm"
          onClick={setToday}
          className={
            startDate &&
            endDate &&
            startOfDay(startDate).getTime() === startOfDay(new Date()).getTime() &&
            endOfDay(endDate).getTime() === endOfDay(new Date()).getTime()
              ? "bg-primary text-primary-foreground hover:bg-primary/90 hover:text-primary-foreground"
              : ""
          }
        >
          Today
        </Button>
        <Button
          variant="outline"
          size="sm"
          onClick={setLast7Days}
          className={
            startDate &&
            endDate &&
            startOfDay(startDate).getTime() === startOfDay(subDays(new Date(), 6)).getTime() &&
            endOfDay(endDate).getTime() === endOfDay(new Date()).getTime()
              ? "bg-primary text-primary-foreground hover:bg-primary/90 hover:text-primary-foreground"
              : ""
          }
        >
          Last 7 Days
        </Button>
        <Button
          variant="outline"
          size="sm"
          onClick={setThisMonth}
          className={
            startDate &&
            endDate &&
            startOfDay(startDate).getTime() === startOfDay(startOfMonth(new Date())).getTime() &&
            endOfDay(endDate).getTime() === endOfDay(endOfMonth(new Date())).getTime()
              ? "bg-primary text-primary-foreground hover:bg-primary/90 hover:text-primary-foreground"
              : ""
          }
        >
          This Month
        </Button>
      </div>

      <div className="mb-4 flex flex-col gap-2 sm:flex-row sm:items-center sm:justify-between">
        <div className="flex gap-2">
          <Button onClick={handleFilterChange} className="gap-2">
            <Search className="h-4 w-4" />
            Apply Filters
          </Button>
          <Button variant="outline" size="default" onClick={resetFilters} className="gap-2">
            Reset Filters
          </Button>
        </div>
        <div className="text-sm text-muted-foreground">
          Showing {filteredLogs.length} of {pagination.total} logs
        </div>
      </div>

      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Timestamp</TableHead>
              <TableHead>User</TableHead>
              <TableHead>Action</TableHead>
              <TableHead>Details</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {loading ? (
              <TableRow>
                <TableCell colSpan={4} className="text-center py-8">
                  Loading activity logs...
                </TableCell>
              </TableRow>
            ) : filteredLogs.length === 0 ? (
              <TableRow>
                <TableCell colSpan={4} className="text-center py-8">
                  No activity logs found
                </TableCell>
              </TableRow>
            ) : (
              filteredLogs.map((log) => (
                <TableRow key={log.id}>
                  <TableCell className="whitespace-nowrap">
                    {format(new Date(log.timestamp), "MMM d, yyyy HH:mm:ss")}
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <div className="flex h-8 w-8 items-center justify-center rounded-full bg-primary/10">
                        <User className="h-4 w-4 text-primary" />
                      </div>
                      <div>
                        <div className="font-medium">{log.user.name}</div>
                        <div className="text-xs text-muted-foreground">{log.user.email}</div>
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge variant={getActionBadgeVariant(log.action)}>
                      {log.action.replace(/_/g, " ")}
                    </Badge>
                  </TableCell>
                  <TableCell>{log.details || "-"}</TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>

      {pagination.totalPages > 1 && (
        <div className="mt-4 flex justify-center">
          <Pagination
            currentPage={pagination.page}
            totalPages={pagination.totalPages}
            onPageChange={handlePageChange}
          />
        </div>
      )}
    </MainLayout>
  );
}
