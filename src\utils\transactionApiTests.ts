import { createMockAuthRequest, runApiTest, mockPrisma } from "./testUtils";
import { NextResponse } from "next/server";

// Mock API handlers for Transaction API

// Mock Sales API
const getSales = async (req: Request) => {
  return NextResponse.json({
    sales: [
      {
        id: "mock-sale-id-1",
        invoiceNumber: "INV-001",
        customerId: null,
        customerName: "Walk-in Customer",
        total: 45.97,
        discount: 0,
        tax: 4.60,
        grandTotal: 50.57,
        paymentMethod: "CASH",
        paymentStatus: "PAID",
        userId: "mock-user-id-1",
        createdAt: new Date(Date.now() - 3600000),
        user: {
          id: "mock-user-id-1",
          name: "Admin User"
        },
        items: [
          {
            id: "mock-sale-item-id-1",
            saleId: "mock-sale-id-1",
            productId: "mock-product-id-1",
            quantity: 2,
            unitPrice: 15.99,
            total: 31.98,
            product: {
              id: "mock-product-id-1",
              name: "Test Product 1",
              sku: "TEST-123"
            }
          },
          {
            id: "mock-sale-item-id-2",
            saleId: "mock-sale-id-1",
            productId: "mock-product-id-2",
            quantity: 1,
            unitPrice: 13.99,
            total: 13.99,
            product: {
              id: "mock-product-id-2",
              name: "Test Product 2",
              sku: "TEST-456"
            }
          }
        ]
      },
      {
        id: "mock-sale-id-2",
        invoiceNumber: "INV-002",
        customerId: "mock-customer-id-1",
        customerName: "John Doe",
        total: 29.99,
        discount: 3.00,
        tax: 2.70,
        grandTotal: 29.69,
        paymentMethod: "CARD",
        paymentStatus: "PAID",
        userId: "mock-user-id-2",
        createdAt: new Date(Date.now() - 86400000),
        user: {
          id: "mock-user-id-2",
          name: "Store Manager"
        },
        items: [
          {
            id: "mock-sale-item-id-3",
            saleId: "mock-sale-id-2",
            productId: "mock-product-id-1",
            quantity: 1,
            unitPrice: 15.99,
            total: 15.99,
            product: {
              id: "mock-product-id-1",
              name: "Test Product 1",
              sku: "TEST-123"
            }
          },
          {
            id: "mock-sale-item-id-4",
            saleId: "mock-sale-id-2",
            productId: "mock-product-id-3",
            quantity: 1,
            unitPrice: 14.00,
            total: 14.00,
            product: {
              id: "mock-product-id-3",
              name: "Test Product 3",
              sku: "TEST-789"
            }
          }
        ]
      }
    ],
    pagination: {
      total: 2,
      page: 1,
      limit: 10,
      pages: 1
    }
  });
};

const createSale = async (req: Request) => {
  const body = await req.json();
  return NextResponse.json({
    sale: {
      id: "mock-new-sale-id",
      invoiceNumber: "INV-003",
      customerId: body.customerId || null,
      customerName: body.customerName || "Walk-in Customer",
      total: 45.97,
      discount: body.discount || 0,
      tax: 4.60,
      grandTotal: 50.57,
      paymentMethod: body.paymentMethod || "CASH",
      paymentStatus: "PAID",
      userId: "mock-user-id-1",
      createdAt: new Date(),
      items: body.items || [
        {
          id: "mock-new-sale-item-id-1",
          saleId: "mock-new-sale-id",
          productId: "mock-product-id-1",
          quantity: 2,
          unitPrice: 15.99,
          total: 31.98
        },
        {
          id: "mock-new-sale-item-id-2",
          saleId: "mock-new-sale-id",
          productId: "mock-product-id-2",
          quantity: 1,
          unitPrice: 13.99,
          total: 13.99
        }
      ]
    }
  });
};

const getSale = async (req: Request, { params }: { params: { id: string } }) => {
  return NextResponse.json({
    sale: {
      id: params.id || "mock-sale-id",
      invoiceNumber: "INV-001",
      customerId: null,
      customerName: "Walk-in Customer",
      total: 45.97,
      discount: 0,
      tax: 4.60,
      grandTotal: 50.57,
      paymentMethod: "CASH",
      paymentStatus: "PAID",
      userId: "mock-user-id-1",
      createdAt: new Date(Date.now() - 3600000),
      user: {
        id: "mock-user-id-1",
        name: "Admin User"
      },
      items: [
        {
          id: "mock-sale-item-id-1",
          saleId: "mock-sale-id",
          productId: "mock-product-id-1",
          quantity: 2,
          unitPrice: 15.99,
          total: 31.98,
          product: {
            id: "mock-product-id-1",
            name: "Test Product 1",
            sku: "TEST-123"
          }
        },
        {
          id: "mock-sale-item-id-2",
          saleId: "mock-sale-id",
          productId: "mock-product-id-2",
          quantity: 1,
          unitPrice: 13.99,
          total: 13.99,
          product: {
            id: "mock-product-id-2",
            name: "Test Product 2",
            sku: "TEST-456"
          }
        }
      ]
    }
  });
};

// Mock Returns API
const getReturns = async (req: Request) => {
  return NextResponse.json({
    returns: [
      {
        id: "mock-return-id-1",
        saleId: "mock-sale-id-1",
        returnNumber: "RET-001",
        total: 15.99,
        reason: "DEFECTIVE",
        userId: "mock-user-id-1",
        createdAt: new Date(Date.now() - 3600000),
        sale: {
          id: "mock-sale-id-1",
          invoiceNumber: "INV-001"
        },
        user: {
          id: "mock-user-id-1",
          name: "Admin User"
        },
        items: [
          {
            id: "mock-return-item-id-1",
            returnId: "mock-return-id-1",
            productId: "mock-product-id-1",
            quantity: 1,
            unitPrice: 15.99,
            total: 15.99,
            product: {
              id: "mock-product-id-1",
              name: "Test Product 1",
              sku: "TEST-123"
            }
          }
        ]
      }
    ],
    pagination: {
      total: 1,
      page: 1,
      limit: 10,
      pages: 1
    }
  });
};

const createReturn = async (req: Request) => {
  const body = await req.json();
  return NextResponse.json({
    return: {
      id: "mock-new-return-id",
      saleId: body.saleId || "mock-sale-id-1",
      returnNumber: "RET-002",
      total: body.total || 13.99,
      reason: body.reason || "CUSTOMER_DISSATISFACTION",
      userId: "mock-user-id-1",
      createdAt: new Date(),
      items: body.items || [
        {
          id: "mock-new-return-item-id",
          returnId: "mock-new-return-id",
          productId: "mock-product-id-2",
          quantity: 1,
          unitPrice: 13.99,
          total: 13.99
        }
      ]
    }
  });
};

const getReturn = async (req: Request, { params }: { params: { id: string } }) => {
  return NextResponse.json({
    return: {
      id: params.id || "mock-return-id",
      saleId: "mock-sale-id-1",
      returnNumber: "RET-001",
      total: 15.99,
      reason: "DEFECTIVE",
      userId: "mock-user-id-1",
      createdAt: new Date(Date.now() - 3600000),
      sale: {
        id: "mock-sale-id-1",
        invoiceNumber: "INV-001"
      },
      user: {
        id: "mock-user-id-1",
        name: "Admin User"
      },
      items: [
        {
          id: "mock-return-item-id-1",
          returnId: "mock-return-id",
          productId: "mock-product-id-1",
          quantity: 1,
          unitPrice: 15.99,
          total: 15.99,
          product: {
            id: "mock-product-id-1",
            name: "Test Product 1",
            sku: "TEST-123"
          }
        }
      ]
    }
  });
};

// Mock Reports API
const getSalesReport = async (req: Request) => {
  const url = new URL(req.url);
  const reportType = url.searchParams.get("type") || "daily";
  
  if (reportType === "daily") {
    return NextResponse.json({
      reportType: "daily",
      date: new Date().toISOString().split("T")[0],
      summary: {
        totalSales: 5,
        totalAmount: 245.87,
        averageAmount: 49.17,
        totalTax: 24.59,
        totalDiscount: 5.00
      },
      hourlyBreakdown: [
        { hour: "09:00", count: 1, amount: 29.99 },
        { hour: "10:00", count: 0, amount: 0 },
        { hour: "11:00", count: 2, amount: 75.98 },
        { hour: "12:00", count: 1, amount: 89.95 },
        { hour: "13:00", count: 0, amount: 0 },
        { hour: "14:00", count: 1, amount: 49.95 }
      ],
      paymentMethods: [
        { method: "CASH", count: 3, amount: 125.93 },
        { method: "CARD", count: 2, amount: 119.94 }
      ]
    });
  } else if (reportType === "monthly") {
    return NextResponse.json({
      reportType: "monthly",
      month: new Date().toISOString().split("T")[0].substring(0, 7),
      summary: {
        totalSales: 87,
        totalAmount: 4325.63,
        averageAmount: 49.72,
        totalTax: 432.56,
        totalDiscount: 215.00
      },
      dailyBreakdown: [
        { date: "2023-05-01", count: 3, amount: 145.97 },
        { date: "2023-05-02", count: 5, amount: 245.85 },
        { date: "2023-05-03", count: 4, amount: 189.96 },
        // More days would be included here
      ],
      paymentMethods: [
        { method: "CASH", count: 45, amount: 2162.82 },
        { method: "CARD", count: 42, amount: 2162.81 }
      ]
    });
  }
  
  return NextResponse.json({ error: "Invalid report type" }, { status: 400 });
};

// Transaction API Tests
export async function runTransactionTests() {
  const tests = [];

  // Sales Tests
  tests.push(await runApiTest(
    "Get Sales List",
    getSales,
    createMockAuthRequest("http://localhost:3000/api/transactions/sales")
  ));

  tests.push(await runApiTest(
    "Create New Sale",
    createSale,
    createMockAuthRequest(
      "http://localhost:3000/api/transactions/sales",
      "POST",
      {
        customerName: "Walk-in Customer",
        paymentMethod: "CASH",
        items: [
          {
            productId: "mock-product-id-1",
            quantity: 2,
            unitPrice: 15.99
          },
          {
            productId: "mock-product-id-2",
            quantity: 1,
            unitPrice: 13.99
          }
        ]
      }
    )
  ));

  tests.push(await runApiTest(
    "Get Sale by ID",
    (req) => getSale(req, { params: { id: "mock-sale-id" } }),
    createMockAuthRequest("http://localhost:3000/api/transactions/sales/mock-sale-id")
  ));

  // Returns Tests
  tests.push(await runApiTest(
    "Get Returns List",
    getReturns,
    createMockAuthRequest("http://localhost:3000/api/transactions/returns")
  ));

  tests.push(await runApiTest(
    "Create New Return",
    createReturn,
    createMockAuthRequest(
      "http://localhost:3000/api/transactions/returns",
      "POST",
      {
        saleId: "mock-sale-id-1",
        reason: "CUSTOMER_DISSATISFACTION",
        items: [
          {
            productId: "mock-product-id-2",
            quantity: 1,
            unitPrice: 13.99
          }
        ]
      }
    )
  ));

  tests.push(await runApiTest(
    "Get Return by ID",
    (req) => getReturn(req, { params: { id: "mock-return-id" } }),
    createMockAuthRequest("http://localhost:3000/api/transactions/returns/mock-return-id")
  ));

  // Reports Tests
  tests.push(await runApiTest(
    "Get Daily Sales Report",
    getSalesReport,
    createMockAuthRequest("http://localhost:3000/api/transactions/reports/sales?type=daily")
  ));

  tests.push(await runApiTest(
    "Get Monthly Sales Report",
    getSalesReport,
    createMockAuthRequest("http://localhost:3000/api/transactions/reports/sales?type=monthly")
  ));

  return tests;
}

export async function runAllTransactionTests() {
  return await runTransactionTests();
}
