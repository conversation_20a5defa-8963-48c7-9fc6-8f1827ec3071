const { PrismaClient } = require('./src/generated/prisma');
const bcrypt = require('bcryptjs');

async function main() {
  const prisma = new PrismaClient();
  
  try {
    // Find user by email
    const email = '<EMAIL>';
    console.log(`Looking for user with email: ${email}`);
    
    const user = await prisma.user.findUnique({
      where: { email },
    });
    
    if (!user) {
      console.log('No user found with this email');
      return;
    }
    
    console.log('User found:', {
      id: user.id,
      name: user.name,
      email: user.email,
      role: user.role,
      active: user.active,
      passwordExists: !!user.password,
      passwordLength: user.password ? user.password.length : 0
    });
    
    // Test password verification
    const testPassword = 'admin123';
    console.log(`Testing password: ${testPassword}`);
    
    const passwordMatch = await bcrypt.compare(testPassword, user.password);
    console.log(`Password match: ${passwordMatch ? 'Yes' : 'No'}`);
    
    // If password doesn't match, try to update it
    if (!passwordMatch) {
      console.log('Password does not match. Updating password...');
      
      const hashedPassword = await bcrypt.hash(testPassword, 10);
      
      await prisma.user.update({
        where: { id: user.id },
        data: { password: hashedPassword },
      });
      
      console.log('Password updated successfully');
      
      // Verify the new password
      const updatedUser = await prisma.user.findUnique({
        where: { id: user.id },
      });
      
      const newPasswordMatch = await bcrypt.compare(testPassword, updatedUser.password);
      console.log(`New password match: ${newPasswordMatch ? 'Yes' : 'No'}`);
    }
    
  } catch (error) {
    console.error('Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

main();
