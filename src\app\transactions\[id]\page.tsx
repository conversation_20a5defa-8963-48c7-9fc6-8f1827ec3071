"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { MainLayout } from "@/components/layout/MainLayout";
import { PageHeader } from "@/components/layout/PageHeader";
import { Button } from "@/components/ui/button";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
// Dialog components are not used in this file
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import {
  ArrowLeft,
  AlertCircle,
  Loader2,
  Receipt,
  Ban,
  Calendar,
  User,
  <PERSON><PERSON>ard,
} from "lucide-react";
import { toast } from "sonner";
import Link from "next/link";
import { formatCurrency, formatDate } from "@/lib/utils";
import { use } from "react";

interface Transaction {
  id: string;
  transactionDate: string;
  subtotal: number;
  discount: number;
  tax: number;
  total: number;
  paymentMethod: string;
  paymentStatus: string;
  status: string;
  notes?: string;
  dueDate?: string;
  cashier: {
    id: string;
    name: string;
  };
  approver?: {
    id: string;
    name: string;
  };
  customer?: {
    id: string;
    name: string;
    phone?: string;
  };
  items: Array<{
    id: string;
    productId: string;
    quantity: number;
    unitPrice: number;
    discount: number;
    subtotal: number;
    product: {
      id: string;
      name: string;
      sku: string;
      unit: {
        abbreviation: string;
      };
    };
  }>;
}

export default function TransactionDetailPage({ params }: { params: { id: string } }) {
  const router = useRouter();
  const [transaction, setTransaction] = useState<Transaction | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isVoiding, setIsVoiding] = useState(false);
  const [voidDialogOpen, setVoidDialogOpen] = useState(false);

  // Unwrap params using React.use()
  const unwrappedParams = use(params as any) as { id: string };
  const id = unwrappedParams.id;

  // Fetch transaction details
  const fetchTransaction = async () => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await fetch(`/api/transactions/${id}`);
      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || "Failed to fetch transaction details");
      }

      setTransaction(data.transaction);
    } catch (err) {
      setError(err instanceof Error ? err.message : "An unknown error occurred");
      console.error("Error fetching transaction:", err);
    } finally {
      setIsLoading(false);
    }
  };

  // Void transaction
  const handleVoidTransaction = async () => {
    setIsVoiding(true);

    try {
      const response = await fetch(`/api/transactions/${id}`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          status: "VOIDED",
          paymentStatus: "CANCELLED",
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || "Failed to void transaction");
      }

      toast.success("Transaction voided successfully");
      setVoidDialogOpen(false);
      fetchTransaction(); // Refresh the data
    } catch (err) {
      toast.error(err instanceof Error ? err.message : "An unknown error occurred");
      console.error("Error voiding transaction:", err);
    } finally {
      setIsVoiding(false);
    }
  };

  // Fetch transaction on initial load
  useEffect(() => {
    fetchTransaction();
  }, [id]);

  if (isLoading) {
    return (
      <MainLayout>
        <div className="flex justify-center items-center h-64">
          <Loader2 className="h-8 w-8 animate-spin text-gray-500" />
        </div>
      </MainLayout>
    );
  }

  if (error) {
    return (
      <MainLayout>
        <Alert variant="destructive" className="mb-6">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
        <Button variant="outline" onClick={() => router.push("/transactions")}>
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Transactions
        </Button>
      </MainLayout>
    );
  }

  if (!transaction) {
    return (
      <MainLayout>
        <Alert variant="destructive" className="mb-6">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>Transaction not found</AlertDescription>
        </Alert>
        <Button variant="outline" onClick={() => router.push("/transactions")}>
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Transactions
        </Button>
      </MainLayout>
    );
  }

  return (
    <MainLayout>
      <PageHeader
        title="Transaction Details"
        description={`Transaction #${transaction.id}`}
        actions={
          <div className="flex gap-2">
            <Button variant="outline" asChild>
              <Link href="/transactions">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back
              </Link>
            </Button>
            <Button asChild>
              <Link href={`/receipts/${transaction.id}`}>
                <Receipt className="h-4 w-4 mr-2" />
                View Receipt
              </Link>
            </Button>
            {transaction.status !== "VOIDED" && (
              <AlertDialog open={voidDialogOpen} onOpenChange={setVoidDialogOpen}>
                <AlertDialogTrigger asChild>
                  <Button
                    variant="outline"
                    className="bg-red-50 text-red-600 hover:bg-red-100 hover:text-red-700 border-red-200"
                  >
                    <Ban className="h-4 w-4 mr-2" />
                    Void Transaction
                  </Button>
                </AlertDialogTrigger>
                <AlertDialogContent>
                  <AlertDialogHeader>
                    <AlertDialogTitle>Void Transaction</AlertDialogTitle>
                    <AlertDialogDescription>
                      Are you sure you want to void this transaction? This will return all items to
                      inventory and mark the transaction as voided.
                    </AlertDialogDescription>
                  </AlertDialogHeader>
                  <AlertDialogFooter>
                    <AlertDialogCancel disabled={isVoiding}>Cancel</AlertDialogCancel>
                    <AlertDialogAction
                      onClick={(e) => {
                        e.preventDefault();
                        handleVoidTransaction();
                      }}
                      disabled={isVoiding}
                      className="bg-red-600 hover:bg-red-700"
                    >
                      {isVoiding ? "Voiding..." : "Void Transaction"}
                    </AlertDialogAction>
                  </AlertDialogFooter>
                </AlertDialogContent>
              </AlertDialog>
            )}
          </div>
        }
      />

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
        <Card>
          <CardHeader>
            <CardTitle>Transaction Info</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center">
                <Calendar className="h-4 w-4 mr-2 text-gray-500" />
                <span>Date: {formatDate(new Date(transaction.transactionDate))}</span>
              </div>
              <div className="flex items-center">
                <User className="h-4 w-4 mr-2 text-gray-500" />
                <span>Cashier: {transaction.cashier.name}</span>
              </div>
              {transaction.approver && (
                <div className="flex items-center">
                  <User className="h-4 w-4 mr-2 text-gray-500" />
                  <span>Approved by: {transaction.approver.name}</span>
                </div>
              )}
              <div className="flex items-center">
                <CreditCard className="h-4 w-4 mr-2 text-gray-500" />
                <span>Payment: {transaction.paymentMethod}</span>
              </div>
              <div className="flex items-center">
                <div
                  className={`text-xs px-2 py-0.5 rounded-full ${
                    transaction.status === "COMPLETED"
                      ? "bg-green-100 text-green-800"
                      : transaction.status === "VOIDED"
                        ? "bg-red-100 text-red-800"
                        : "bg-blue-100 text-blue-800"
                  }`}
                >
                  {transaction.status}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Payment Details</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <span>Payment Method:</span>
                <span>{transaction.paymentMethod}</span>
              </div>
              <div className="flex items-center justify-between">
                <span>Payment Status:</span>
                <div
                  className={`text-xs px-2 py-0.5 rounded-full ${
                    transaction.paymentStatus === "PAID"
                      ? "bg-green-100 text-green-800"
                      : transaction.paymentStatus === "PARTIAL"
                        ? "bg-yellow-100 text-yellow-800"
                        : transaction.paymentStatus === "CANCELLED"
                          ? "bg-red-100 text-red-800"
                          : "bg-blue-100 text-blue-800"
                  }`}
                >
                  {transaction.paymentStatus}
                </div>
              </div>
              {transaction.dueDate && (
                <div className="flex items-center justify-between">
                  <span>Due Date:</span>
                  <span>{formatDate(new Date(transaction.dueDate))}</span>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Customer</CardTitle>
          </CardHeader>
          <CardContent>
            {transaction.customer ? (
              <div className="space-y-2">
                <div className="font-medium">{transaction.customer.name}</div>
                {transaction.customer.phone && (
                  <div className="text-sm text-gray-500">{transaction.customer.phone}</div>
                )}
                <Button variant="outline" size="sm" className="mt-2" asChild>
                  <Link href={`/customers/${transaction.customer.id}`}>View Customer</Link>
                </Button>
              </div>
            ) : (
              <div className="text-gray-500">Walk-in customer</div>
            )}
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Items</CardTitle>
          <CardDescription>Products included in this transaction</CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Product</TableHead>
                <TableHead className="text-right">Price</TableHead>
                <TableHead className="text-right">Quantity</TableHead>
                <TableHead className="text-right">Discount</TableHead>
                <TableHead className="text-right">Total</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {transaction.items.map((item) => (
                <TableRow key={item.id}>
                  <TableCell>
                    <div>
                      <div className="font-medium">{item.product.name}</div>
                      <div className="text-sm text-gray-500">SKU: {item.product.sku}</div>
                    </div>
                  </TableCell>
                  <TableCell className="text-right">{formatCurrency(item.unitPrice)}</TableCell>
                  <TableCell className="text-right">
                    {item.quantity} {item.product.unit.abbreviation}
                  </TableCell>
                  <TableCell className="text-right">
                    {item.discount > 0 ? formatCurrency(item.discount) : "-"}
                  </TableCell>
                  <TableCell className="text-right font-medium">
                    {formatCurrency(item.subtotal - item.discount)}
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>

          <div className="mt-6 space-y-2">
            <div className="flex justify-between">
              <span>Subtotal</span>
              <span>{formatCurrency(transaction.subtotal)}</span>
            </div>
            {transaction.discount > 0 && (
              <div className="flex justify-between">
                <span>Discount</span>
                <span>-{formatCurrency(transaction.discount)}</span>
              </div>
            )}
            {transaction.tax > 0 && (
              <div className="flex justify-between">
                <span>Tax</span>
                <span>{formatCurrency(transaction.tax)}</span>
              </div>
            )}
            <div className="flex justify-between font-bold text-lg pt-2 border-t">
              <span>Total</span>
              <span>{formatCurrency(transaction.total)}</span>
            </div>
          </div>

          {transaction.notes && (
            <div className="mt-6 pt-4 border-t">
              <div className="font-medium mb-1">Notes</div>
              <div className="text-sm">{transaction.notes}</div>
            </div>
          )}
        </CardContent>
      </Card>
    </MainLayout>
  );
}
