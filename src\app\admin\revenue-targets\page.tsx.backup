"use client";

import { useState, useEffect } from "react";
import { MainLayout } from "@/components/layout/MainLayout";
import { PageHeader } from "@/components/layout/PageHeader";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Separator } from "@/components/ui/separator";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { Calendar } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Calendar as CalendarIcon,
  Loader2,
  Plus,
  Edit,
  Trash2,
  Target,
  TrendingUp,
  DollarSign,
} from "lucide-react";
import { format } from "date-fns";
import { cn, formatCurrency } from "@/lib/utils";
import { toast } from "sonner";

interface RevenueTarget {
  id: string;
  name: string;
  description?: string;
  targetType: "DAILY" | "WEEKLY" | "MONTHLY" | "QUARTERLY" | "YEARLY";
  startDate: string;
  endDate: string;
  amount: number;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  user: {
    id: string;
    name: string;
    email: string;
  };
}

// Helper function to calculate target progress
const calculateTargetProgress = (target: RevenueTarget, actualRevenue: number = 0) => {
  const percentage = target.amount > 0 ? Math.min((actualRevenue / target.amount) * 100, 100) : 0;

  let status: "on-track" | "behind" | "critical" = "on-track";
  let color = "bg-green-500";

  if (percentage < 50) {
    status = "critical";
    color = "bg-red-500";
  } else if (percentage < 80) {
    status = "behind";
    color = "bg-yellow-500";
  }

  return {
    percentage: Math.round(percentage),
    status,
    color,
    actualRevenue,
    targetAmount: target.amount,
    difference: actualRevenue - target.amount,
  };
};

export default function RevenueTargetsPage() {
  // State for targets and form
  const [targets, setTargets] = useState<RevenueTarget[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [submitting, setSubmitting] = useState(false);
  const [editingTarget, setEditingTarget] = useState<RevenueTarget | null>(null);
  const [revenueData, setRevenueData] = useState<Record<string, number>>({});
  const [showForm, setShowForm] = useState(false);

  // Form state
  const [formData, setFormData] = useState({
    name: "",
    description: "",
    targetType: "MONTHLY" as "DAILY" | "WEEKLY" | "MONTHLY" | "QUARTERLY" | "YEARLY",
    amount: 0,
    startDate: undefined as Date | undefined,
    endDate: undefined as Date | undefined,
    isActive: true,
  });

  useEffect(() => {
    fetchTargets();
  }, []);

  // Fetch revenue data when targets change
  useEffect(() => {
    if (targets.length > 0) {
      fetchRevenueData();
    }
  }, [targets]);

  const fetchTargets = async () => {
    try {
      setLoading(true);
      const response = await fetch("/api/revenue-targets");

      if (!response.ok) {
        throw new Error("Failed to fetch revenue targets");
      }

      const data = await response.json();
      setTargets(data.data || []);
    } catch (err) {
      setError(err instanceof Error ? err.message : "An error occurred");
    } finally {
      setLoading(false);
    }
  };

  const fetchRevenueData = async () => {
    try {
      if (targets.length === 0) {
        setRevenueData({});
        return;
      }

      console.log("[Revenue Data] Fetching actual revenue for", targets.length, "targets");
      const revenuePromises = targets.map(async (target) => {
        try {
          // Format dates for API call
          const fromDate = new Date(target.startDate).toISOString().split("T")[0];
          const toDate = new Date(target.endDate).toISOString().split("T")[0];

          console.log(
            `[Revenue Data] Fetching for target ${target.name}: ${fromDate} to ${toDate}`
          );

          // Call the revenue summary API for this target's date range
          const response = await fetch(
            `/api/analytics/revenue-summary?fromDate=${fromDate}&toDate=${toDate}`
          );

          if (!response.ok) {
            console.error(`[Revenue Data] API error for target ${target.id}:`, response.status);
            return { targetId: target.id, revenue: 0 };
          }

          const data = await response.json();
          const revenue = data.data?.totalRevenue || 0;

          console.log(`[Revenue Data] Target ${target.name}: ${revenue} IDR`);
          return { targetId: target.id, revenue };
        } catch (error) {
          console.error(`[Revenue Data] Error fetching revenue for target ${target.id}:`, error);
          return { targetId: target.id, revenue: 0 };
        }
      });

      // Wait for all revenue data to be fetched
      const revenueResults = await Promise.all(revenuePromises);

      // Convert to the expected format
      const revenueDataMap: Record<string, number> = {};
      revenueResults.forEach(({ targetId, revenue }) => {
        revenueDataMap[targetId] = revenue;
      });

      console.log("[Revenue Data] Final revenue data:", revenueDataMap);
      setRevenueData(revenueDataMap);
    } catch (err) {
      console.error("Failed to fetch revenue data:", err);
      setRevenueData({});
    }
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setSubmitting(true);
    setError(null);
    setSuccess(null);

    try {
      const requestBody = {
        name: formData.name,
        description: formData.description,
        targetType: formData.targetType,
        amount: formData.amount,
        startDate: formData.startDate?.toISOString(),
        endDate: formData.endDate?.toISOString(),
        isActive: formData.isActive,
      };

      const url = editingTarget
        ? `/api/revenue-targets/${editingTarget.id}`
        : "/api/revenue-targets";
      const method = editingTarget ? "PUT" : "POST";

      const response = await fetch(url, {
        method,
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(requestBody),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || "Failed to save revenue target");
      }

      setSuccess(
        editingTarget
          ? "Revenue target updated successfully"
          : "Revenue target created successfully"
      );

      // Reset form
      setFormData({
        name: "",
        description: "",
        targetType: "MONTHLY",
        amount: 0,
        startDate: undefined,
        endDate: undefined,
        isActive: true,
      });
      setEditingTarget(null);
      setShowForm(false);

      // Refresh targets list
      fetchTargets();
    } catch (error) {
      console.error("Error saving revenue target:", error);
      setError((error as Error).message || "Failed to save revenue target");
    } finally {
      setSubmitting(false);
    }
  };

  const handleAddNew = () => {
    setEditingTarget(null);
    setFormData({
      name: "",
      description: "",
      targetType: "MONTHLY",
      amount: 0,
      startDate: undefined,
      endDate: undefined,
      isActive: true,
    });
    setShowForm(true);
  };

  const handleEdit = (target: RevenueTarget) => {
    setEditingTarget(target);
    setFormData({
      name: target.name,
      description: target.description || "",
      targetType: target.targetType,
      amount: target.amount,
      startDate: new Date(target.startDate),
      endDate: new Date(target.endDate),
      isActive: target.isActive,
    });
    setShowForm(true);
  };

  const handleDelete = async (targetId: string) => {
    if (!confirm("Are you sure you want to delete this revenue target?")) {
      return;
    }

    try {
      const response = await fetch(`/api/revenue-targets/${targetId}`, {
        method: "DELETE",
      });

      if (!response.ok) {
        throw new Error("Failed to delete revenue target");
      }

      setTargets(targets.filter((t) => t.id !== targetId));
    } catch (err) {
      setError(err instanceof Error ? err.message : "Failed to delete target");
    }
  };

  const handleCancel = () => {
    setShowForm(false);
    setEditingTarget(null);
    setFormData({
      name: "",
      description: "",
      targetType: "MONTHLY",
      amount: 0,
      startDate: undefined,
      endDate: undefined,
      isActive: true,
    });
    setError(null);
    setSuccess(null);
  };

  const getTargetTypeLabel = (type: string) => {
    return type.charAt(0) + type.slice(1).toLowerCase();
  };

  const getTargetStatusBadge = (target: RevenueTarget) => {
    const progress = calculateTargetProgress(target, revenueData[target.id] || 0);

    if (progress.status === "critical") {
      return <Badge variant="destructive">Critical</Badge>;
    } else if (progress.status === "behind") {
      return <Badge variant="secondary">Behind</Badge>;
    } else {
      return <Badge variant="default">On Track</Badge>;
    }
  };

  if (loading) {
    return (
      <div className="p-6">
        <div className="animate-pulse space-y-4">
          <div className="h-8 bg-gray-200 rounded w-1/4"></div>
          <div className="h-32 bg-gray-200 rounded"></div>
          <div className="h-32 bg-gray-200 rounded"></div>
        </div>
      </div>
    );
  }

  return (
    <MainLayout>
      <PageHeader
        title="Revenue Targets"
        description="Manage revenue targets and performance goals"
      />

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mt-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Targets</CardTitle>
            <Target className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{targets.length}</div>
            <p className="text-xs text-muted-foreground">
              {targets.filter((t) => t.isActive).length} active
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">On Track</CardTitle>
            <TrendingUp className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">
              {
                targets.filter((t) => {
                  const progress = calculateTargetProgress(t, revenueData[t.id] || 0);
                  return progress.status === "on-track";
                }).length
              }
            </div>
            <p className="text-xs text-muted-foreground">targets meeting goals</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Behind</CardTitle>
            <CalendarIcon className="h-4 w-4 text-yellow-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-yellow-600">
              {
                targets.filter((t) => {
                  const progress = calculateTargetProgress(t, revenueData[t.id] || 0);
                  return progress.status === "behind";
                }).length
              }
            </div>
            <p className="text-xs text-muted-foreground">targets behind schedule</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Critical</CardTitle>
            <DollarSign className="h-4 w-4 text-red-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">
              {
                targets.filter((t) => {
                  const progress = calculateTargetProgress(t, revenueData[t.id] || 0);
                  return progress.status === "critical";
                }).length
              }
            </div>
            <p className="text-xs text-muted-foreground">targets need attention</p>
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mt-6">
        {/* Form */}
        <Card className="md:col-span-1">
          <CardHeader>
            <CardTitle>
              {editingTarget ? "Edit Revenue Target" : "Add New Revenue Target"}
            </CardTitle>
          </CardHeader>
          <CardContent className="p-6">
            <form onSubmit={handleSubmit} className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="name">Target Name</Label>
                <Input
                  id="name"
                  value={formData.name}
                  onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                  placeholder="Enter target name"
                  disabled={submitting}
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="description">Description (Optional)</Label>
                <Input
                  id="description"
                  value={formData.description}
                  onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                  placeholder="Enter description"
                  disabled={submitting}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="targetType">Target Type</Label>
                <Select
                  value={formData.targetType}
                  onValueChange={(value: any) => setFormData({ ...formData, targetType: value })}
                >
                  <SelectTrigger id="targetType">
                    <SelectValue placeholder="Select target type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="DAILY">Daily</SelectItem>
                    <SelectItem value="WEEKLY">Weekly</SelectItem>
                    <SelectItem value="MONTHLY">Monthly</SelectItem>
                    <SelectItem value="QUARTERLY">Quarterly</SelectItem>
                    <SelectItem value="YEARLY">Yearly</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="amount">Target Amount (IDR)</Label>
                <Input
                  id="amount"
                  type="number"
                  value={formData.amount}
                  onChange={(e) =>
                    setFormData({ ...formData, amount: parseFloat(e.target.value) || 0 })
                  }
                  min={0}
                  step={1000}
                  disabled={submitting}
                  required
                />
              </div>

              <div className="space-y-2">
                <Label>Date Range</Label>
                <div className="grid grid-cols-2 gap-2">
                  <div>
                    <Label htmlFor="startDate" className="text-xs">
                      Start Date
                    </Label>
                    <Popover key={`start-${editingTarget?.id || "new"}`}>
                      <PopoverTrigger asChild>
                        <Button
                          variant="outline"
                          className={cn(
                            "w-full justify-start text-left font-normal",
                            !formData.startDate && "text-muted-foreground"
                          )}
                          disabled={submitting}
                        >
                          <CalendarIcon className="mr-2 h-4 w-4" />
                          {formData.startDate ? format(formData.startDate, "PPP") : "Pick a date"}
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0" align="start">
                        <Calendar
                          mode="single"
                          selected={formData.startDate}
                          onSelect={(date) => date && setFormData({ ...formData, startDate: date })}
                          initialFocus
                        />
                      </PopoverContent>
                    </Popover>
                  </div>
                  <div>
                    <Label htmlFor="endDate" className="text-xs">
                      End Date
                    </Label>
                    <Popover key={`end-${editingTarget?.id || "new"}`}>
                      <PopoverTrigger asChild>
                        <Button
                          variant="outline"
                          className={cn(
                            "w-full justify-start text-left font-normal",
                            !formData.endDate && "text-muted-foreground"
                          )}
                          disabled={submitting}
                        >
                          <CalendarIcon className="mr-2 h-4 w-4" />
                          {formData.endDate ? format(formData.endDate, "PPP") : "Pick a date"}
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0" align="start">
                        <Calendar
                          mode="single"
                          selected={formData.endDate}
                          onSelect={(date) => date && setFormData({ ...formData, endDate: date })}
                          disabled={(date) =>
                            formData.startDate ? date < formData.startDate : false
                          }
                          initialFocus
                        />
                      </PopoverContent>
                    </Popover>
                  </div>
                </div>
              </div>

              <div className="flex items-center space-x-2">
                <Switch
                  id="isActive"
                  checked={formData.isActive}
                  onCheckedChange={(checked) => setFormData({ ...formData, isActive: checked })}
                  disabled={submitting}
                />
                <Label htmlFor="isActive">Active</Label>
              </div>

              {error && (
                <Alert variant="destructive">
                  <AlertDescription>{error}</AlertDescription>
                </Alert>
              )}

              {success && (
                <Alert variant="default" className="bg-green-50 text-green-800 border-green-200">
                  <AlertDescription>{success}</AlertDescription>
                </Alert>
              )}

              <div className="flex gap-2">
                <Button
                  type="submit"
                  className="flex-1"
                  disabled={
                    submitting ||
                    !formData.name ||
                    !formData.startDate ||
                    !formData.endDate ||
                    formData.amount <= 0 ||
                    (formData.startDate &&
                      formData.endDate &&
                      formData.startDate >= formData.endDate)
                  }
                >
                  {submitting ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      {editingTarget ? "Updating..." : "Creating..."}
                    </>
                  ) : editingTarget ? (
                    "Update Target"
                  ) : (
                    "Create Target"
                  )}
                </Button>
                {(editingTarget || showForm) && (
                  <Button
                    type="button"
                    variant="outline"
                    onClick={handleCancel}
                    disabled={submitting}
                  >
                    Cancel
                  </Button>
                )}
              </div>
            </form>
          </CardContent>
        </Card>

        {/* Targets List */}
        <Card className="md:col-span-2">
          <CardHeader>
            <CardTitle>Revenue Targets</CardTitle>
          </CardHeader>
          <CardContent>
            {targets.length === 0 ? (
              <div className="text-center py-8">
                <Target className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">No revenue targets</h3>
                <p className="text-gray-500 mb-4">
                  Get started by creating your first revenue target.
                </p>
                <Button onClick={handleAddNew}>
                  <Plus className="h-4 w-4 mr-2" />
                  Add Revenue Target
                </Button>
              </div>
            ) : (
              <div className="space-y-4">
                {targets.map((target) => {
                  const progress = calculateTargetProgress(target, revenueData[target.id] || 0);

                  return (
                    <div
                      key={target.id}
                      className="border rounded-lg p-4 hover:bg-gray-50 transition-colors"
                    >
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-center gap-3 mb-2">
                            <h3 className="font-semibold text-lg">{target.name}</h3>
                            {getTargetStatusBadge(target)}
                            <Badge variant="outline">{getTargetTypeLabel(target.targetType)}</Badge>
                            {!target.isActive && <Badge variant="secondary">Inactive</Badge>}
                          </div>

                          {target.description && (
                            <p className="text-gray-600 mb-3">{target.description}</p>
                          )}

                          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                            <div>
                              <p className="text-sm text-gray-500">Target Amount</p>
                              <p className="font-semibold">{formatCurrency(target.amount)}</p>
                            </div>
                            <div>
                              <p className="text-sm text-gray-500">Actual Revenue</p>
                              <p className="font-semibold">
                                {formatCurrency(progress.actualRevenue)}
                              </p>
                            </div>
                            <div>
                              <p className="text-sm text-gray-500">Period</p>
                              <p className="font-semibold">
                                {new Date(target.startDate).toLocaleDateString()} -{" "}
                                {new Date(target.endDate).toLocaleDateString()}
                              </p>
                            </div>
                          </div>

                          <div className="mb-3">
                            <div className="flex justify-between items-center mb-1">
                              <span className="text-sm text-gray-600">Progress</span>
                              <span className="text-sm font-medium">{progress.percentage}%</span>
                            </div>
                            <Progress value={progress.percentage} className="h-2" />
                          </div>

                          <div className="flex items-center justify-between text-sm text-gray-500">
                            <span>Created by {target.user.name}</span>
                            <span>{new Date(target.createdAt).toLocaleDateString()}</span>
                          </div>
                        </div>

                        <div className="flex items-center gap-2 ml-4">
                          <Button variant="outline" size="sm" onClick={() => handleEdit(target)}>
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleDelete(target.id)}
                            className="text-red-600 hover:text-red-700"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </MainLayout>
  );
}
