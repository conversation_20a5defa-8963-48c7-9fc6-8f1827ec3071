import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { prisma } from '@/lib/prisma';
import { verifyAuthToken } from '@/lib/auth-utils';

// Purchase order template item schema for validation
const purchaseOrderTemplateItemSchema = z.object({
  productId: z.string().min(1, { message: "Product is required" }),
  quantity: z.coerce.number().positive({ message: "Quantity must be positive" }),
  unitPrice: z.coerce.number().positive({ message: "Unit price must be positive" }),
});

// Purchase order template schema for validation
const purchaseOrderTemplateSchema = z.object({
  name: z.string().min(1, { message: "Template name is required" }),
  description: z.string().optional(),
  supplierId: z.string().min(1, { message: "Supplier is required" }),
  taxPercentage: z.coerce.number().min(0, { message: "Tax percentage must be non-negative" }).max(100, { message: "Tax percentage cannot exceed 100%" }).default(0),
  notes: z.string().optional(),
  items: z.array(purchaseOrderTemplateItemSchema).min(1, { message: "At least one item is required" }),
});

export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const auth = await verifyAuthToken(request);
    if (!auth.authenticated) {
      return NextResponse.json({ error: auth.error }, { status: auth.status });
    }

    // Only allow SUPER_ADMIN and WAREHOUSE_ADMIN to view templates
    if (!["SUPER_ADMIN", "WAREHOUSE_ADMIN"].includes(auth.user.role)) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    // Parse query parameters
    const searchParams = request.nextUrl.searchParams;
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "10");
    const search = searchParams.get("search") || "";
    const supplierId = searchParams.get("supplierId") || "";
    const isActive = searchParams.get("isActive");

    // Build where clause
    const where: any = {};

    if (search) {
      where.OR = [
        { name: { contains: search, mode: 'insensitive' } },
        { description: { contains: search, mode: 'insensitive' } },
        { supplier: { name: { contains: search, mode: 'insensitive' } } },
      ];
    }

    if (supplierId) {
      where.supplierId = supplierId;
    }

    if (isActive !== null && isActive !== undefined) {
      where.isActive = isActive === "true";
    }

    // Calculate pagination
    const skip = (page - 1) * limit;

    // Fetch templates with pagination
    const [templates, totalCount] = await Promise.all([
      prisma.purchaseOrderTemplate.findMany({
        where,
        include: {
          supplier: {
            select: {
              id: true,
              name: true,
              contactPerson: true,
              phone: true,
              email: true,
            },
          },
          createdBy: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
          items: {
            include: {
              product: {
                include: {
                  category: true,
                  unit: true,
                },
              },
            },
          },
        },
        orderBy: {
          createdAt: 'desc',
        },
        skip,
        take: limit,
      }),
      prisma.purchaseOrderTemplate.count({ where }),
    ]);

    // Calculate pagination info
    const totalPages = Math.ceil(totalCount / limit);
    const hasNextPage = page < totalPages;
    const hasPreviousPage = page > 1;

    return NextResponse.json({
      templates,
      pagination: {
        currentPage: page,
        totalPages,
        totalCount,
        hasNextPage,
        hasPreviousPage,
        limit,
      },
    });
  } catch (error) {
    console.error("Error fetching purchase order templates:", error);
    return NextResponse.json(
      { error: "Failed to fetch purchase order templates", message: (error as Error).message },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const auth = await verifyAuthToken(request);
    if (!auth.authenticated) {
      return NextResponse.json({ error: auth.error }, { status: auth.status });
    }

    // Only allow SUPER_ADMIN and WAREHOUSE_ADMIN to create templates
    if (!["SUPER_ADMIN", "WAREHOUSE_ADMIN"].includes(auth.user.role)) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    // Parse and validate request body
    const body = await request.json();
    const validatedData = purchaseOrderTemplateSchema.parse(body);

    // Verify supplier exists
    const supplier = await prisma.supplier.findUnique({
      where: { id: validatedData.supplierId },
    });

    if (!supplier) {
      return NextResponse.json(
        { error: "Supplier not found" },
        { status: 404 }
      );
    }

    // Verify all products exist
    const productIds = validatedData.items.map(item => item.productId);
    const products = await prisma.product.findMany({
      where: { id: { in: productIds }, active: true },
    });

    if (products.length !== productIds.length) {
      return NextResponse.json(
        { error: "One or more products not found or inactive" },
        { status: 400 }
      );
    }

    // Check for duplicate template name for the same supplier
    const existingTemplate = await prisma.purchaseOrderTemplate.findFirst({
      where: {
        name: validatedData.name,
        supplierId: validatedData.supplierId,
        isActive: true,
      },
    });

    if (existingTemplate) {
      return NextResponse.json(
        { error: "A template with this name already exists for this supplier" },
        { status: 400 }
      );
    }

    // Create purchase order template with items
    const template = await prisma.purchaseOrderTemplate.create({
      data: {
        name: validatedData.name,
        description: validatedData.description,
        supplierId: validatedData.supplierId,
        taxPercentage: validatedData.taxPercentage,
        notes: validatedData.notes,
        createdById: auth.user.id,
        items: {
          create: validatedData.items.map(item => ({
            productId: item.productId,
            quantity: item.quantity,
            unitPrice: item.unitPrice,
          })),
        },
      },
      include: {
        supplier: {
          select: {
            id: true,
            name: true,
            contactPerson: true,
            phone: true,
            email: true,
          },
        },
        createdBy: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        items: {
          include: {
            product: {
              include: {
                category: true,
                unit: true,
              },
            },
          },
        },
      },
    });

    return NextResponse.json(template, { status: 201 });
  } catch (error) {
    console.error("Error creating purchase order template:", error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Validation failed", details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: "Failed to create purchase order template", message: (error as Error).message },
      { status: 500 }
    );
  }
}
