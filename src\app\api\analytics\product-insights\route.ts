import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/auth';
import { verifyAuthToken } from '@/lib/auth-utils';

export async function GET(request: NextRequest) {
  try {
    // Verify authentication using <PERSON>W<PERSON> token from cookies
    const authResult = await verifyAuthToken(request);

    if (!authResult.authenticated || !authResult.user) {
      console.log("[Product Insights API] Authentication failed:", authResult.error);
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { user } = authResult;
    const userRole = user.role;

    // Check if user has analytics access
    const hasAccess = ["SUPER_ADMIN", "FINANCE_ADMIN"].includes(userRole);
    if (!hasAccess) {
      console.log("[Product Insights API] User does not have access:", userRole);
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    const { searchParams } = new URL(request.url);
    const from = searchParams.get('from');
    const to = searchParams.get('to');
    const categoryIds = searchParams.get('categoryIds')?.split(',').filter(Boolean);

    console.log('[Product Insights API] Query params:', { from, to, categoryIds });

    // Set default date range (last 30 days)
    const endDate = to ? new Date(to) : new Date();
    const startDate = from ? new Date(from) : new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);

    console.log('[Product Insights API] Date range:', { startDate, endDate });

    // Build where clause for products
    const productWhere: any = { active: true };
    if (categoryIds?.length) {
      productWhere.categoryId = { in: categoryIds };
    }

    // Fetch all products with their sales data
    const products = await prisma.product.findMany({
      where: productWhere,
      include: {
        category: { select: { name: true } },
        storeStock: { select: { quantity: true, minThreshold: true } },
        transactionItems: {
          where: {
            transaction: {
              createdAt: { gte: startDate, lte: endDate },
              status: 'COMPLETED'
            }
          },
          select: {
            quantity: true,
            unitPrice: true,
            subtotal: true,
            transaction: {
              select: {
                createdAt: true,
                id: true
              }
            }
          }
        }
      }
    });

    console.log(`[Product Insights API] Found ${products.length} products`);

    // Calculate insights for each product
    const productInsights = products.map(product => {
      const sales = product.transactionItems;
      const totalQuantitySold = sales.reduce((sum, item) => sum + Number(item.quantity), 0);
      const totalRevenue = sales.reduce((sum, item) => sum + Number(item.subtotal), 0);
      const currentStock = product.storeStock?.quantity ? Number(product.storeStock.quantity) : 0;
      const purchasePrice = product.purchasePrice ? Number(product.purchasePrice) : 0;
      const basePrice = Number(product.basePrice);

      // Calculate turnover rate (sales per day)
      const daysDiff = Math.max(1, Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24)));
      const turnoverRate = totalQuantitySold / daysDiff;

      // Calculate profit margin
      const profitMargin = purchasePrice > 0 ? ((basePrice - purchasePrice) / basePrice) * 100 : 0;

      // Calculate days since last sale
      const lastSaleDate = sales.length > 0 
        ? Math.max(...sales.map(s => new Date(s.transaction.createdAt).getTime()))
        : 0;
      const daysSinceLastSale = lastSaleDate > 0 
        ? Math.ceil((Date.now() - lastSaleDate) / (1000 * 60 * 60 * 24))
        : 999;

      // Calculate average selling price
      const avgSellingPrice = sales.length > 0 
        ? sales.reduce((sum, item) => sum + Number(item.unitPrice), 0) / sales.length
        : basePrice;

      return {
        id: product.id,
        name: product.name,
        sku: product.sku,
        category: product.category?.name || 'Uncategorized',
        basePrice,
        purchasePrice,
        avgSellingPrice,
        currentStock,
        totalQuantitySold,
        totalRevenue,
        turnoverRate,
        profitMargin,
        daysSinceLastSale,
        salesCount: sales.length
      };
    });

    // Generate insights
    const insights = generateProductInsights(productInsights);

    console.log('[Product Insights API] Generated insights successfully');

    return NextResponse.json({
      success: true,
      data: insights,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('[Product Insights API] Error:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to fetch product insights',
        timestamp: new Date().toISOString()
      },
      { status: 500 }
    );
  }
}

function generateProductInsights(products: any[]) {
  // Sort products by different metrics
  const sortedByTurnover = [...products].sort((a, b) => b.turnoverRate - a.turnoverRate);
  const sortedByMargin = [...products].sort((a, b) => b.profitMargin - a.profitMargin);
  const sortedByDaysSinceLastSale = [...products].sort((a, b) => b.daysSinceLastSale - a.daysSinceLastSale);

  // Calculate averages
  const avgTurnoverRate = products.reduce((sum, p) => sum + p.turnoverRate, 0) / products.length;
  const avgProfitMargin = products.reduce((sum, p) => sum + p.profitMargin, 0) / products.length;

  // Calculate sales velocity trends
  const salesVelocityData = calculateSalesVelocity(products);

  // Calculate bundle suggestions
  const bundleSuggestions = calculateBundleSuggestions(products);

  // Calculate seasonal trends
  const seasonalTrends = calculateSeasonalTrends(products);

  return {
    // Stock Turnover Rate
    stockTurnover: {
      averageRate: avgTurnoverRate,
      fastMoving: sortedByTurnover.slice(0, 5).map(p => ({
        name: p.name,
        sku: p.sku,
        category: p.category,
        turnoverRate: p.turnoverRate,
        totalSold: p.totalQuantitySold
      })),
      slowMoving: sortedByTurnover.slice(-5).reverse().map(p => ({
        name: p.name,
        sku: p.sku,
        category: p.category,
        turnoverRate: p.turnoverRate,
        totalSold: p.totalQuantitySold,
        daysSinceLastSale: p.daysSinceLastSale
      }))
    },

    // Profit Margin Analysis
    profitMargins: {
      averageMargin: avgProfitMargin,
      highest: sortedByMargin.slice(0, 5).map(p => ({
        name: p.name,
        sku: p.sku,
        category: p.category,
        profitMargin: p.profitMargin,
        basePrice: p.basePrice,
        purchasePrice: p.purchasePrice
      })),
      lowest: sortedByMargin.slice(-5).reverse().map(p => ({
        name: p.name,
        sku: p.sku,
        category: p.category,
        profitMargin: p.profitMargin,
        basePrice: p.basePrice,
        purchasePrice: p.purchasePrice
      }))
    },

    // Price Optimization Opportunities
    priceOptimization: products
      .filter(p => p.profitMargin < avgProfitMargin && p.turnoverRate > avgTurnoverRate)
      .slice(0, 5)
      .map(p => ({
        name: p.name,
        sku: p.sku,
        category: p.category,
        currentMargin: p.profitMargin,
        turnoverRate: p.turnoverRate,
        suggestedAction: 'Consider price increase',
        potentialIncrease: Math.min(10, (avgProfitMargin - p.profitMargin) / 2)
      })),

    // Dead Stock (no sales in 30+ days)
    deadStock: products
      .filter(p => p.daysSinceLastSale >= 30)
      .sort((a, b) => b.daysSinceLastSale - a.daysSinceLastSale)
      .slice(0, 10)
      .map(p => ({
        name: p.name,
        sku: p.sku,
        category: p.category,
        daysSinceLastSale: p.daysSinceLastSale,
        currentStock: p.currentStock,
        lastSaleValue: p.totalRevenue > 0 ? p.totalRevenue / p.salesCount : 0
      })),

    // Promotion Opportunities (slow-moving with good margins)
    promotionOpportunities: products
      .filter(p => p.turnoverRate < avgTurnoverRate && p.profitMargin > 15 && p.currentStock > 0)
      .sort((a, b) => (b.profitMargin - b.turnoverRate) - (a.profitMargin - a.turnoverRate))
      .slice(0, 5)
      .map(p => ({
        name: p.name,
        sku: p.sku,
        category: p.category,
        profitMargin: p.profitMargin,
        turnoverRate: p.turnoverRate,
        currentStock: p.currentStock,
        suggestedDiscount: Math.min(20, Math.max(5, p.profitMargin / 3))
      })),

    // Sales Velocity Trends
    salesVelocity: salesVelocityData,

    // Bundle Suggestions
    bundleSuggestions: bundleSuggestions,

    // Seasonal Trends
    seasonalTrends: seasonalTrends,

    // Summary metrics
    summary: {
      totalProducts: products.length,
      fastMovingCount: products.filter(p => p.turnoverRate > avgTurnoverRate).length,
      slowMovingCount: products.filter(p => p.turnoverRate < avgTurnoverRate * 0.5).length,
      deadStockCount: products.filter(p => p.daysSinceLastSale >= 30).length,
      highMarginCount: products.filter(p => p.profitMargin > avgProfitMargin).length,
      lowMarginCount: products.filter(p => p.profitMargin < avgProfitMargin * 0.5).length,
      increasingTrendCount: salesVelocityData.increasing.length,
      decreasingTrendCount: salesVelocityData.decreasing.length
    }
  };
}

function calculateSalesVelocity(products: any[]) {
  const productsWithTrend = products.map(product => {
    const sales = product.transactionItems || [];
    if (sales.length < 2) {
      return { ...product, trend: 'stable', trendValue: 0 };
    }

    // Split sales into two periods (first half vs second half)
    const sortedSales = sales.sort((a: any, b: any) =>
      new Date(a.transaction.createdAt).getTime() - new Date(b.transaction.createdAt).getTime()
    );

    const midPoint = Math.floor(sortedSales.length / 2);
    const firstHalf = sortedSales.slice(0, midPoint);
    const secondHalf = sortedSales.slice(midPoint);

    const firstHalfTotal = firstHalf.reduce((sum: number, item: any) => sum + Number(item.quantity), 0);
    const secondHalfTotal = secondHalf.reduce((sum: number, item: any) => sum + Number(item.quantity), 0);

    // Calculate trend percentage
    const trendValue = firstHalfTotal > 0
      ? ((secondHalfTotal - firstHalfTotal) / firstHalfTotal) * 100
      : 0;

    let trend = 'stable';
    if (trendValue > 20) trend = 'increasing';
    else if (trendValue < -20) trend = 'decreasing';

    return { ...product, trend, trendValue };
  });

  return {
    increasing: productsWithTrend
      .filter(p => p.trend === 'increasing')
      .sort((a, b) => b.trendValue - a.trendValue)
      .slice(0, 5)
      .map(p => ({
        name: p.name,
        sku: p.sku,
        category: p.category,
        trendValue: p.trendValue,
        currentTurnover: p.turnoverRate,
        totalSold: p.totalQuantitySold
      })),
    decreasing: productsWithTrend
      .filter(p => p.trend === 'decreasing')
      .sort((a, b) => a.trendValue - b.trendValue)
      .slice(0, 5)
      .map(p => ({
        name: p.name,
        sku: p.sku,
        category: p.category,
        trendValue: Math.abs(p.trendValue),
        currentTurnover: p.turnoverRate,
        totalSold: p.totalQuantitySold
      })),
    stable: productsWithTrend
      .filter(p => p.trend === 'stable')
      .slice(0, 3)
      .map(p => ({
        name: p.name,
        sku: p.sku,
        category: p.category,
        currentTurnover: p.turnoverRate,
        totalSold: p.totalQuantitySold
      }))
  };
}

function calculateBundleSuggestions(products: any[]) {
  // Create a map of transactions and their products
  const transactionProducts = new Map();

  products.forEach(product => {
    (product.transactionItems || []).forEach((item: any) => {
      const transactionId = item.transaction.id;
      if (!transactionProducts.has(transactionId)) {
        transactionProducts.set(transactionId, []);
      }
      transactionProducts.get(transactionId).push({
        productId: product.id,
        name: product.name,
        sku: product.sku,
        category: product.category?.name || 'Uncategorized',
        quantity: Number(item.quantity)
      });
    });
  });

  // Find product pairs that appear together
  const productPairs = new Map();

  transactionProducts.forEach(transactionItems => {
    if (transactionItems.length > 1) {
      for (let i = 0; i < transactionItems.length; i++) {
        for (let j = i + 1; j < transactionItems.length; j++) {
          const product1 = transactionItems[i];
          const product2 = transactionItems[j];

          // Create a consistent key for the pair
          const pairKey = [product1.productId, product2.productId].sort().join('-');

          if (!productPairs.has(pairKey)) {
            productPairs.set(pairKey, {
              product1,
              product2,
              frequency: 0,
              totalQuantity1: 0,
              totalQuantity2: 0
            });
          }

          const pair = productPairs.get(pairKey);
          pair.frequency++;
          pair.totalQuantity1 += product1.quantity;
          pair.totalQuantity2 += product2.quantity;
        }
      }
    }
  });

  // Sort by frequency and return top suggestions
  const sortedPairs = Array.from(productPairs.values())
    .filter(pair => pair.frequency >= 2) // Only pairs that appear together at least twice
    .sort((a, b) => b.frequency - a.frequency)
    .slice(0, 5);

  return sortedPairs.map(pair => ({
    product1: {
      name: pair.product1.name,
      sku: pair.product1.sku,
      category: pair.product1.category
    },
    product2: {
      name: pair.product2.name,
      sku: pair.product2.sku,
      category: pair.product2.category
    },
    frequency: pair.frequency,
    avgQuantity1: Math.round(pair.totalQuantity1 / pair.frequency),
    avgQuantity2: Math.round(pair.totalQuantity2 / pair.frequency),
    confidence: Math.min(95, pair.frequency * 15) // Simple confidence score
  }));
}

function calculateSeasonalTrends(products: any[]) {
  const seasonalData = products.map(product => {
    const sales = product.transactionItems || [];

    // Group sales by month
    const monthlyData = new Map();

    sales.forEach((item: any) => {
      const date = new Date(item.transaction.createdAt);
      const monthKey = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;

      if (!monthlyData.has(monthKey)) {
        monthlyData.set(monthKey, { quantity: 0, revenue: 0 });
      }

      const monthData = monthlyData.get(monthKey);
      monthData.quantity += Number(item.quantity);
      monthData.revenue += Number(item.subtotal);
    });

    // Calculate seasonal performance
    const months = Array.from(monthlyData.entries()).map(([month, data]) => ({
      month,
      quantity: data.quantity,
      revenue: data.revenue
    }));

    // Find best and worst performing months
    const sortedByQuantity = [...months].sort((a, b) => b.quantity - a.quantity);
    const bestMonth = sortedByQuantity[0];
    const worstMonth = sortedByQuantity[sortedByQuantity.length - 1];

    // Calculate seasonal variance
    const avgQuantity = months.reduce((sum, m) => sum + m.quantity, 0) / months.length;
    const variance = months.reduce((sum, m) => sum + Math.pow(m.quantity - avgQuantity, 2), 0) / months.length;
    const seasonalityScore = Math.sqrt(variance) / avgQuantity * 100;

    return {
      ...product,
      monthlyData: months,
      bestMonth,
      worstMonth,
      seasonalityScore,
      isHighlySeasonal: seasonalityScore > 50
    };
  });

  return {
    highlySeasonal: seasonalData
      .filter(p => p.isHighlySeasonal && p.monthlyData.length > 1)
      .sort((a, b) => b.seasonalityScore - a.seasonalityScore)
      .slice(0, 5)
      .map(p => ({
        name: p.name,
        sku: p.sku,
        category: p.category,
        seasonalityScore: Math.round(p.seasonalityScore),
        bestMonth: p.bestMonth?.month,
        bestMonthQuantity: p.bestMonth?.quantity || 0,
        worstMonth: p.worstMonth?.month,
        worstMonthQuantity: p.worstMonth?.quantity || 0,
        monthlyData: p.monthlyData
      })),
    stable: seasonalData
      .filter(p => !p.isHighlySeasonal && p.monthlyData.length > 1)
      .slice(0, 3)
      .map(p => ({
        name: p.name,
        sku: p.sku,
        category: p.category,
        seasonalityScore: Math.round(p.seasonalityScore),
        avgMonthlyQuantity: Math.round(p.monthlyData.reduce((sum: number, m: any) => sum + m.quantity, 0) / p.monthlyData.length)
      })),
    summary: {
      totalAnalyzed: seasonalData.filter(p => p.monthlyData.length > 1).length,
      highlySeasonalCount: seasonalData.filter(p => p.isHighlySeasonal).length,
      stableCount: seasonalData.filter(p => !p.isHighlySeasonal).length
    }
  };
}
