import { NextRequest, NextResponse } from 'next/server';
import { createBackup, restoreBackup, listBackups, deleteBackup } from '@/lib/backup/db-backup';
import { jwtVerify } from "jose";
import { prisma } from '@/auth';

// Helper function to format file size
function formatFileSize(bytes: number): string {
  if (bytes < 1024) return bytes + ' bytes';
  else if (bytes < 1024 * 1024) return (bytes / 1024).toFixed(2) + ' KB';
  else if (bytes < 1024 * 1024 * 1024) return (bytes / (1024 * 1024)).toFixed(2) + ' MB';
  else return (bytes / (1024 * 1024 * 1024)).toFixed(2) + ' GB';
}

// Helper function to verify authentication token
async function verifyAuthToken(request: NextRequest) {
  // Get the session token from cookies
  const token = request.cookies.get("session-token");

  // Check if token exists
  if (!token) {
    return {
      authenticated: false,
      error: "Unauthorized. You must be logged in to access backup functionality.",
      status: 401
    };
  }

  // Verify the token
  try {
    const result = await jwtVerify(
      token.value,
      new TextEncoder().encode(process.env.NEXTAUTH_SECRET || "fallback-secret")
    );

    // Only allow SUPER_ADMIN and FINANCE_ADMIN to access backup functionality
    if (!result.payload.role || !["SUPER_ADMIN", "FINANCE_ADMIN"].includes(result.payload.role as string)) {
      return {
        authenticated: false,
        error: "Unauthorized. You don't have permission to access backup functionality.",
        status: 403
      };
    }

    return {
      authenticated: true,
      user: {
        id: result.payload.id as string,
        name: result.payload.name as string,
        email: result.payload.email as string,
        role: result.payload.role as string
      }
    };
  } catch (error) {
    console.error("Token verification failed:", error);
    return {
      authenticated: false,
      error: "Unauthorized. Invalid authentication token.",
      status: 401
    };
  }
}

// GET /api/backup - List all backups
export async function GET(request: NextRequest) {
  try {
    // Check authentication and authorization
    const auth = await verifyAuthToken(request);

    if (!auth.authenticated) {
      return NextResponse.json(
        { error: auth.error },
        { status: auth.status }
      );
    }

    const backups = listBackups();
    return NextResponse.json({ backups });
  } catch (error) {
    console.error('Error listing backups:', error);
    return NextResponse.json(
      { error: 'Failed to list backups', message: error.message },
      { status: 500 }
    );
  }
}

// POST /api/backup - Create a new backup
export async function POST(request: NextRequest) {
  try {
    // Check authentication and authorization
    const auth = await verifyAuthToken(request);

    if (!auth.authenticated) {
      return NextResponse.json(
        { error: auth.error },
        { status: auth.status }
      );
    }

    const data = await request.json();
    const { comment } = data;

    const result = await createBackup({ comment });

    // Log the backup creation in activity logs
    await prisma.activityLog.create({
      data: {
        userId: auth.user.id,
        action: "CREATE_BACKUP",
        details: `Created database backup: ${result.filePath} (${formatFileSize(result.size)})${comment ? ` - Comment: ${comment}` : ''}`,
      },
    });

    return NextResponse.json({ success: true, backup: result });
  } catch (error) {
    console.error('Error creating backup:', error);
    return NextResponse.json(
      { error: 'Failed to create backup', message: error.message },
      { status: 500 }
    );
  }
}

// DELETE /api/backup - Delete a backup
export async function DELETE(request: NextRequest) {
  try {
    // Check authentication and authorization
    const auth = await verifyAuthToken(request);

    if (!auth.authenticated) {
      return NextResponse.json(
        { error: auth.error },
        { status: auth.status }
      );
    }

    const data = await request.json();
    const { backupPath } = data;

    if (!backupPath) {
      return NextResponse.json(
        { error: 'Backup path is required' },
        { status: 400 }
      );
    }

    deleteBackup(backupPath);

    // Log the backup deletion in activity logs
    await prisma.activityLog.create({
      data: {
        userId: auth.user.id,
        action: "DELETE_BACKUP",
        details: `Deleted database backup: ${backupPath}`,
      },
    });

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error deleting backup:', error);
    return NextResponse.json(
      { error: 'Failed to delete backup', message: error.message },
      { status: 500 }
    );
  }
}
