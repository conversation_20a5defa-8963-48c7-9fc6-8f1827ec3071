"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { toast } from "sonner";
import { MainLayout } from "@/components/layout/MainLayout";
import { PageHeader } from "@/components/layout/PageHeader";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
  CardFooter,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Loader2,
  Monitor,
  HardDrive,
  Link as LinkIcon,
  Link2Off,
  AlertCircle,
  Plus,
} from "lucide-react";
import { useAuth } from "@/hooks/use-auth";

interface Terminal {
  id: string;
  name: string;
  ipAddress?: string;
  location?: string;
  isActive: boolean;
  drawerId?: string;
  drawer?: {
    id: string;
    name: string;
    location?: string;
    isActive: boolean;
  };
}

interface CashDrawer {
  id: string;
  name: string;
  location?: string;
  isActive: boolean;
  terminal?: {
    id: string;
    name: string;
  };
}

export default function TerminalMapPage() {
  const router = useRouter();
  const { user, isLoading: authLoading } = useAuth();
  const [terminals, setTerminals] = useState<Terminal[]>([]);
  const [drawers, setDrawers] = useState<CashDrawer[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  // Assignment dialog state
  const [assignmentDialogOpen, setAssignmentDialogOpen] = useState(false);
  const [selectedTerminal, setSelectedTerminal] = useState<Terminal | null>(null);
  const [selectedDrawerId, setSelectedDrawerId] = useState<string>("");
  const [isAssigning, setIsAssigning] = useState(false);

  // Fetch terminals and drawers
  const fetchData = async () => {
    setIsLoading(true);

    try {
      // Fetch terminals
      const terminalsResponse = await fetch("/api/terminals");
      const terminalsData = await terminalsResponse.json();

      if (!terminalsResponse.ok) {
        throw new Error(terminalsData.error || "Failed to fetch terminals");
      }

      // Fetch drawers
      const drawersResponse = await fetch("/api/cash-drawers");
      const drawersData = await drawersResponse.json();

      if (!drawersResponse.ok) {
        throw new Error(drawersData.error || "Failed to fetch cash drawers");
      }

      setTerminals(terminalsData.terminals);
      setDrawers(drawersData.drawers);
    } catch (err) {
      toast.error(err instanceof Error ? err.message : "An unknown error occurred");
      console.error("Error fetching data:", err);
    } finally {
      setIsLoading(false);
    }
  };

  // Load data on mount
  useEffect(() => {
    fetchData();
  }, []);

  // Open assignment dialog
  const openAssignmentDialog = (terminal: Terminal) => {
    setSelectedTerminal(terminal);
    setSelectedDrawerId("");
    setAssignmentDialogOpen(true);
  };

  // Close assignment dialog
  const closeAssignmentDialog = () => {
    setAssignmentDialogOpen(false);
    setSelectedTerminal(null);
    setSelectedDrawerId("");
  };

  // Handle drawer assignment
  const handleAssignDrawer = async () => {
    if (!selectedTerminal || !selectedDrawerId) {
      toast.error("Please select a drawer to assign");
      return;
    }

    setIsAssigning(true);

    try {
      const response = await fetch(`/api/terminals/${selectedTerminal.id}/assign-drawer`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          drawerId: selectedDrawerId,
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || "Failed to assign drawer");
      }

      toast.success(`Drawer assigned to ${selectedTerminal.name} successfully`);
      closeAssignmentDialog();
      fetchData(); // Refresh the data
    } catch (err) {
      toast.error(err instanceof Error ? err.message : "An unknown error occurred");
      console.error("Error assigning drawer:", err);
    } finally {
      setIsAssigning(false);
    }
  };

  // Filter active terminals and drawers
  const activeTerminals = terminals.filter((t) => t.isActive);
  const activeDrawers = drawers.filter((d) => d.isActive);

  // Get unassigned drawers
  const unassignedDrawers = activeDrawers.filter(
    (d) => !terminals.some((t) => t.drawerId === d.id)
  );

  // Get terminals without drawers
  const terminalsWithoutDrawers = activeTerminals.filter((t) => !t.drawerId);

  return (
    <MainLayout>
      <PageHeader
        heading="Terminal Map"
        subheading="Visual map of terminals and their assigned cash drawers"
        actions={
          <Button variant="outline" onClick={fetchData}>
            Refresh
          </Button>
        }
      />

      {isLoading ? (
        <div className="flex justify-center items-center py-8">
          <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
        </div>
      ) : (
        <div className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {activeTerminals.map((terminal) => (
              <Card key={terminal.id} className={terminal.drawer ? "border-blue-200" : ""}>
                <CardHeader className="pb-2">
                  <div className="flex justify-between items-start">
                    <CardTitle className="flex items-center">
                      <Monitor className="h-5 w-5 mr-2" />
                      {terminal.name}
                    </CardTitle>
                    <Badge variant={terminal.isActive ? "success" : "destructive"}>
                      {terminal.isActive ? "Active" : "Inactive"}
                    </Badge>
                  </div>
                  <CardDescription>{terminal.location || "No location specified"}</CardDescription>
                </CardHeader>
                <CardContent>
                  {terminal.ipAddress && (
                    <div className="text-sm mb-2">
                      <span className="text-muted-foreground">IP:</span> {terminal.ipAddress}
                    </div>
                  )}

                  <div className="mt-4 pt-4 border-t">
                    <div className="text-sm font-medium mb-2">Assigned Drawer:</div>
                    {terminal.drawer ? (
                      <div className="flex items-center p-3 bg-blue-50 rounded-md">
                        <LinkIcon className="h-5 w-5 mr-2 text-blue-500" />
                        <div>
                          <div className="font-medium">{terminal.drawer.name}</div>
                          <div className="text-sm text-muted-foreground">
                            {terminal.drawer.location || "No location specified"}
                          </div>
                        </div>
                      </div>
                    ) : (
                      <div className="flex items-center justify-between p-3 bg-gray-50 rounded-md">
                        <div className="flex items-center">
                          <Link2Off className="h-5 w-5 mr-2 text-gray-400" />
                          <div className="text-muted-foreground">No drawer assigned</div>
                        </div>
                        {unassignedDrawers.length > 0 && (
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => openAssignmentDialog(terminal)}
                            className="ml-2"
                          >
                            <Plus className="h-3 w-3 mr-1" />
                            Assign
                          </Button>
                        )}
                      </div>
                    )}
                  </div>
                </CardContent>
                <CardFooter className="pt-0">
                  <Button
                    variant="outline"
                    size="sm"
                    className="w-full"
                    onClick={() => router.push(`/admin/terminals/${terminal.id}`)}
                  >
                    View Details
                  </Button>
                </CardFooter>
              </Card>
            ))}
          </div>

          {/* Unassigned Drawers */}
          {unassignedDrawers.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle>Unassigned Drawers</CardTitle>
                <CardDescription>
                  Cash drawers that are not assigned to any terminal
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {unassignedDrawers.map((drawer) => (
                    <div
                      key={drawer.id}
                      className="flex items-center justify-between p-3 border rounded-md"
                    >
                      <div className="flex items-center">
                        <HardDrive className="h-5 w-5 mr-2 text-gray-400" />
                        <div>
                          <div className="font-medium">{drawer.name}</div>
                          <div className="text-sm text-muted-foreground">
                            {drawer.location || "No location specified"}
                          </div>
                        </div>
                      </div>
                      {terminalsWithoutDrawers.length > 0 ? (
                        <Select
                          onValueChange={(terminalId) => {
                            const terminal = terminalsWithoutDrawers.find(
                              (t) => t.id === terminalId
                            );
                            if (terminal) {
                              setSelectedTerminal(terminal);
                              setSelectedDrawerId(drawer.id);
                              setAssignmentDialogOpen(true);
                            }
                          }}
                        >
                          <SelectTrigger className="w-32">
                            <SelectValue placeholder="Assign to..." />
                          </SelectTrigger>
                          <SelectContent>
                            {terminalsWithoutDrawers.map((terminal) => (
                              <SelectItem key={terminal.id} value={terminal.id}>
                                {terminal.name}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      ) : (
                        <div className="text-sm text-muted-foreground italic">
                          No available terminals
                        </div>
                      )}
                    </div>
                  ))}
                </div>

                {/* Message when no terminals available for assignment */}
                {terminalsWithoutDrawers.length === 0 && (
                  <div className="mt-4 p-4 bg-blue-50 border border-blue-200 rounded-md">
                    <div className="flex items-start">
                      <AlertCircle className="h-5 w-5 text-blue-600 mr-3 mt-0.5 flex-shrink-0" />
                      <div className="flex-1">
                        <h4 className="text-sm font-medium text-blue-800 mb-1">
                          No Available Terminals for Assignment
                        </h4>
                        <p className="text-sm text-blue-700 mb-3">
                          All existing terminals already have assigned drawers. To assign these
                          unassigned drawers, you need to create new terminals.
                        </p>
                        <Button
                          size="sm"
                          onClick={() =>
                            router.push("/admin/terminals?create=true&returnTo=terminal-map")
                          }
                          className="bg-blue-600 hover:bg-blue-700 text-white"
                        >
                          <Plus className="h-4 w-4 mr-1" />
                          Add New Terminal
                        </Button>
                      </div>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          )}

          {/* Terminals Without Drawers */}
          {terminalsWithoutDrawers.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle>Terminals Without Drawers</CardTitle>
                <CardDescription>
                  Terminals that do not have an assigned cash drawer
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {terminalsWithoutDrawers.map((terminal) => (
                    <div key={terminal.id} className="flex items-center p-3 border rounded-md">
                      <Monitor className="h-5 w-5 mr-2 text-gray-400" />
                      <div>
                        <div className="font-medium">{terminal.name}</div>
                        <div className="text-sm text-muted-foreground">
                          {terminal.location || "No location specified"}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Warning for Inactive Items */}
          {(terminals.some((t) => !t.isActive) || drawers.some((d) => !d.isActive)) && (
            <Card className="border-yellow-200 bg-yellow-50">
              <CardHeader className="pb-2">
                <CardTitle className="flex items-center text-yellow-700">
                  <AlertCircle className="h-5 w-5 mr-2" />
                  Inactive Items
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-yellow-700 mb-2">
                  Some terminals or drawers are marked as inactive and are not shown on the map.
                </p>
                <div className="flex space-x-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => router.push("/admin/terminals")}
                  >
                    Manage Terminals
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => router.push("/admin/cash-drawers")}
                  >
                    Manage Drawers
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      )}

      {/* Assignment Dialog */}
      <Dialog open={assignmentDialogOpen} onOpenChange={setAssignmentDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Assign Drawer to Terminal</DialogTitle>
            <DialogDescription>
              Select a drawer to assign to {selectedTerminal?.name}
            </DialogDescription>
          </DialogHeader>
          <div className="py-4">
            <Select value={selectedDrawerId} onValueChange={setSelectedDrawerId}>
              <SelectTrigger>
                <SelectValue placeholder="Select a drawer" />
              </SelectTrigger>
              <SelectContent>
                {unassignedDrawers.map((drawer) => (
                  <SelectItem key={drawer.id} value={drawer.id}>
                    {drawer.name}
                    {drawer.location && ` (${drawer.location})`}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={closeAssignmentDialog}>
              Cancel
            </Button>
            <Button onClick={handleAssignDrawer} disabled={isAssigning || !selectedDrawerId}>
              {isAssigning && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              Assign Drawer
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </MainLayout>
  );
}
