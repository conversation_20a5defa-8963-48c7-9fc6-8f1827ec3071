import { NextResponse } from "next/server";
import { PrismaClient } from "@/generated/prisma";

// Create a direct connection to the database for raw SQL operations
const directPrisma = new PrismaClient();

/**
 * API endpoint to update the database schema
 * This endpoint attempts to add the DEVELOPER role to the UserRole enum
 * It's used during the initial setup process
 */
export async function GET() {
  try {
    // Try to add the DEVELOPER role to the UserRole enum
    await directPrisma.$executeRawUnsafe(`
      ALTER TYPE "UserRole" ADD VALUE IF NOT EXISTS 'DEVELOPER';
    `);

    console.log("Schema update: Added DEVELOPER to UserRole enum");

    return NextResponse.json({
      success: true,
      message: "Schema updated successfully",
    });
  } catch (error) {
    console.error("Error updating schema:", error);
    return NextResponse.json({
      success: false,
      message: "Failed to update schema",
      error: error.message,
    }, { status: 500 });
  } finally {
    await directPrisma.$disconnect();
  }
}
