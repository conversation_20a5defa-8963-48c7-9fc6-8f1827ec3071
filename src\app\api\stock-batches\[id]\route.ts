import { NextRequest, NextResponse } from "next/server";
import { z } from "zod";
import { prisma } from "@/lib/prisma";
import { verifyAuthToken } from "@/lib/auth-utils";

// GET /api/stock-batches/[id] - Get specific stock batch
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication
    const auth = await verifyAuthToken(request);

    if (!auth.authenticated) {
      return NextResponse.json(
        { error: auth.error },
        { status: auth.status }
      );
    }

    const batchId = params.id;

    // Get stock batch with full details
    const stockBatch = await prisma.stockBatch.findUnique({
      where: { id: batchId },
      include: {
        product: {
          include: {
            category: true,
            unit: true,
          }
        },
        productSupplier: {
          include: {
            supplier: true,
          }
        },
        purchaseOrder: {
          select: {
            id: true,
            orderDate: true,
            status: true,
            total: true,
          }
        },
        warehouseStock: {
          select: {
            id: true,
            quantity: true,
            minThreshold: true,
            maxThreshold: true,
          }
        },
        storeStock: {
          select: {
            id: true,
            quantity: true,
            minThreshold: true,
            maxThreshold: true,
          }
        },
        transactionItems: {
          include: {
            transaction: {
              select: {
                id: true,
                transactionDate: true,
                total: true,
                status: true,
              }
            }
          },
          orderBy: {
            transaction: {
              transactionDate: 'desc'
            }
          },
          take: 10 // Last 10 transactions
        },
        stockAdjustments: {
          include: {
            user: {
              select: {
                id: true,
                name: true,
              }
            }
          },
          orderBy: {
            date: 'desc'
          },
          take: 10 // Last 10 adjustments
        },
        stockHistory: {
          include: {
            user: {
              select: {
                id: true,
                name: true,
              }
            }
          },
          orderBy: {
            date: 'desc'
          },
          take: 20 // Last 20 history entries
        }
      }
    });

    if (!stockBatch) {
      return NextResponse.json(
        { error: "Stock batch not found" },
        { status: 404 }
      );
    }

    // Calculate additional metrics
    const metrics = {
      totalSold: Number(stockBatch.quantity) - Number(stockBatch.remainingQuantity),
      sellThroughRate: Number(stockBatch.quantity) > 0 
        ? ((Number(stockBatch.quantity) - Number(stockBatch.remainingQuantity)) / Number(stockBatch.quantity)) * 100 
        : 0,
      daysInStock: Math.ceil((new Date().getTime() - stockBatch.receivedDate.getTime()) / (1000 * 60 * 60 * 24)),
      daysUntilExpiry: stockBatch.expiryDate 
        ? Math.ceil((stockBatch.expiryDate.getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24))
        : null,
      currentValue: Number(stockBatch.remainingQuantity) * Number(stockBatch.purchasePrice),
      totalTransactions: stockBatch.transactionItems.length,
      totalAdjustments: stockBatch.stockAdjustments.length,
    };

    return NextResponse.json({
      stockBatch,
      metrics
    });
  } catch (error) {
    console.error("Error fetching stock batch:", error);
    return NextResponse.json(
      { error: "Failed to fetch stock batch", message: (error as Error).message },
      { status: 500 }
    );
  }
}

// PUT /api/stock-batches/[id] - Update stock batch
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication
    const auth = await verifyAuthToken(request);

    if (!auth.authenticated) {
      return NextResponse.json(
        { error: auth.error },
        { status: auth.status }
      );
    }

    // Check permissions
    const hasPermission = ["SUPER_ADMIN", "WAREHOUSE_ADMIN"].includes(auth.user.role);
    if (!hasPermission) {
      return NextResponse.json(
        { error: "Unauthorized - Insufficient permissions" },
        { status: 403 }
      );
    }

    const batchId = params.id;

    // Verify batch exists
    const existingBatch = await prisma.stockBatch.findUnique({
      where: { id: batchId },
      include: {
        product: { select: { name: true } },
        productSupplier: {
          include: {
            supplier: { select: { name: true } }
          }
        }
      }
    });

    if (!existingBatch) {
      return NextResponse.json(
        { error: "Stock batch not found" },
        { status: 404 }
      );
    }

    // Get request body
    const body = await request.json();

    // Validate data
    const updateSchema = z.object({
      batchNumber: z.string().optional(),
      expiryDate: z.string().optional().transform(val => val ? new Date(val) : undefined),
      status: z.enum(['ACTIVE', 'EXPIRED', 'RECALLED', 'SOLD_OUT']).optional(),
      notes: z.string().optional(),
    });

    const validationResult = updateSchema.safeParse(body);
    if (!validationResult.success) {
      return NextResponse.json(
        { error: "Validation failed", issues: validationResult.error.issues },
        { status: 400 }
      );
    }

    const data = validationResult.data;

    // Update the batch
    const updatedBatch = await prisma.stockBatch.update({
      where: { id: batchId },
      data: {
        ...data,
        updatedAt: new Date(),
      },
      include: {
        product: {
          select: {
            id: true,
            name: true,
            sku: true,
          }
        },
        productSupplier: {
          include: {
            supplier: {
              select: {
                id: true,
                name: true,
              }
            }
          }
        }
      }
    });

    // Log activity
    await prisma.activityLog.create({
      data: {
        userId: auth.user.id,
        action: "UPDATE_STOCK_BATCH",
        details: `Updated stock batch ${updatedBatch.batchNumber} for ${existingBatch.product.name}`,
      },
    });

    return NextResponse.json({
      message: "Stock batch updated successfully",
      stockBatch: updatedBatch
    });
  } catch (error) {
    console.error("Error updating stock batch:", error);
    return NextResponse.json(
      { error: "Failed to update stock batch", message: (error as Error).message },
      { status: 500 }
    );
  }
}

// DELETE /api/stock-batches/[id] - Delete stock batch (only if no transactions)
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication
    const auth = await verifyAuthToken(request);

    if (!auth.authenticated) {
      return NextResponse.json(
        { error: auth.error },
        { status: auth.status }
      );
    }

    // Check permissions
    const hasPermission = ["SUPER_ADMIN"].includes(auth.user.role);
    if (!hasPermission) {
      return NextResponse.json(
        { error: "Unauthorized - Only SUPER_ADMIN can delete stock batches" },
        { status: 403 }
      );
    }

    const batchId = params.id;

    // Verify batch exists and check for dependencies
    const existingBatch = await prisma.stockBatch.findUnique({
      where: { id: batchId },
      include: {
        product: { select: { name: true } },
        transactionItems: { select: { id: true } },
        stockAdjustments: { select: { id: true } },
        stockHistory: { select: { id: true } }
      }
    });

    if (!existingBatch) {
      return NextResponse.json(
        { error: "Stock batch not found" },
        { status: 404 }
      );
    }

    // Check if batch has any transactions or adjustments
    if (existingBatch.transactionItems.length > 0 || 
        existingBatch.stockAdjustments.length > 0) {
      return NextResponse.json(
        { 
          error: "Cannot delete stock batch", 
          message: "This batch has associated transactions or adjustments. Please set status to RECALLED instead." 
        },
        { status: 409 }
      );
    }

    // Delete the batch and related records in a transaction
    await prisma.$transaction(async (tx) => {
      // Delete stock history entries
      await tx.stockHistory.deleteMany({
        where: { batchId }
      });

      // Update stock quantities
      if (existingBatch.warehouseStockId) {
        await tx.warehouseStock.update({
          where: { id: existingBatch.warehouseStockId },
          data: {
            quantity: { decrement: Number(existingBatch.remainingQuantity) },
            lastUpdated: new Date(),
          }
        });
      }

      if (existingBatch.storeStockId) {
        await tx.storeStock.update({
          where: { id: existingBatch.storeStockId },
          data: {
            quantity: { decrement: Number(existingBatch.remainingQuantity) },
            lastUpdated: new Date(),
          }
        });
      }

      // Delete the batch
      await tx.stockBatch.delete({
        where: { id: batchId }
      });
    });

    // Log activity
    await prisma.activityLog.create({
      data: {
        userId: auth.user.id,
        action: "DELETE_STOCK_BATCH",
        details: `Deleted stock batch ${existingBatch.batchNumber} for ${existingBatch.product.name}`,
      },
    });

    return NextResponse.json({
      message: "Stock batch deleted successfully"
    });
  } catch (error) {
    console.error("Error deleting stock batch:", error);
    return NextResponse.json(
      { error: "Failed to delete stock batch", message: (error as Error).message },
      { status: 500 }
    );
  }
}
