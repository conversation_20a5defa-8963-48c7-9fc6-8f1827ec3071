"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Plus } from "lucide-react";
import { toast } from "sonner";
import { usePOS } from "@/contexts/POSContext";

interface Customer {
  id: string;
  name: string;
  phone?: string;
  email?: string;
  customerType: "REGULAR" | "FRIEND" | "FAMILY";
}

export function CustomerSelect() {
  const [customers, setCustomers] = useState<Customer[]>([]);
  const [selectedCustomer, setSelectedCustomer] = useState<Customer | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [newCustomerName, setNewCustomerName] = useState("");
  const [newCustomerPhone, setNewCustomerPhone] = useState("");
  const [isCreating, setIsCreating] = useState(false);
  const { focusSearchInput } = usePOS();

  // Load customers on mount
  useEffect(() => {
    fetchCustomers();

    // Load selected customer from localStorage
    if (typeof window !== "undefined") {
      const savedCustomer = localStorage.getItem("pos_selected_customer");
      if (savedCustomer) {
        try {
          const parsedCustomer = JSON.parse(savedCustomer);
          setSelectedCustomer(parsedCustomer);
        } catch (e) {
          console.error("Error parsing saved customer:", e);
          localStorage.removeItem("pos_selected_customer");
        }
      }
    }
  }, []);

  // Save selected customer to localStorage
  useEffect(() => {
    if (typeof window !== "undefined") {
      if (selectedCustomer) {
        localStorage.setItem("pos_selected_customer", JSON.stringify(selectedCustomer));
      } else {
        localStorage.removeItem("pos_selected_customer");
      }
    }
  }, [selectedCustomer]);

  const fetchCustomers = async () => {
    setIsLoading(true);
    try {
      const response = await fetch("/api/customers");
      if (response.ok) {
        const data = await response.json();
        setCustomers(data.customers || []);
      }
    } catch (error) {
      console.error("Error fetching customers:", error);
      toast.error("Failed to load customers");
    } finally {
      setIsLoading(false);
    }
  };

  const handleCustomerSelect = (customerId: string) => {
    if (customerId === "walk-in") {
      setSelectedCustomer(null);
    } else {
      const customer = customers.find((c) => c.id === customerId);
      setSelectedCustomer(customer || null);
    }

    // Auto-focus search input after customer selection
    focusSearchInput();
  };

  const handleCreateCustomer = async () => {
    if (!newCustomerName.trim()) {
      toast.error("Customer name is required");
      return;
    }

    setIsCreating(true);
    try {
      const response = await fetch("/api/customers", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          name: newCustomerName.trim(),
          phone: newCustomerPhone.trim() || undefined,
        }),
      });

      if (response.ok) {
        const data = await response.json();
        const newCustomer = data.customer;

        // Add to customers list
        setCustomers((prev) => [...prev, newCustomer]);

        // Auto-select the new customer
        setSelectedCustomer(newCustomer);

        // Reset form and close dialog
        setNewCustomerName("");
        setNewCustomerPhone("");
        setDialogOpen(false);

        toast.success("Customer created successfully");

        // Auto-focus search input after customer creation (delayed to avoid toast interference)
        focusSearchInput(400);
      } else {
        const errorData = await response.json();
        toast.error(errorData.error || "Failed to create customer");
      }
    } catch (error) {
      console.error("Error creating customer:", error);
      toast.error("Failed to create customer");
    } finally {
      setIsCreating(false);
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Customer</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <Select
            value={selectedCustomer?.id || "walk-in"}
            onValueChange={handleCustomerSelect}
            disabled={isLoading}
          >
            <SelectTrigger>
              <SelectValue placeholder="Select customer" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="walk-in">Walk-in Customer</SelectItem>
              {customers.map((customer) => (
                <SelectItem key={customer.id} value={customer.id}>
                  {customer.name} {customer.phone && `(${customer.phone})`}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
            <DialogTrigger asChild>
              <Button variant="outline" className="w-full">
                <Plus className="h-4 w-4 mr-2" />
                Quick Add Customer
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Add New Customer</DialogTitle>
              </DialogHeader>
              <div className="space-y-4">
                <div>
                  <Label htmlFor="customerName">Name *</Label>
                  <Input
                    id="customerName"
                    value={newCustomerName}
                    onChange={(e) => setNewCustomerName(e.target.value)}
                    placeholder="Enter customer name"
                  />
                </div>
                <div>
                  <Label htmlFor="customerPhone">Phone (optional)</Label>
                  <Input
                    id="customerPhone"
                    value={newCustomerPhone}
                    onChange={(e) => setNewCustomerPhone(e.target.value)}
                    placeholder="Enter phone number"
                  />
                </div>
                <div className="flex space-x-2">
                  <Button
                    onClick={handleCreateCustomer}
                    disabled={isCreating || !newCustomerName.trim()}
                    className="flex-1"
                  >
                    {isCreating ? "Creating..." : "Create Customer"}
                  </Button>
                  <Button
                    variant="outline"
                    onClick={() => setDialogOpen(false)}
                    disabled={isCreating}
                  >
                    Cancel
                  </Button>
                </div>
              </div>
            </DialogContent>
          </Dialog>
        </div>
      </CardContent>
    </Card>
  );
}
