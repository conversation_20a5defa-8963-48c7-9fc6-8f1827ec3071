import { NextRequest, NextResponse } from "next/server";
import { cookies } from "next/headers";
import { jwtVerify } from "jose";

// GET /api/auth/session - Get current session
export async function GET(request: NextRequest) {
  try {
    // Get the token from the request cookies directly
    const token = request.cookies.get("session-token");

    if (!token) {
      console.log("No session token found");
      return NextResponse.json({ user: null }, { status: 200 });
    }

    // Verify the token
    const { payload } = await jwtVerify(
      token.value,
      new TextEncoder().encode(process.env.NEXTAUTH_SECRET || "fallback-secret")
    );

    // Return the user data
    return NextResponse.json({
      user: {
        id: payload.id,
        name: payload.name,
        email: payload.email,
        role: payload.role,
      },
    });
  } catch (error) {
    console.error("Session error:", error);
    return NextResponse.json({ user: null }, { status: 200 });
  }
}
