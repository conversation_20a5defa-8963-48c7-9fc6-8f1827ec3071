// This script seeds the stock tables for existing products
const { PrismaClient } = require('../src/generated/prisma');
const { faker } = require('@faker-js/faker');

const prisma = new PrismaClient();

async function main() {
  console.log('Starting to seed stock data...');

  try {
    // Get all products
    const products = await prisma.product.findMany();

    if (products.length === 0) {
      console.log('No products found. Please run seed-products.js first.');
      return;
    }

    console.log(`Found ${products.length} products. Creating stock entries...`);

    // Get admin user for stock history
    let adminUser = await prisma.user.findFirst({
      where: { role: 'SUPER_ADMIN' }
    });

    if (!adminUser) {
      console.log('No admin user found. Creating a default admin user...');
      // Create a default admin user if none exists
      adminUser = await prisma.user.create({
        data: {
          name: 'Admin User',
          email: '<EMAIL>',
          password: '$2a$10$iqJSHD.BGr0E2IxQwYgJmeP3NvhPrXAeLSaGCj6IR/XU5QtjVu5Tm', // hash for 'admin123'
          role: 'SUPER_ADMIN',
        }
      });
    }

    // Create store stock and warehouse stock for each product
    for (const product of products) {
      // Generate random stock quantities
      const storeQuantity = faker.number.int({ min: 5, max: 100 });
      const warehouseQuantity = faker.number.int({ min: 20, max: 200 });

      // Generate thresholds
      const storeMinThreshold = faker.number.int({ min: 3, max: 10 });
      const storeMaxThreshold = faker.number.int({ min: storeMinThreshold + 10, max: storeMinThreshold + 50 });

      const warehouseMinThreshold = faker.number.int({ min: 10, max: 30 });
      const warehouseMaxThreshold = faker.number.int({ min: warehouseMinThreshold + 20, max: warehouseMinThreshold + 100 });

      // Create or update store stock
      const storeStock = await prisma.storeStock.upsert({
        where: { productId: product.id },
        update: {
          quantity: storeQuantity,
          minThreshold: storeMinThreshold,
          maxThreshold: storeMaxThreshold,
          lastUpdated: new Date()
        },
        create: {
          productId: product.id,
          quantity: storeQuantity,
          minThreshold: storeMinThreshold,
          maxThreshold: storeMaxThreshold,
          lastUpdated: new Date()
        }
      });

      // Create store stock history
      await prisma.stockHistory.create({
        data: {
          productId: product.id,
          storeStockId: storeStock.id,
          previousQuantity: 0,
          newQuantity: storeQuantity,
          changeQuantity: storeQuantity,
          source: "INITIAL",
          notes: "Initial stock setup from seed script",
          userId: adminUser.id,
        }
      });

      // Create or update warehouse stock
      const warehouseStock = await prisma.warehouseStock.upsert({
        where: { productId: product.id },
        update: {
          quantity: warehouseQuantity,
          minThreshold: warehouseMinThreshold,
          maxThreshold: warehouseMaxThreshold,
          lastUpdated: new Date()
        },
        create: {
          productId: product.id,
          quantity: warehouseQuantity,
          minThreshold: warehouseMinThreshold,
          maxThreshold: warehouseMaxThreshold,
          lastUpdated: new Date()
        }
      });

      // Create warehouse stock history
      await prisma.stockHistory.create({
        data: {
          productId: product.id,
          warehouseStockId: warehouseStock.id,
          previousQuantity: 0,
          newQuantity: warehouseQuantity,
          changeQuantity: warehouseQuantity,
          source: "INITIAL",
          notes: "Initial warehouse stock setup from seed script",
          userId: adminUser.id,
        }
      });
    }

    console.log('Stock seeding completed successfully!');
  } catch (error) {
    console.error('Error seeding stock:', error);
  } finally {
    await prisma.$disconnect();
  }
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  });
