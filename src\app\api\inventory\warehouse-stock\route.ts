import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/auth";
import { z } from "zod";

// Helper function to verify the authentication token
async function verifyAuthToken(request: NextRequest) {
  // Get the session token from cookies
  const token = request.cookies.get("session-token");

  // Check if token exists
  if (!token) {
    console.log("[API] /api/inventory/warehouse-stock - No session token found");
    return {
      authenticated: false,
      error: "Unauthorized - No session token",
      status: 403,
      user: null
    };
  }

  // Verify the token
  try {
    const { payload } = await import("jose").then(({ jwtVerify }) =>
      jwtVerify(
        token.value,
        new TextEncoder().encode(process.env.NEXTAUTH_SECRET || "fallback-secret")
      )
    );

    return {
      authenticated: true,
      user: {
        id: payload.id as string,
        name: payload.name as string,
        email: payload.email as string,
        role: payload.role as string
      }
    };
  } catch (error) {
    console.error("[API] /api/inventory/warehouse-stock - JWT verification failed:", error);
    return {
      authenticated: false,
      error: "Unauthorized - Invalid token",
      status: 403,
      user: null
    };
  }
}

// GET /api/inventory/warehouse-stock - Get all warehouse stock
export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const auth = await verifyAuthToken(request);

    if (!auth.authenticated) {
      return NextResponse.json(
        { error: auth.error },
        { status: auth.status }
      );
    }

    // Check if user has permission to view inventory
    if (!auth.user || !["SUPER_ADMIN", "WAREHOUSE_ADMIN", "CASHIER"].includes(auth.user.role)) {
      return NextResponse.json(
        { error: "Unauthorized - Insufficient permissions" },
        { status: 403 }
      );
    }

    // Parse query parameters
    const searchParams = request.nextUrl.searchParams;
    const id = searchParams.get("id");
    const productId = searchParams.get("productId");
    const lowStock = searchParams.get("lowStock") === "true";
    const limit = parseInt(searchParams.get("limit") || "100");
    const page = parseInt(searchParams.get("page") || "1");
    const skip = (page - 1) * limit;

    // If id is provided, return a specific warehouse stock item
    if (id) {
      const warehouseStock = await prisma.warehouseStock.findMany({
        where: { id },
        include: {
          product: {
            include: {
              category: true,
              unit: true,
              supplier: true
            }
          }
        }
      });

      return NextResponse.json({ warehouseStock });
    }

    // Build the query
    const query: any = {};

    if (productId) {
      query.productId = productId;
    }

    if (lowStock) {
      query.quantity = {
        lte: prisma.warehouseStock.fields.minThreshold
      };
    }

    // Get total count for pagination
    const totalCount = await prisma.warehouseStock.count({
      where: query
    });

    // Get warehouse stock with product details
    const warehouseStock = await prisma.warehouseStock.findMany({
      where: query,
      include: {
        product: {
          include: {
            category: true,
            unit: true,
            supplier: true
          }
        }
      },
      skip,
      take: limit,
      orderBy: {
        lastUpdated: "desc"
      }
    });

    return NextResponse.json({
      warehouseStock,
      pagination: {
        total: totalCount,
        page,
        limit,
        pages: Math.ceil(totalCount / limit)
      }
    });
  } catch (error) {
    console.error("Error fetching warehouse stock:", error);
    return NextResponse.json(
      { error: "Failed to fetch warehouse stock", message: (error as Error).message },
      { status: 500 }
    );
  }
}

// POST /api/inventory/warehouse-stock - Create or update warehouse stock
export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const auth = await verifyAuthToken(request);

    if (!auth.authenticated) {
      return NextResponse.json(
        { error: auth.error },
        { status: auth.status }
      );
    }

    // Check if user has permission to update inventory
    if (!auth.user || !["SUPER_ADMIN", "WAREHOUSE_ADMIN"].includes(auth.user.role)) {
      return NextResponse.json(
        { error: "Unauthorized - Insufficient permissions" },
        { status: 403 }
      );
    }

    // Get request body
    const body = await request.json();

    // Validate warehouse stock data
    const warehouseStockSchema = z.object({
      productId: z.string(),
      quantity: z.number().min(0),
      minThreshold: z.number().min(0),
      maxThreshold: z.number().optional().nullable(),
    });

    const validationResult = warehouseStockSchema.safeParse(body);
    if (!validationResult.success) {
      return NextResponse.json(
        { error: "Validation failed", issues: validationResult.error.issues },
        { status: 400 }
      );
    }

    const { productId, quantity, minThreshold, maxThreshold } = validationResult.data;

    // Check if product exists
    const product = await prisma.product.findUnique({
      where: { id: productId }
    });

    if (!product) {
      return NextResponse.json(
        { error: "Product not found" },
        { status: 404 }
      );
    }

    // Create or update warehouse stock
    const warehouseStock = await prisma.warehouseStock.upsert({
      where: { productId },
      update: {
        quantity,
        minThreshold,
        maxThreshold,
        lastUpdated: new Date()
      },
      create: {
        productId,
        quantity,
        minThreshold,
        maxThreshold: maxThreshold || null,
        lastUpdated: new Date()
      },
      include: {
        product: {
          include: {
            category: true,
            unit: true
          }
        }
      }
    });

    // Create stock history entry if user is authenticated
    if (auth.user) {
      await prisma.stockHistory.create({
        data: {
          productId,
          warehouseStockId: warehouseStock.id,
          previousQuantity: 0, // For new entries
          newQuantity: quantity,
          changeQuantity: quantity,
          source: "INITIAL",
          notes: "Initial stock setup",
          userId: auth.user.id,
        }
      });

      // Log activity
      await prisma.activityLog.create({
        data: {
          userId: auth.user.id,
          action: "UPDATE_WAREHOUSE_STOCK",
          details: `Updated warehouse stock for ${product.name} (${product.sku}): quantity=${quantity}, minThreshold=${minThreshold}`,
        }
      });
    }

    return NextResponse.json({ warehouseStock });
  } catch (error) {
    console.error("Error updating warehouse stock:", error);
    return NextResponse.json(
      { error: "Failed to update warehouse stock", message: (error as Error).message },
      { status: 500 }
    );
  }
}
