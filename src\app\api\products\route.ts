import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/auth";
import { verifyAuthToken } from "@/lib/auth-utils";
import { z } from "zod";

// Helper function to check and remove expired temporary prices
async function checkAndRemoveExpiredTemporaryPrices(userId: string) {
  try {
    const now = new Date();
    
    // Find expired temporary prices
    const expiredPrices = await prisma.temporaryPrice.findMany({
      where: {
        endDate: {
          lte: now
        }
      }
    });

    if (expiredPrices.length > 0) {
      // Delete expired temporary prices
      await prisma.temporaryPrice.deleteMany({
        where: {
          endDate: {
            lte: now
          }
        }
      });

      console.log(`Removed ${expiredPrices.length} expired temporary prices`);
    }
  } catch (error) {
    console.error("Error checking expired temporary prices:", error);
  }
}

// Helper function to format product data for response
function formatProductForResponse(product: any) {
  return {
    ...product,
    basePrice: Number(product.basePrice),
    purchasePrice: product.purchasePrice ? Number(product.purchasePrice) : null,
    optionalPrice1: product.optionalPrice1 ? Number(product.optionalPrice1) : null,
    optionalPrice2: product.optionalPrice2 ? Number(product.optionalPrice2) : null,
    discountValue: product.discountValue ? Number(product.discountValue) : null,
  };
}

// Product schema for validation
const productSchema = z.object({
  name: z.string().min(2, { message: "Name must be at least 2 characters" }),
  description: z.string().optional(),
  sku: z.string().min(1, { message: "SKU is required" }),
  barcode: z.string().min(1, { message: "Barcode is required" }),
  categoryId: z.string().optional().nullable(),
  unitId: z.string(),
  supplierId: z.string().optional().nullable(),
  basePrice: z.number().positive({ message: "Base price must be positive" }),
  purchasePrice: z.number().positive({ message: "Purchase price must be positive" }),
  optionalPrice1: z.number().positive({ message: "Optional price 1 must be positive" }).optional(),
  optionalPrice2: z.number().positive({ message: "Optional price 2 must be positive" }).optional(),
  discountValue: z.number().min(0, { message: "Discount value must be non-negative" }).optional(),
  discountType: z.enum(["FIXED", "PERCENTAGE"]).optional(),
  expiryDate: z.string().optional().transform(val => val ? new Date(val) : undefined),
  imageUrl: z.string().optional(),
  active: z.boolean().default(true),
  quantity: z.number().min(0).optional(),
});

// GET /api/products - Get all products with optional filtering
export async function GET(request: NextRequest) {
  try {
    // Note: Authentication temporarily disabled for testing
    // const auth = await verifyAuthToken(request);

    // if (!auth.authenticated) {
    //   return NextResponse.json(
    //     { error: auth.error },
    //     { status: auth.status }
    //   );
    // }

    // Check for and remove expired temporary prices
    // Note: Temporarily disabled due to auth being disabled
    // if (auth.authenticated) {
    //   await checkAndRemoveExpiredTemporaryPrices(auth.user.id);
    // }

    // Get query parameters
    const url = new URL(request.url);
    const search = url.searchParams.get("search") || "";
    const category = url.searchParams.get("category") || "";
    const active = url.searchParams.get("active");
    const temporaryPrice = url.searchParams.get("temporaryPrice");
    const hasBarcodes = url.searchParams.get("hasBarcodes");
    const page = parseInt(url.searchParams.get("page") || "1");
    const limit = parseInt(url.searchParams.get("limit") || "10");
    const skip = (page - 1) * limit;

    // Build filter conditions
    const where: any = {};

    if (search) {
      // Check if search is an exact barcode match first
      const exactBarcodeMatch = await prisma.product.findFirst({
        where: { barcode: search },
      });

      if (exactBarcodeMatch) {
        // If we found an exact barcode match, just search for that specific product
        where.id = exactBarcodeMatch.id;
      } else {
        // Otherwise do a regular search
        where.OR = [
          { name: { contains: search, mode: "insensitive" } },
          { sku: { contains: search, mode: "insensitive" } },
          { barcode: { contains: search, mode: "insensitive" } },
        ];
      }
    }

    if (category) {
      where.categoryId = category;
    }

    if (active !== null && active !== undefined && active !== "all") {
      where.active = active === "true";
    }

    // Filter by temporary price status
    if (temporaryPrice !== null && temporaryPrice !== undefined && temporaryPrice !== "all") {
      if (temporaryPrice === "true") {
        where.temporaryPrice = { isNot: null };
      } else {
        where.temporaryPrice = null;
      }
    }

    // Filter products with barcodes
    if (hasBarcodes === "true") {
      where.barcode = { not: null };
    } else if (hasBarcodes === "false") {
      where.barcode = null;
    }

    // Get products with pagination
    const [products, total] = await Promise.all([
      prisma.product.findMany({
        where,
        include: {
          category: true,
          unit: true,
          supplier: true, // Keep for backward compatibility
          productSuppliers: {
            include: {
              supplier: {
                select: {
                  id: true,
                  name: true,
                  contactPerson: true,
                  phone: true,
                  email: true,
                }
              }
            },
            orderBy: [
              { isPreferred: 'desc' },
              { purchasePrice: 'asc' }
            ]
          },
          storeStock: true,
          warehouseStock: true,
          temporaryPrice: true,
        },
        skip,
        take: limit,
        orderBy: { name: "asc" },
      }),
      prisma.product.count({ where }),
    ]);

    return NextResponse.json({
      products,
      pagination: {
        total,
        page,
        limit,
        pages: Math.ceil(total / limit),
      },
    });
  } catch (error) {
    console.error("Error fetching products:", error);
    return NextResponse.json(
      { error: "Failed to fetch products", message: (error as Error).message },
      { status: 500 }
    );
  }
}

// POST /api/products - Create a new product
export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const auth = await verifyAuthToken(request);

    if (!auth.authenticated) {
      return NextResponse.json(
        { error: auth.error },
        { status: auth.status }
      );
    }

    // Check if user has permission to create products
    if (!auth.user) {
      return NextResponse.json(
        { error: "Unauthorized - User not found" },
        { status: 403 }
      );
    }

    const hasPermission = ["SUPER_ADMIN", "WAREHOUSE_ADMIN"].includes(auth.user.role);
    if (!hasPermission) {
      return NextResponse.json(
        { error: "Unauthorized - Insufficient permissions" },
        { status: 403 }
      );
    }

    // Get request body
    const body = await request.json();

    // Validate product data
    const validationResult = productSchema.safeParse(body);
    if (!validationResult.success) {
      return NextResponse.json(
        { error: "Validation failed", issues: validationResult.error.issues },
        { status: 400 }
      );
    }

    const productData = validationResult.data;

    // Check if SKU already exists
    const existingSku = await prisma.product.findUnique({
      where: { sku: productData.sku },
    });

    if (existingSku) {
      return NextResponse.json(
        { error: "SKU already exists" },
        { status: 400 }
      );
    }

    // Check if barcode already exists
    const existingBarcode = await prisma.product.findUnique({
      where: { barcode: productData.barcode },
    });

    if (existingBarcode) {
      return NextResponse.json(
        { error: "Barcode already exists" },
        { status: 400 }
      );
    }

    // Process categoryId and supplierId
    const categoryId = productData.categoryId === "none" ? null : productData.categoryId;
    const supplierId = productData.supplierId === "none" ? null : productData.supplierId;

    console.log("Product data received:", JSON.stringify(productData, null, 2));
    console.log("Processed categoryId:", categoryId);
    console.log("Processed supplierId:", supplierId);

    // Verify that the unitId exists
    if (productData.unitId) {
      const unitExists = await prisma.unit.findUnique({
        where: { id: productData.unitId },
      });

      if (!unitExists) {
        return NextResponse.json(
          { error: "Unit not found", message: "The selected unit does not exist" },
          { status: 400 }
        );
      }
    } else {
      return NextResponse.json(
        { error: "Unit is required", message: "Please select a unit for the product" },
        { status: 400 }
      );
    }

    // Extract discount fields
    const { discountValue, discountType, ...productDataWithoutDiscount } = productData;

    // Create product without discount fields
    const product = await prisma.product.create({
      data: {
        name: productDataWithoutDiscount.name,
        description: productDataWithoutDiscount.description,
        sku: productDataWithoutDiscount.sku,
        barcode: productDataWithoutDiscount.barcode,
        categoryId: categoryId,
        unitId: productDataWithoutDiscount.unitId,
        supplierId: supplierId,
        basePrice: productDataWithoutDiscount.basePrice,
        purchasePrice: productDataWithoutDiscount.purchasePrice,
        optionalPrice1: productDataWithoutDiscount.optionalPrice1,
        optionalPrice2: productDataWithoutDiscount.optionalPrice2,
        expiryDate: productDataWithoutDiscount.expiryDate,
        imageUrl: productDataWithoutDiscount.imageUrl,
        active: productDataWithoutDiscount.active,
      },
      include: {
        category: true,
        unit: true,
        supplier: true,
      },
    });

    // Update discount fields separately using raw SQL if they were provided
    if (discountValue !== undefined || discountType !== undefined) {
      // Prepare SQL parameters
      const updates = [];
      const params = [];

      if (discountValue !== undefined) {
        updates.push(`"discountValue" = $1`);
        params.push(discountValue);
      }

      if (discountType !== undefined) {
        updates.push(`"discountType" = $${params.length + 1}`);
        params.push(discountType);
      }

      // Add the product ID as the last parameter
      params.push(product.id);

      // Execute raw SQL update
      await prisma.$executeRaw`
        UPDATE "Product"
        SET ${prisma.$raw(updates.join(', '))}
        WHERE id = $${params.length}
      `;
    }

    // Create initial store stock
    await prisma.storeStock.create({
      data: {
        productId: product.id,
        quantity: productData.quantity || 0,
        minThreshold: 5, // Default minimum threshold
      },
    });

    // Log activity
    if (auth.user) {
      await prisma.activityLog.create({
        data: {
          userId: auth.user.id,
          action: "CREATE_PRODUCT",
          details: `Created product: ${product.name} (SKU: ${product.sku})`,
        },
      });
    }

    return NextResponse.json({ product }, { status: 201 });
  } catch (error) {
    console.error("Error creating product:", error);
    console.error("Error details:", JSON.stringify(error, null, 2));

    // Return more detailed error information
    return NextResponse.json(
      {
        error: "Failed to create product",
        message: (error as Error).message,
        details: error instanceof Error ? error.stack : "Unknown error"
      },
      { status: 500 }
    );
  }
}