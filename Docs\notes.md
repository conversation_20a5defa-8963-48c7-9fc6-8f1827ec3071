We will document notes about the project in this file. Could be anything, from brainstorming ideas to reflecting on decisions made.
Or case by case study of a particular feature.

## POS

- What if the product is available in the actual store shelf but in the system it's out of stock?
  Should we still allow selling it? How to proceed since it will be not found in the search?
- what is the best way to handle different pricing for different customers? (e.g. members vs non-members)

## Product Management

### Inline Editing

The product list page now supports inline editing for the following fields:

- Name (text field)
- SKU (text field)
- Category (dropdown)
- Unit (dropdown)

Implementation details:

- Created a reusable `InlineEdit` component that supports both text and dropdown fields
- Text fields automatically save when focus changes (user clicks elsewhere)
- Dropdown fields save automatically on selection
- All edits are validated and saved via background API requests
- Loading indicators show during save operations
- Error handling with inline error messages
- Toast notifications for successful updates

This feature allows users to quickly edit product information without navigating away from the product list page, improving efficiency for inventory management tasks.
