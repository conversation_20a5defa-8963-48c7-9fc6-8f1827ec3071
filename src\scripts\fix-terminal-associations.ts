// Script to fix terminal associations for existing transactions
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function fixTerminalAssociations() {
  try {
    console.log('🔧 Starting terminal association fix...\n');

    // Check current state
    const totalTransactions = await prisma.transaction.count();
    const transactionsWithTerminal = await prisma.transaction.count({
      where: { terminalId: { not: null } }
    });
    const transactionsWithoutTerminal = await prisma.transaction.count({
      where: { terminalId: null }
    });
    const transactionsWithDrawerSession = await prisma.transaction.count({
      where: { drawerSessionId: { not: null } }
    });

    console.log('📊 Current state:');
    console.log(`   Total transactions: ${totalTransactions}`);
    console.log(`   With terminal ID: ${transactionsWithTerminal}`);
    console.log(`   Without terminal ID: ${transactionsWithoutTerminal}`);
    console.log(`   With drawer session: ${transactionsWithDrawerSession}\n`);

    if (transactionsWithoutTerminal === 0) {
      console.log('✅ All transactions already have terminal associations!');
      return;
    }

    // Fix transactions that have drawer sessions but missing terminal IDs
    console.log('🔄 Updating transactions with drawer sessions...');
    
    const updateResult = await prisma.$executeRaw`
      UPDATE "Transaction" 
      SET "terminalId" = ds."terminalId"
      FROM "DrawerSession" ds
      WHERE "Transaction"."drawerSessionId" = ds."id"
        AND "Transaction"."terminalId" IS NULL
        AND ds."terminalId" IS NOT NULL
    `;

    console.log(`   Updated ${updateResult} transactions via drawer sessions\n`);

    // For transactions without drawer sessions, try to associate with the first available terminal
    // This is a fallback for very old transactions
    const transactionsStillWithoutTerminal = await prisma.transaction.count({
      where: { terminalId: null }
    });

    if (transactionsStillWithoutTerminal > 0) {
      console.log(`🔄 ${transactionsStillWithoutTerminal} transactions still need terminal associations...`);
      
      // Get the first active terminal as a fallback
      const fallbackTerminal = await prisma.terminal.findFirst({
        where: { isActive: true },
        orderBy: { createdAt: 'asc' }
      });

      if (fallbackTerminal) {
        console.log(`   Using fallback terminal: ${fallbackTerminal.name} (${fallbackTerminal.id})`);
        
        const fallbackUpdateResult = await prisma.transaction.updateMany({
          where: { terminalId: null },
          data: { terminalId: fallbackTerminal.id }
        });

        console.log(`   Updated ${fallbackUpdateResult.count} transactions with fallback terminal\n`);
      } else {
        console.log('   ⚠️  No active terminals found for fallback\n');
      }
    }

    // Check final state
    const finalTransactionsWithTerminal = await prisma.transaction.count({
      where: { terminalId: { not: null } }
    });
    const finalTransactionsWithoutTerminal = await prisma.transaction.count({
      where: { terminalId: null }
    });

    console.log('📊 Final state:');
    console.log(`   With terminal ID: ${finalTransactionsWithTerminal}`);
    console.log(`   Without terminal ID: ${finalTransactionsWithoutTerminal}`);
    
    if (finalTransactionsWithoutTerminal === 0) {
      console.log('✅ All transactions now have terminal associations!');
    } else {
      console.log(`⚠️  ${finalTransactionsWithoutTerminal} transactions still need manual review`);
    }

    // Show sample of updated transactions
    console.log('\n📋 Sample of transactions with terminal associations:');
    const sampleTransactions = await prisma.transaction.findMany({
      take: 5,
      where: { terminalId: { not: null } },
      select: {
        id: true,
        terminalId: true,
        createdAt: true,
        total: true,
        terminal: {
          select: {
            name: true
          }
        }
      },
      orderBy: { createdAt: 'desc' }
    });

    sampleTransactions.forEach((tx, index) => {
      console.log(`${index + 1}. ${tx.id.substring(0, 8)}... -> ${tx.terminal?.name} (${tx.terminalId?.substring(0, 8)}...)`);
    });

  } catch (error) {
    console.error('❌ Error fixing terminal associations:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Run the script
if (require.main === module) {
  fixTerminalAssociations()
    .then(() => {
      console.log('\n🎉 Terminal association fix completed!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n💥 Terminal association fix failed:', error);
      process.exit(1);
    });
}

export { fixTerminalAssociations };
