"use client";

import React, { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { MainLayout } from "@/components/layout/MainLayout";
import { PageHeader } from "@/components/layout/PageHeader";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
  CardFooter,
} from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { ArrowLeft, Loader2, Calendar, User, DollarSign, Printer, AlertCircle } from "lucide-react";
import { toast } from "sonner";
import { formatCurrency, formatDate } from "@/lib/utils";
import Link from "next/link";
import { useAuth } from "@/hooks/use-auth";

interface DrawerSession {
  id: string;
  drawerId: string;
  userId: string;
  businessDate: string;
  openingBalance: number;
  expectedClosingBalance?: number;
  actualClosingBalance?: number;
  discrepancy?: number;
  openedAt: string;
  closedAt?: string;
  status: "OPEN" | "CLOSED" | "RECONCILED";
  notes?: string;
  drawer: {
    id: string;
    name: string;
    location?: string;
  };
  user: {
    id: string;
    name: string;
  };
}

interface Transaction {
  id: string;
  transactionDate: string;
  subtotal: number;
  discount: number;
  tax: number;
  total: number;
  paymentMethod: string;
  status: string;
  cashier: {
    id: string;
    name: string;
  };
  customer?: {
    id: string;
    name: string;
  };
}

interface SessionStats {
  totalTransactions: number;
  totalSales: number;
  totalCashSales: number;
  totalDebitSales: number;
  totalQrisSales: number;
  expectedClosingBalance: number;
}

export default function DrawerSessionDetailPage({ params }: { params: { id: string } }) {
  const router = useRouter();
  const { user } = useAuth();
  const [session, setSession] = useState<DrawerSession | null>(null);
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [stats, setStats] = useState<SessionStats | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isClosing, setIsClosing] = useState(false);

  // Unwrap params using React.use()
  const { id } = React.use(params);

  // Fetch session details
  const fetchSession = async () => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await fetch(`/api/drawer-sessions/${id}`);
      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || "Failed to fetch drawer session");
      }

      setSession(data.session);
      setTransactions(data.transactions);
      setStats(data.stats);
    } catch (err) {
      setError(err instanceof Error ? err.message : "An unknown error occurred");
      console.error("Error fetching drawer session:", err);
    } finally {
      setIsLoading(false);
    }
  };

  // Load session on mount
  useEffect(() => {
    fetchSession();
  }, [id]);

  // Get payment method badge
  const getPaymentMethodBadge = (method: string) => {
    switch (method) {
      case "CASH":
        return <Badge variant="default">Cash</Badge>;
      case "DEBIT":
        return <Badge variant="outline">Debit</Badge>;
      case "QRIS":
        return <Badge variant="secondary">QRIS</Badge>;
      default:
        return <Badge variant="outline">{method}</Badge>;
    }
  };

  // Get status badge
  const getStatusBadge = (status: string) => {
    switch (status) {
      case "COMPLETED":
        return <Badge variant="success">Completed</Badge>;
      case "PENDING":
        return <Badge variant="warning">Pending</Badge>;
      case "VOIDED":
        return <Badge variant="destructive">Voided</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  // Print reconciliation report
  const printReconciliation = () => {
    if (!session || !stats) return;

    const printWindow = window.open("", "_blank");
    if (!printWindow) {
      toast.error("Pop-up blocked. Please allow pop-ups for this site.");
      return;
    }

    const content = `
      <!DOCTYPE html>
      <html>
      <head>
        <title>Drawer Reconciliation Report</title>
        <style>
          body { font-family: Arial, sans-serif; margin: 20px; }
          h1 { font-size: 18px; text-align: center; }
          .info { margin-bottom: 20px; }
          .info div { margin-bottom: 5px; }
          table { width: 100%; border-collapse: collapse; margin-bottom: 20px; }
          th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
          th { background-color: #f2f2f2; }
          .footer { margin-top: 30px; border-top: 1px solid #ddd; padding-top: 10px; }
          .total { font-weight: bold; }
          .discrepancy { font-weight: bold; }
          .surplus { color: green; }
          .shortage { color: red; }
          @media print {
            button { display: none; }
          }
        </style>
      </head>
      <body>
        <button onclick="window.print();" style="float: right;">Print</button>
        <h1>Drawer Reconciliation Report</h1>

        <div class="info">
          <div><strong>Drawer:</strong> ${session.drawer.name}</div>
          <div><strong>Cashier:</strong> ${session.user.name}</div>
          <div><strong>Business Date:</strong> ${formatDate(new Date(session.businessDate))}</div>
          <div><strong>Opened:</strong> ${formatDate(new Date(session.openedAt), true)}</div>
          ${session.closedAt ? `<div><strong>Closed:</strong> ${formatDate(new Date(session.closedAt), true)}</div>` : ""}
          <div><strong>Status:</strong> ${session.status}</div>
        </div>

        <h2>Transaction Summary</h2>
        <table>
          <tr>
            <th>Payment Method</th>
            <th>Amount</th>
            <th>Count</th>
          </tr>
          <tr>
            <td>Cash</td>
            <td>${formatCurrency(stats.totalCashSales)}</td>
            <td>${transactions.filter((t) => t.paymentMethod === "CASH").length}</td>
          </tr>
          <tr>
            <td>Debit</td>
            <td>${formatCurrency(stats.totalDebitSales)}</td>
            <td>${transactions.filter((t) => t.paymentMethod === "DEBIT").length}</td>
          </tr>
          <tr>
            <td>QRIS</td>
            <td>${formatCurrency(stats.totalQrisSales)}</td>
            <td>${transactions.filter((t) => t.paymentMethod === "QRIS").length}</td>
          </tr>
          <tr class="total">
            <td>Total</td>
            <td>${formatCurrency(stats.totalSales)}</td>
            <td>${stats.totalTransactions}</td>
          </tr>
        </table>

        <h2>Cash Reconciliation</h2>
        <table>
          <tr>
            <td>Opening Balance</td>
            <td>${formatCurrency(session.openingBalance)}</td>
          </tr>
          <tr>
            <td>Cash Sales</td>
            <td>${formatCurrency(stats.totalCashSales)}</td>
          </tr>
          <tr>
            <td>Expected Closing Balance</td>
            <td>${formatCurrency(stats.expectedClosingBalance)}</td>
          </tr>
          <tr>
            <td>Actual Closing Balance</td>
            <td>${session.actualClosingBalance ? formatCurrency(session.actualClosingBalance) : "Not closed yet"}</td>
          </tr>
          ${
            session.discrepancy !== null && session.discrepancy !== undefined
              ? `
          <tr class="discrepancy ${session.discrepancy > 0 ? "surplus" : session.discrepancy < 0 ? "shortage" : ""}">
            <td>Discrepancy</td>
            <td>${session.discrepancy > 0 ? "+" : ""}${formatCurrency(session.discrepancy)}</td>
          </tr>
          ${
            session.notes
              ? `
          <tr>
            <td>Discrepancy Reason</td>
            <td>${session.notes}</td>
          </tr>`
              : ""
          }`
              : ""
          }
        </table>

        <div class="footer">
          <p>Generated on ${formatDate(new Date(), true)}</p>
        </div>
      </body>
      </html>
    `;

    printWindow.document.open();
    printWindow.document.write(content);
    printWindow.document.close();
  };

  return (
    <MainLayout>
      <div className="flex items-center">
        <Button variant="outline" size="icon" className="mr-4" asChild>
          <Link href={`/admin/cash-drawers/${session?.drawerId || ""}`}>
            <ArrowLeft className="h-4 w-4" />
          </Link>
        </Button>
        <PageHeader
          heading={session ? `Drawer Session: ${session.drawer.name}` : "Drawer Session"}
          subheading={session ? `Cashier: ${session.user.name}` : "View session details"}
        />
      </div>

      {isLoading ? (
        <div className="flex justify-center items-center py-8">
          <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
        </div>
      ) : error ? (
        <div className="bg-destructive/10 text-destructive p-4 rounded-md mt-6">{error}</div>
      ) : session && stats ? (
        <>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mt-6">
            <Card>
              <CardHeader>
                <CardTitle>Session Details</CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Status:</span>
                  <Badge
                    variant={
                      session.status === "OPEN"
                        ? "success"
                        : session.status === "CLOSED"
                          ? "default"
                          : "secondary"
                    }
                  >
                    {session.status}
                  </Badge>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Cashier:</span>
                  <span className="font-medium">{session.user.name}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Drawer:</span>
                  <span>{session.drawer.name}</span>
                </div>
                {session.drawer.location && (
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Location:</span>
                    <span>{session.drawer.location}</span>
                  </div>
                )}
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Business Date:</span>
                  <span>{formatDate(new Date(session.businessDate))}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Opened At:</span>
                  <span>{formatDate(new Date(session.openedAt), true)}</span>
                </div>
                {session.closedAt && (
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Closed At:</span>
                    <span>{formatDate(new Date(session.closedAt), true)}</span>
                  </div>
                )}
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Transaction Summary</CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Total Transactions:</span>
                  <span>{stats.totalTransactions}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Total Sales:</span>
                  <span>{formatCurrency(stats.totalSales)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Cash Sales:</span>
                  <span>{formatCurrency(stats.totalCashSales)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Debit Sales:</span>
                  <span>{formatCurrency(stats.totalDebitSales)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">QRIS Sales:</span>
                  <span>{formatCurrency(stats.totalQrisSales)}</span>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Cash Reconciliation</CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Opening Balance:</span>
                  <span>{formatCurrency(session.openingBalance)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Expected Closing:</span>
                  <span>{formatCurrency(stats.expectedClosingBalance)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Actual Closing:</span>
                  <span>
                    {session.actualClosingBalance
                      ? formatCurrency(session.actualClosingBalance)
                      : "-"}
                  </span>
                </div>
                {session.discrepancy !== null && session.discrepancy !== undefined && (
                  <>
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Discrepancy:</span>
                      <span
                        className={
                          session.discrepancy > 0
                            ? "text-green-600"
                            : session.discrepancy < 0
                              ? "text-red-600"
                              : ""
                        }
                      >
                        {session.discrepancy > 0 ? "+" : ""}
                        {formatCurrency(session.discrepancy)}
                      </span>
                    </div>
                    {session.notes && (
                      <div className="flex justify-between items-start pt-2 border-t">
                        <span className="text-muted-foreground">Discrepancy Reason:</span>
                        <span className="text-right max-w-[200px] text-sm">{session.notes}</span>
                      </div>
                    )}
                  </>
                )}
              </CardContent>
              <CardFooter className="flex justify-end">
                {session.status !== "OPEN" && (
                  <Button variant="outline" size="sm" onClick={printReconciliation}>
                    <Printer className="h-4 w-4 mr-2" />
                    Print Report
                  </Button>
                )}
              </CardFooter>
            </Card>
          </div>

          <Card className="mt-6">
            <CardHeader>
              <CardTitle>Transactions</CardTitle>
              <CardDescription>All transactions processed during this session</CardDescription>
            </CardHeader>
            <CardContent>
              {transactions.length === 0 ? (
                <div className="text-center py-8 text-muted-foreground">
                  No transactions found for this session.
                </div>
              ) : (
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Date & Time</TableHead>
                      <TableHead>Transaction ID</TableHead>
                      <TableHead>Customer</TableHead>
                      <TableHead>Payment</TableHead>
                      <TableHead>Amount</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead className="text-right">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {transactions.map((transaction) => (
                      <TableRow key={transaction.id}>
                        <TableCell>
                          {formatDate(new Date(transaction.transactionDate), true)}
                        </TableCell>
                        <TableCell className="font-mono text-xs">
                          {transaction.id.substring(0, 8)}...
                        </TableCell>
                        <TableCell>{transaction.customer?.name || "Walk-in Customer"}</TableCell>
                        <TableCell>{getPaymentMethodBadge(transaction.paymentMethod)}</TableCell>
                        <TableCell>{formatCurrency(transaction.total)}</TableCell>
                        <TableCell>{getStatusBadge(transaction.status)}</TableCell>
                        <TableCell className="text-right">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => router.push(`/transactions/${transaction.id}`)}
                          >
                            View
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              )}
            </CardContent>
          </Card>
        </>
      ) : (
        <div className="text-center py-8 text-muted-foreground">Drawer session not found.</div>
      )}
    </MainLayout>
  );
}
