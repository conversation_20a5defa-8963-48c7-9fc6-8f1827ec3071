import NextAuth from "next-auth";
import { PrismaAdapter } from "@auth/prisma-adapter";
import { PrismaClient } from "@/generated/prisma";
import CredentialsProvider from "next-auth/providers/credentials";
import bcrypt from "bcryptjs";
import { z } from "zod";

// Create a singleton instance of PrismaClient to prevent too many connections
const globalForPrisma = globalThis as unknown as { prisma: PrismaClient };
export const prisma = globalForPrisma.prisma || new PrismaClient();
if (process.env.NODE_ENV !== "production") globalForPrisma.prisma = prisma;

// Define the login schema for validation
const loginSchema = z.object({
  email: z.string().email({ message: "Invalid email address" }),
  password: z.string().min(6, { message: "Password must be at least 6 characters" }),
});

export const { handlers, auth, signIn, signOut } = NextAuth({
  // We'll use JWT strategy instead of the adapter for simplicity
  // adapter: PrismaAdapter(prisma),
  session: { strategy: "jwt" },
  pages: {
    signIn: "/login",
    error: "/login",
  },
  callbacks: {
    async session({ session, token }) {
      if (token.sub && session.user) {
        session.user.id = token.sub;
        session.user.role = token.role as string;
      }
      return session;
    },
    async jwt({ token, user }) {
      if (user) {
        token.role = user.role;
      }
      return token;
    },
  },
  providers: [
    CredentialsProvider({
      name: "Credentials",
      credentials: {
        email: { label: "Email", type: "email" },
        password: { label: "Password", type: "password" },
      },
      async authorize(credentials) {
        try {
          console.log("Auth attempt with credentials:", { email: credentials.email });

          // Validate credentials
          const result = loginSchema.safeParse(credentials);
          if (!result.success) {
            console.log("Validation failed:", result.error);
            return null;
          }

          // Find user by email
          console.log("Looking for user with email:", credentials.email);
          const user = await prisma.user.findUnique({
            where: { email: credentials.email },
          });

          console.log("User found:", user ? "Yes" : "No");

          if (!user) {
            console.log("No user found with this email");
            return null;
          }

          if (!user.password) {
            console.log("User has no password set");
            return null;
          }

          if (!user.active) {
            console.log("User account is not active");
            return null;
          }

          // Verify password
          console.log("Verifying password...");
          const passwordMatch = await bcrypt.compare(credentials.password, user.password);
          console.log("Password match:", passwordMatch ? "Yes" : "No");

          if (!passwordMatch) {
            console.log("Password does not match");
            return null;
          }

          // Log activity
          console.log("Creating activity log entry");
          await prisma.activityLog.create({
            data: {
              userId: user.id,
              action: "LOGIN",
              details: "User logged in",
            },
          });

          console.log("Login successful, returning user data");
          return {
            id: user.id,
            name: user.name,
            email: user.email,
            role: user.role,
          };
        } catch (error) {
          console.error("Auth error:", error);
          console.error("Error details:", {
            name: error.name,
            message: error.message,
            stack: error.stack
          });
          return null;
        }
      },
    }),
  ],
});

// Helper function to hash passwords
export async function hashPassword(password: string): Promise<string> {
  return await bcrypt.hash(password, 10);
}

// Helper function to verify passwords
export async function verifyPassword(
  password: string,
  hashedPassword: string
): Promise<boolean> {
  return await bcrypt.compare(password, hashedPassword);
}

// Types for role-based access control
export type UserRole = "DEVELOPER" | "SUPER_ADMIN" | "CASHIER" | "FINANCE_ADMIN" | "WAREHOUSE_ADMIN" | "MARKETING";

// Define permissions for each role
export const rolePermissions: Record<string, string[]> = {
  DEVELOPER: ["*", "developer.access"], // All permissions plus developer-specific permissions
  SUPER_ADMIN: ["*"], // All permissions
  CASHIER: ["pos.access", "pos.create", "pos.view", "customers.view", "customers.create"],
  FINANCE_ADMIN: [
    "transactions.view",
    "transactions.create",
    "transactions.edit",
    "reports.view",
    "reports.create",
  ],
  WAREHOUSE_ADMIN: [
    "inventory.view",
    "inventory.create",
    "inventory.edit",
    "products.view",
    "products.create",
    "products.edit",
  ],
  MARKETING: ["products.view", "customers.view", "reports.view"],
};

// Helper function to check if a user has a specific permission
export function hasPermission(userRole: string, permission: string): boolean {
  const permissions = rolePermissions[userRole] || [];
  return permissions.includes("*") || permissions.includes(permission);
}
