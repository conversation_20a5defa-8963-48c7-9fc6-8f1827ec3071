// Mock for next-auth
module.exports = {
  auth: jest.fn().mockResolvedValue(null),
  signIn: jest.fn(),
  signOut: jest.fn(),
  // Mock for role-based permissions
  hasPermission: jest.fn().mockImplementation((role, permission) => {
    if (role === 'SUPER_ADMIN') return true;
    
    const permissions = {
      CASHIER: ['pos.access', 'pos.create', 'customers.view'],
      FINANCE_ADMIN: ['transactions.view', 'reports.view'],
      WAREHOUSE_ADMIN: ['inventory.view', 'inventory.edit', 'products.view'],
      MARKETING: ['products.view', 'customers.view'],
    };
    
    return permissions[role]?.includes(permission) || false;
  }),
  // Mock for password utilities
  hashPassword: jest.fn().mockImplementation(password => `hashed-${password}`),
  verifyPassword: jest.fn().mockImplementation((password, hashedPassword) => {
    return hashedPassword === `hashed-${password}`;
  }),
  // Mock for role permissions
  rolePermissions: {
    SUPER_ADMIN: ['*'],
    CASHIER: ['pos.access', 'pos.create', 'customers.view'],
    FINANCE_ADMIN: ['transactions.view', 'reports.view'],
    WAREHOUSE_ADMIN: ['inventory.view', 'inventory.edit', 'products.view'],
    MARKETING: ['products.view', 'customers.view'],
  },
  // Mock for Prisma
  prisma: {
    user: {
      findMany: jest.fn(),
      findUnique: jest.fn(),
      create: jest.fn(),
      update: jest.fn(),
    },
    activityLog: {
      create: jest.fn(),
      findMany: jest.fn(),
      count: jest.fn(),
      groupBy: jest.fn(),
    },
  },
};
