import { NextRequest, NextResponse } from 'next/server';
import { PrismaClient } from '@/generated/prisma';
import { verifyAuthToken } from '@/lib/auth-utils';
import { z } from 'zod';
import { PDFDocument, StandardFonts, rgb } from 'pdf-lib';
import { createPOStatusNotifications, markPONotificationsAsRead } from '@/lib/notifications';

// Create a singleton instance of PrismaClient to prevent too many connections
const globalForPrisma = globalThis as unknown as { prisma: PrismaClient };
const prisma = globalForPrisma.prisma || new PrismaClient();
if (process.env.NODE_ENV !== "production") globalForPrisma.prisma = prisma;

// Purchase order item schema for validation
const purchaseOrderItemUpdateSchema = z.object({
  id: z.string().optional(), // For existing items
  productId: z.string().min(1, { message: "Product is required" }),
  quantity: z.number().positive({ message: "Quantity must be positive" }),
  unitPrice: z.number().positive({ message: "Unit price must be positive" }),
});

// Purchase order update schema for validation
const purchaseOrderUpdateSchema = z.object({
  // Status updates (existing functionality)
  status: z.enum(['DRAFT', 'PENDING_APPROVAL', 'APPROVED', 'ORDERED', 'RECEIVED', 'CANCELLED']).optional(),
  notes: z.string().optional(),
  approvalNotes: z.string().optional(),

  // Full edit functionality (new)
  supplierId: z.string().min(1, { message: "Supplier is required" }).optional(),
  orderDate: z.string().min(1, { message: "Order date is required" }).transform(val => new Date(val)).optional(),
  taxPercentage: z.number().min(0, { message: "Tax percentage must be non-negative" }).max(100, { message: "Tax percentage cannot exceed 100%" }).optional(),
  items: z.array(purchaseOrderItemUpdateSchema).min(1, { message: "At least one item is required" }).optional(),
});

// GET /api/purchase-orders/[id] - Get purchase order details or generate PDF
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const { searchParams } = new URL(request.url);
    const format = searchParams.get('format');

    const auth = await verifyAuthToken(request);
    if (!auth.authenticated || !auth.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = ['SUPER_ADMIN', 'WAREHOUSE_ADMIN', 'FINANCE_ADMIN'].includes(auth.user.role);
    if (!hasPermission) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 });
    }

    const purchaseOrder = await prisma.purchaseOrder.findUnique({
      where: { id },
      include: {
        supplier: true,
        createdBy: {
          select: { id: true, name: true, email: true },
        },
        approvedBy: {
          select: { id: true, name: true, email: true },
        },
        items: {
          include: {
            product: {
              include: {
                category: true,
                unit: true,
              },
            },
          },
        },
      },
    });

    if (!purchaseOrder) {
      return NextResponse.json({ error: 'Purchase order not found' }, { status: 404 });
    }

    // If PDF format is requested, generate and return PDF
    if (format === 'pdf') {
      try {
        // Get store information
        const storeInfo = await prisma.storeInfo.findFirst({
          where: { id: "default-store" }
        });

        // Generate PDF
        const pdfBytes = await generatePurchaseOrderPDF(purchaseOrder, storeInfo);

        return new NextResponse(pdfBytes, {
          headers: {
            'Content-Type': 'application/pdf',
            'Content-Disposition': `attachment; filename="purchase-order-${purchaseOrder.id.slice(-8).toUpperCase()}.pdf"`,
          },
        });
      } catch (pdfError) {
        console.error('Error generating PDF:', pdfError);
        return NextResponse.json({ error: 'Failed to generate PDF' }, { status: 500 });
      }
    }

    // Mark related notifications as read when viewing PO
    try {
      await markPONotificationsAsRead(id, auth.user.id);
    } catch (notificationError) {
      console.error('Error marking notifications as read:', notificationError);
      // Don't fail the request if notification update fails
    }

    return NextResponse.json(purchaseOrder);
  } catch (error) {
    console.error('Error fetching purchase order:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

// PATCH /api/purchase-orders/[id] - Update purchase order
export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;

    const auth = await verifyAuthToken(request);
    if (!auth.authenticated || !auth.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = ['SUPER_ADMIN', 'WAREHOUSE_ADMIN', 'FINANCE_ADMIN'].includes(auth.user.role);
    if (!hasPermission) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 });
    }

    const body = await request.json();
    const validatedData = purchaseOrderUpdateSchema.parse(body);

    // Check if purchase order exists
    const existingPO = await prisma.purchaseOrder.findUnique({
      where: { id },
      include: {
        supplier: true,
        items: {
          include: {
            product: true,
          },
        },
      },
    });

    if (!existingPO) {
      return NextResponse.json({ error: 'Purchase order not found' }, { status: 404 });
    }

    // Check if PO can be edited (only DRAFT and PENDING_APPROVAL can be fully edited)
    const isFullEdit = validatedData.supplierId || validatedData.orderDate || validatedData.taxPercentage || validatedData.items;
    if (isFullEdit && !['DRAFT', 'PENDING_APPROVAL'].includes(existingPO.status)) {
      return NextResponse.json({
        error: 'Purchase order can only be edited when in DRAFT or PENDING_APPROVAL status'
      }, { status: 400 });
    }

    // Prepare update data
    const updateData: any = {};

    // Handle status-only updates (existing functionality)
    if (validatedData.status) {
      updateData.status = validatedData.status;
    }
    if (validatedData.notes !== undefined) {
      updateData.notes = validatedData.notes;
    }
    if (validatedData.approvalNotes !== undefined) {
      updateData.approvalNotes = validatedData.approvalNotes;
    }

    // Handle full edit updates (new functionality)
    if (validatedData.supplierId) {
      // Verify supplier exists
      const supplier = await prisma.supplier.findUnique({
        where: { id: validatedData.supplierId },
      });
      if (!supplier) {
        return NextResponse.json({ error: 'Supplier not found' }, { status: 404 });
      }
      updateData.supplierId = validatedData.supplierId;
    }

    if (validatedData.orderDate) {
      updateData.orderDate = validatedData.orderDate;
    }

    if (validatedData.taxPercentage !== undefined) {
      updateData.taxPercentage = validatedData.taxPercentage;
    }

    // If approving, set approval fields
    if (validatedData.status === 'APPROVED' && existingPO.status !== 'APPROVED') {
      updateData.approvedById = auth.user.id;
      updateData.approvedAt = new Date();
    }

    // If receiving, set received date and update stock
    if (validatedData.status === 'RECEIVED' && existingPO.status !== 'RECEIVED') {
      updateData.receivedAt = new Date();
    }

    // Use transaction to ensure data consistency
    const updatedPO = await prisma.$transaction(async (tx) => {
      // Handle item updates if provided
      if (validatedData.items) {
        // Verify all products exist
        const productIds = validatedData.items.map(item => item.productId);
        const products = await tx.product.findMany({
          where: { id: { in: productIds } },
        });

        if (products.length !== productIds.length) {
          throw new Error('One or more products not found');
        }

        // Delete existing items
        await tx.purchaseOrderItem.deleteMany({
          where: { purchaseOrderId: id },
        });

        // Calculate totals for items
        const itemsSubtotal = validatedData.items.reduce((sum, item) => sum + (item.quantity * item.unitPrice), 0);
        const taxAmount = updateData.taxPercentage ? (itemsSubtotal * updateData.taxPercentage / 100) : 0;
        const total = itemsSubtotal + taxAmount;

        // Update totals in updateData
        updateData.subtotal = itemsSubtotal;
        updateData.tax = taxAmount;
        updateData.total = total;
      }

      // Update purchase order
      const po = await tx.purchaseOrder.update({
        where: { id },
        data: updateData,
        include: {
          supplier: true,
          createdBy: {
            select: { id: true, name: true, email: true },
          },
          approvedBy: {
            select: { id: true, name: true, email: true },
          },
          items: {
            include: {
              product: {
                include: {
                  category: true,
                  unit: true,
                },
              },
            },
          },
        },
      });

      // Create new items if provided
      if (validatedData.items) {
        await tx.purchaseOrderItem.createMany({
          data: validatedData.items.map(item => ({
            purchaseOrderId: id,
            productId: item.productId,
            quantity: item.quantity,
            unitPrice: item.unitPrice,
            subtotal: item.quantity * item.unitPrice,
          })),
        });

        // Fetch updated PO with new items
        return await tx.purchaseOrder.findUnique({
          where: { id },
          include: {
            supplier: true,
            createdBy: {
              select: { id: true, name: true, email: true },
            },
            approvedBy: {
              select: { id: true, name: true, email: true },
            },
            items: {
              include: {
                product: {
                  include: {
                    category: true,
                    unit: true,
                  },
                },
              },
            },
          },
        });
      }

      // If status changed to RECEIVED, update warehouse stock
      if (validatedData.status === 'RECEIVED' && existingPO.status !== 'RECEIVED') {
        for (const item of po.items) {
          // Get or create warehouse stock
          const warehouseStock = await tx.warehouseStock.upsert({
            where: { productId: item.productId },
            update: {
              quantity: {
                increment: item.quantity,
              },
              lastUpdated: new Date(),
            },
            create: {
              productId: item.productId,
              quantity: item.quantity,
              minThreshold: 0,
              lastUpdated: new Date(),
            },
          });

          // Create stock history entry
          await tx.stockHistory.create({
            data: {
              productId: item.productId,
              warehouseStockId: warehouseStock.id,
              previousQuantity: Number(warehouseStock.quantity) - Number(item.quantity),
              newQuantity: Number(warehouseStock.quantity),
              changeQuantity: Number(item.quantity),
              source: "PURCHASE",
              referenceId: po.id,
              referenceType: "PurchaseOrder",
              notes: `Purchase order received: ${po.id.slice(-8).toUpperCase()}`,
              userId: auth.user!.id,
            },
          });
        }
      }

      return po;
    });

    // Create notifications for status changes
    if (validatedData.status && validatedData.status !== existingPO.status) {
      try {
        await createPOStatusNotifications(id, validatedData.status, auth.user.id);
      } catch (notificationError) {
        console.error('Error creating status notifications:', notificationError);
        // Don't fail the request if notifications fail
      }
    }

    // Log activity
    let activityDetails = `Updated purchase order for supplier: ${existingPO.supplier.name}`;
    if (validatedData.status) {
      activityDetails += ` - Status changed to: ${validatedData.status}`;
    }

    await prisma.activityLog.create({
      data: {
        userId: auth.user.id,
        action: 'UPDATE_PURCHASE_ORDER',
        details: activityDetails,
      },
    });

    return NextResponse.json(updatedPO);
  } catch (error) {
    console.error('Error updating purchase order:', error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation failed', issues: error.issues },
        { status: 400 }
      );
    }

    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

async function generatePurchaseOrderPDF(purchaseOrder: any, storeInfo: any) {
  // Create a new PDF document
  const pdfDoc = await PDFDocument.create();
  const page = pdfDoc.addPage([595, 842]); // A4 portrait

  // Embed fonts
  const font = await pdfDoc.embedFont(StandardFonts.Helvetica);
  const boldFont = await pdfDoc.embedFont(StandardFonts.HelveticaBold);

  const { width, height } = page.getSize();
  const margin = 50;
  let y = height - margin;

  // Helper function to draw text
  const drawText = (text: string, x: number, yPos: number, options: any = {}) => {
    page.drawText(text, {
      x,
      y: yPos,
      size: options.size || 10,
      font: options.bold ? boldFont : font,
      color: options.color || rgb(0, 0, 0),
      maxWidth: options.maxWidth,
    });
  };

  // Header
  drawText('PURCHASE ORDER', margin, y, { size: 20, bold: true });
  y -= 30;

  // Store information (if available)
  if (storeInfo) {
    drawText(storeInfo.storeName || 'Next POS Store', margin, y, { size: 14, bold: true });
    y -= 15;
    if (storeInfo.address) {
      drawText(storeInfo.address, margin, y, { size: 10 });
      y -= 12;
    }
    if (storeInfo.phone) {
      drawText(`Phone: ${storeInfo.phone}`, margin, y, { size: 10 });
      y -= 12;
    }
    if (storeInfo.email) {
      drawText(`Email: ${storeInfo.email}`, margin, y, { size: 10 });
      y -= 12;
    }
  }

  y -= 20;

  // Purchase Order Information
  const poNumber = purchaseOrder.id.slice(-8).toUpperCase();
  drawText(`PO Number: ${poNumber}`, margin, y, { size: 12, bold: true });
  drawText(`Date: ${new Date(purchaseOrder.orderDate).toLocaleDateString()}`, width - margin - 150, y, { size: 12 });
  y -= 15;

  drawText(`Status: ${purchaseOrder.status}`, margin, y, { size: 10 });
  drawText(`Created by: ${purchaseOrder.createdBy.name}`, width - margin - 150, y, { size: 10 });
  y -= 20;

  // Supplier Information
  drawText('SUPPLIER INFORMATION', margin, y, { size: 12, bold: true });
  y -= 15;

  drawText(`Name: ${purchaseOrder.supplier.name}`, margin, y, { size: 10 });
  y -= 12;

  if (purchaseOrder.supplier.contactPerson) {
    drawText(`Contact: ${purchaseOrder.supplier.contactPerson}`, margin, y, { size: 10 });
    y -= 12;
  }

  if (purchaseOrder.supplier.phone) {
    drawText(`Phone: ${purchaseOrder.supplier.phone}`, margin, y, { size: 10 });
    y -= 12;
  }

  if (purchaseOrder.supplier.email) {
    drawText(`Email: ${purchaseOrder.supplier.email}`, margin, y, { size: 10 });
    y -= 12;
  }

  if (purchaseOrder.supplier.address) {
    drawText(`Address: ${purchaseOrder.supplier.address}`, margin, y, { size: 10, maxWidth: width - margin * 2 });
    y -= 12;
  }

  y -= 20;

  // Items table header
  drawText('ITEMS', margin, y, { size: 12, bold: true });
  y -= 20;

  // Table headers
  const tableY = y;
  page.drawRectangle({
    x: margin,
    y: tableY - 15,
    width: width - margin * 2,
    height: 15,
    color: rgb(0.9, 0.9, 0.9),
  });

  drawText('Product', margin + 5, tableY - 10, { size: 9, bold: true });
  drawText('SKU', margin + 150, tableY - 10, { size: 9, bold: true });
  drawText('Qty', margin + 220, tableY - 10, { size: 9, bold: true });
  drawText('Unit Price', margin + 260, tableY - 10, { size: 9, bold: true });
  drawText('Subtotal', margin + 350, tableY - 10, { size: 9, bold: true });

  y = tableY - 25;

  // Items
  for (const item of purchaseOrder.items) {
    if (y < margin + 100) {
      // Add new page if needed
      const newPage = pdfDoc.addPage([595, 842]);
      y = height - margin;
    }

    drawText(item.product.name, margin + 5, y, { size: 9, maxWidth: 140 });
    drawText(item.product.sku, margin + 150, y, { size: 9 });
    drawText(Number(item.quantity).toString(), margin + 220, y, { size: 9 });
    drawText(`Rp ${Number(item.unitPrice).toLocaleString('id-ID')}`, margin + 260, y, { size: 9 });
    drawText(`Rp ${Number(item.subtotal).toLocaleString('id-ID')}`, margin + 350, y, { size: 9 });

    y -= 15;
  }

  y -= 10;

  // Totals
  const totalsX = width - margin - 200;
  drawText('Subtotal:', totalsX, y, { size: 10, bold: true });
  drawText(`Rp ${Number(purchaseOrder.subtotal).toLocaleString('id-ID')}`, totalsX + 80, y, { size: 10 });
  y -= 15;

  const taxLabel = purchaseOrder.taxPercentage
    ? `Tax (${purchaseOrder.taxPercentage}%):`
    : 'Tax:';
  drawText(taxLabel, totalsX, y, { size: 10, bold: true });
  drawText(`Rp ${Number(purchaseOrder.tax).toLocaleString('id-ID')}`, totalsX + 80, y, { size: 10 });
  y -= 15;

  // Draw line above total
  page.drawLine({
    start: { x: totalsX, y: y + 12 },
    end: { x: totalsX + 150, y: y + 12 },
    thickness: 1,
    color: rgb(0, 0, 0),
  });

  drawText('TOTAL:', totalsX, y, { size: 12, bold: true });
  drawText(`Rp ${Number(purchaseOrder.total).toLocaleString('id-ID')}`, totalsX + 80, y, { size: 12, bold: true });
  y -= 30;

  // Notes
  if (purchaseOrder.notes) {
    drawText('NOTES:', margin, y, { size: 10, bold: true });
    y -= 15;
    drawText(purchaseOrder.notes, margin, y, { size: 10, maxWidth: width - margin * 2 });
    y -= 20;
  }

  // Footer
  y = margin + 50;
  drawText('Authorized Signature: ____________________', margin, y, { size: 10 });
  drawText('Date: ____________________', width - margin - 150, y, { size: 10 });

  // Footer with generation info
  drawText(`Generated on ${new Date().toLocaleString()}`, margin, 20, { size: 8, color: rgb(0.5, 0.5, 0.5) });

  return await pdfDoc.save();
}
