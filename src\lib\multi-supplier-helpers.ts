/**
 * Multi-Supplier Product Management System - Helper Functions
 * 
 * This file contains helper functions to maintain backward compatibility
 * and provide convenient access to multi-supplier functionality.
 */

import { prisma } from '@/lib/prisma';
import { ProductSupplier, Supplier, Product } from '@prisma/client';

/**
 * Get the primary/preferred supplier for a product
 * Falls back to the first active supplier if no preferred supplier is found
 */
export async function getPrimarySupplier(productId: string): Promise<ProductSupplier | null> {
  try {
    // First try to get the preferred supplier
    let productSupplier = await prisma.productSupplier.findFirst({
      where: {
        productId,
        isPreferred: true,
        isActive: true,
      },
      include: {
        supplier: true,
      },
    });

    // If no preferred supplier, get the first active supplier
    if (!productSupplier) {
      productSupplier = await prisma.productSupplier.findFirst({
        where: {
          productId,
          isActive: true,
        },
        include: {
          supplier: true,
        },
        orderBy: {
          createdAt: 'asc', // Get the oldest (first added) supplier
        },
      });
    }

    return productSupplier;
  } catch (error) {
    console.error('Error getting primary supplier:', error);
    return null;
  }
}

/**
 * Get supplier-specific price for a product
 */
export async function getSupplierPrice(productId: string, supplierId: string): Promise<number | null> {
  try {
    const productSupplier = await prisma.productSupplier.findUnique({
      where: {
        productId_supplierId: {
          productId,
          supplierId,
        },
      },
      select: {
        purchasePrice: true,
        isActive: true,
      },
    });

    if (!productSupplier || !productSupplier.isActive) {
      return null;
    }

    return Number(productSupplier.purchasePrice);
  } catch (error) {
    console.error('Error getting supplier price:', error);
    return null;
  }
}

/**
 * Get all suppliers for a product
 */
export async function getProductSuppliers(productId: string): Promise<ProductSupplier[]> {
  try {
    const productSuppliers = await prisma.productSupplier.findMany({
      where: {
        productId,
        isActive: true,
      },
      include: {
        supplier: true,
      },
      orderBy: [
        { isPreferred: 'desc' }, // Preferred suppliers first
        { purchasePrice: 'asc' }, // Then by price (lowest first)
        { createdAt: 'asc' }, // Then by creation date
      ],
    });

    return productSuppliers;
  } catch (error) {
    console.error('Error getting product suppliers:', error);
    return [];
  }
}

/**
 * Get preferred supplier price (fallback to first active supplier)
 * This function maintains backward compatibility with the old Product.purchasePrice field
 */
export async function getPreferredSupplierPrice(productId: string): Promise<number | null> {
  try {
    const primarySupplier = await getPrimarySupplier(productId);
    
    if (primarySupplier) {
      return Number(primarySupplier.purchasePrice);
    }

    // Fallback: check if the product still has the old purchasePrice field
    const product = await prisma.product.findUnique({
      where: { id: productId },
      select: { purchasePrice: true },
    });

    if (product?.purchasePrice) {
      return Number(product.purchasePrice);
    }

    return null;
  } catch (error) {
    console.error('Error getting preferred supplier price:', error);
    return null;
  }
}

/**
 * Get all suppliers that supply a specific product
 */
export async function getSuppliersForProduct(productId: string): Promise<Supplier[]> {
  try {
    const productSuppliers = await prisma.productSupplier.findMany({
      where: {
        productId,
        isActive: true,
      },
      include: {
        supplier: true,
      },
      orderBy: [
        { isPreferred: 'desc' },
        { supplier: { name: 'asc' } },
      ],
    });

    return productSuppliers.map(ps => ps.supplier);
  } catch (error) {
    console.error('Error getting suppliers for product:', error);
    return [];
  }
}

/**
 * Get all products supplied by a specific supplier
 */
export async function getProductsForSupplier(supplierId: string): Promise<Product[]> {
  try {
    const productSuppliers = await prisma.productSupplier.findMany({
      where: {
        supplierId,
        isActive: true,
      },
      include: {
        product: {
          include: {
            category: true,
            unit: true,
          },
        },
      },
      orderBy: [
        { isPreferred: 'desc' },
        { product: { name: 'asc' } },
      ],
    });

    return productSuppliers.map(ps => ps.product);
  } catch (error) {
    console.error('Error getting products for supplier:', error);
    return [];
  }
}

/**
 * Set a supplier as preferred for a product
 * This will unset any other preferred suppliers for the same product
 */
export async function setPreferredSupplier(productId: string, supplierId: string): Promise<boolean> {
  try {
    await prisma.$transaction(async (tx) => {
      // First, unset all preferred suppliers for this product
      await tx.productSupplier.updateMany({
        where: {
          productId,
          isPreferred: true,
        },
        data: {
          isPreferred: false,
        },
      });

      // Then set the specified supplier as preferred
      await tx.productSupplier.update({
        where: {
          productId_supplierId: {
            productId,
            supplierId,
          },
        },
        data: {
          isPreferred: true,
        },
      });
    });

    return true;
  } catch (error) {
    console.error('Error setting preferred supplier:', error);
    return false;
  }
}

/**
 * Check if a product has multiple suppliers
 */
export async function hasMultipleSuppliers(productId: string): Promise<boolean> {
  try {
    const count = await prisma.productSupplier.count({
      where: {
        productId,
        isActive: true,
      },
    });

    return count > 1;
  } catch (error) {
    console.error('Error checking multiple suppliers:', error);
    return false;
  }
}

/**
 * Get supplier information for a ProductSupplier relationship
 */
export async function getProductSupplierInfo(productId: string, supplierId: string) {
  try {
    const productSupplier = await prisma.productSupplier.findUnique({
      where: {
        productId_supplierId: {
          productId,
          supplierId,
        },
      },
      include: {
        product: {
          include: {
            category: true,
            unit: true,
          },
        },
        supplier: true,
      },
    });

    return productSupplier;
  } catch (error) {
    console.error('Error getting product supplier info:', error);
    return null;
  }
}
