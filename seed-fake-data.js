// This script installs required dependencies and runs the seeding scripts
const { execSync } = require('child_process');
const path = require('path');
const fs = require('fs');

console.log('Starting fake data seeding process...');

// Check if @faker-js/faker is installed
try {
  console.log('Checking if @faker-js/faker is installed...');
  require.resolve('@faker-js/faker');
  console.log('@faker-js/faker is already installed.');
} catch (e) {
  console.log('@faker-js/faker is not installed. Installing...');
  try {
    execSync('npm install @faker-js/faker --save-dev --legacy-peer-deps', { stdio: 'inherit' });
    console.log('@faker-js/faker installed successfully.');
  } catch (error) {
    console.error('Failed to install @faker-js/faker:', error);
    process.exit(1);
  }
}

// Run the product seeding script
console.log('\nRunning product seeding script...');
try {
  execSync('node prisma/seed-products.js', { stdio: 'inherit' });
} catch (error) {
  console.error('Failed to run product seeding script:', error);
  process.exit(1);
}

// Run the stock seeding script
console.log('\nRunning stock seeding script...');
try {
  execSync('node prisma/seed-stock.js', { stdio: 'inherit' });
} catch (error) {
  console.error('Failed to run stock seeding script:', error);
  process.exit(1);
}

console.log('\nFake data seeding completed successfully!');
