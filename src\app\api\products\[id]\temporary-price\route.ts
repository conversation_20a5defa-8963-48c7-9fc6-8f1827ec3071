import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/auth";

// Helper function to verify the authentication token
async function verifyAuthToken(request: NextRequest) {
  // Get the session token from cookies
  const token = request.cookies.get("session-token");

  // Check if token exists
  if (!token) {
    console.log("[API] /api/products/[id]/temporary-price - No session token found");
    return {
      authenticated: false,
      error: "Unauthorized - No session token",
      status: 403,
      user: null
    };
  }

  try {
    // Verify the token
    const response = await fetch(`${process.env.NEXTAUTH_URL}/api/auth/session`, {
      headers: {
        cookie: `session-token=${token.value}`,
      },
    });

    if (!response.ok) {
      console.log("[API] /api/products/[id]/temporary-price - Invalid session token");
      return {
        authenticated: false,
        error: "Unauthorized - Invalid session token",
        status: 403,
        user: null
      };
    }

    const session = await response.json();

    if (!session.user) {
      console.log("[API] /api/products/[id]/temporary-price - No user in session");
      return {
        authenticated: false,
        error: "Unauthorized - No user in session",
        status: 403,
        user: null
      };
    }

    return {
      authenticated: true,
      error: null,
      status: 200,
      user: session.user
    };
  } catch (error) {
    console.error("[API] /api/products/[id]/temporary-price - Error verifying token:", error);
    return {
      authenticated: false,
      error: "Unauthorized - Error verifying token",
      status: 403,
      user: null
    };
  }
}

// POST /api/products/[id]/temporary-price - Create a temporary price for a product
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication
    const auth = await verifyAuthToken(request);

    if (!auth.authenticated) {
      return NextResponse.json(
        { error: auth.error },
        { status: auth.status }
      );
    }

    // Check if user has permission to update products
    const hasPermission = ["SUPER_ADMIN", "WAREHOUSE_ADMIN", "DEVELOPER"].includes(auth.user.role);
    if (!hasPermission) {
      return NextResponse.json(
        { error: "Unauthorized - Insufficient permissions" },
        { status: 403 }
      );
    }

    // Get request body
    const body = await request.json();
    const { value, type, startDate, endDate } = body;

    // Validate required fields
    if (!value || !type || !startDate || !endDate) {
      return NextResponse.json(
        { error: "Missing required fields" },
        { status: 400 }
      );
    }

    // Validate dates
    const start = new Date(startDate);
    const end = new Date(endDate);
    if (isNaN(start.getTime()) || isNaN(end.getTime())) {
      return NextResponse.json(
        { error: "Invalid date format" },
        { status: 400 }
      );
    }

    if (start >= end) {
      return NextResponse.json(
        { error: "End date must be after start date" },
        { status: 400 }
      );
    }

    // Check if product exists
    const product = await prisma.product.findUnique({
      where: { id: params.id },
    });

    if (!product) {
      return NextResponse.json(
        { error: "Product not found" },
        { status: 404 }
      );
    }

    // Check if product already has a temporary price
    const existingTemporaryPrice = await prisma.temporaryPrice.findUnique({
      where: { productId: params.id },
    });

    if (existingTemporaryPrice) {
      return NextResponse.json(
        { error: "Product already has a temporary price" },
        { status: 400 }
      );
    }

    // Create temporary price
    const temporaryPrice = await prisma.temporaryPrice.create({
      data: {
        productId: params.id,
        value,
        type,
        startDate: start,
        endDate: end,
        createdBy: auth.user.id,
      },
      include: {
        product: true,
        user: {
          select: {
            id: true,
            name: true,
          },
        },
      },
    });

    // Log activity
    await prisma.activityLog.create({
      data: {
        userId: auth.user.id,
        action: "CREATE_TEMPORARY_PRICE",
        details: `Created temporary price for product: ${product.name} (${product.sku})`,
      },
    });

    return NextResponse.json({
      success: true,
      message: "Temporary price created successfully",
      temporaryPrice,
    });
  } catch (error) {
    console.error("[API] POST /api/products/[id]/temporary-price - Error:", error);
    return NextResponse.json(
      { error: "Failed to create temporary price" },
      { status: 500 }
    );
  }
}

// DELETE /api/products/[id]/temporary-price - Delete a temporary price for a product
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication
    const auth = await verifyAuthToken(request);

    if (!auth.authenticated) {
      return NextResponse.json(
        { error: auth.error },
        { status: auth.status }
      );
    }

    // Check if user has permission to update products
    const hasPermission = ["SUPER_ADMIN", "WAREHOUSE_ADMIN", "DEVELOPER"].includes(auth.user.role);
    if (!hasPermission) {
      return NextResponse.json(
        { error: "Unauthorized - Insufficient permissions" },
        { status: 403 }
      );
    }

    // Check if product exists
    const product = await prisma.product.findUnique({
      where: { id: params.id },
      include: {
        temporaryPrice: true,
      },
    });

    if (!product) {
      return NextResponse.json(
        { error: "Product not found" },
        { status: 404 }
      );
    }

    // Check if product has a temporary price
    if (!product.temporaryPrice) {
      return NextResponse.json(
        { error: "Product does not have a temporary price" },
        { status: 404 }
      );
    }

    // Delete temporary price
    await prisma.temporaryPrice.delete({
      where: { productId: params.id },
    });

    // Log activity
    await prisma.activityLog.create({
      data: {
        userId: auth.user.id,
        action: "DELETE_TEMPORARY_PRICE",
        details: `Deleted temporary price for product: ${product.name} (${product.sku})`,
      },
    });

    return NextResponse.json({
      success: true,
      message: "Temporary price deleted successfully",
    });
  } catch (error) {
    console.error("[API] DELETE /api/products/[id]/temporary-price - Error:", error);
    return NextResponse.json(
      { error: "Failed to delete temporary price" },
      { status: 500 }
    );
  }
}
