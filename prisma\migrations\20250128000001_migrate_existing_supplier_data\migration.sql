-- Migration for Multi-Supplier Product Management System - Phase 2
-- This migration migrates existing Product-Supplier relationships to the new ProductSupplier table

-- Migrate existing Product.supplierId relationships to ProductSupplier table
INSERT INTO "ProductSupplier" (
    "id",
    "productId",
    "supplierId",
    "supplierProductCode",
    "supplierProductName",
    "purchasePrice",
    "minimumOrderQuantity",
    "leadTimeDays",
    "isPreferred",
    "isActive",
    "lastOrderDate",
    "lastPurchasePrice",
    "notes",
    "createdAt",
    "updatedAt"
)
SELECT 
    gen_random_uuid() as "id",
    p."id" as "productId",
    p."supplierId" as "supplierId",
    NULL as "supplierProductCode",
    NULL as "supplierProductName",
    COALESCE(p."purchasePrice", 0) as "purchasePrice",
    NULL as "minimumOrderQuantity",
    NULL as "leadTimeDays",
    true as "isPreferred", -- Set migrated suppliers as preferred
    true as "isActive",
    NULL as "lastOrderDate",
    p."purchasePrice" as "lastPurchasePrice",
    'Migrated from existing Product.supplierId relationship' as "notes",
    p."createdAt" as "createdAt",
    p."updatedAt" as "updatedAt"
FROM "Product" p
WHERE p."supplierId" IS NOT NULL;

-- Update existing PurchaseOrderItem records to link to ProductSupplier
-- This links PO items to the newly created ProductSupplier records
UPDATE "PurchaseOrderItem" 
SET "productSupplierId" = ps."id"
FROM "ProductSupplier" ps
INNER JOIN "PurchaseOrder" po ON po."supplierId" = ps."supplierId"
WHERE "PurchaseOrderItem"."purchaseOrderId" = po."id" 
  AND "PurchaseOrderItem"."productId" = ps."productId";

-- Create initial StockBatch records for existing inventory
-- This creates batch records for current store stock
INSERT INTO "StockBatch" (
    "id",
    "productId",
    "productSupplierId",
    "batchNumber",
    "receivedDate",
    "expiryDate",
    "quantity",
    "remainingQuantity",
    "purchasePrice",
    "purchaseOrderId",
    "warehouseStockId",
    "storeStockId",
    "status",
    "notes",
    "createdAt",
    "updatedAt"
)
SELECT 
    gen_random_uuid() as "id",
    ss."productId" as "productId",
    ps."id" as "productSupplierId",
    'MIGRATED-STORE-' || ss."id" as "batchNumber",
    ss."lastUpdated" as "receivedDate",
    NULL as "expiryDate",
    ss."quantity" as "quantity",
    ss."quantity" as "remainingQuantity",
    ps."purchasePrice" as "purchasePrice",
    NULL as "purchaseOrderId",
    NULL as "warehouseStockId",
    ss."id" as "storeStockId",
    'ACTIVE' as "status",
    'Initial batch created from existing store stock during migration' as "notes",
    ss."lastUpdated" as "createdAt",
    ss."lastUpdated" as "updatedAt"
FROM "StoreStock" ss
INNER JOIN "ProductSupplier" ps ON ps."productId" = ss."productId" AND ps."isPreferred" = true
WHERE ss."quantity" > 0;

-- Create initial StockBatch records for existing warehouse inventory
INSERT INTO "StockBatch" (
    "id",
    "productId",
    "productSupplierId",
    "batchNumber",
    "receivedDate",
    "expiryDate",
    "quantity",
    "remainingQuantity",
    "purchasePrice",
    "purchaseOrderId",
    "warehouseStockId",
    "storeStockId",
    "status",
    "notes",
    "createdAt",
    "updatedAt"
)
SELECT 
    gen_random_uuid() as "id",
    ws."productId" as "productId",
    ps."id" as "productSupplierId",
    'MIGRATED-WAREHOUSE-' || ws."id" as "batchNumber",
    ws."lastUpdated" as "receivedDate",
    NULL as "expiryDate",
    ws."quantity" as "quantity",
    ws."quantity" as "remainingQuantity",
    ps."purchasePrice" as "purchasePrice",
    NULL as "purchaseOrderId",
    ws."id" as "warehouseStockId",
    NULL as "storeStockId",
    'ACTIVE' as "status",
    'Initial batch created from existing warehouse stock during migration' as "notes",
    ws."lastUpdated" as "createdAt",
    ws."lastUpdated" as "updatedAt"
FROM "WarehouseStock" ws
INNER JOIN "ProductSupplier" ps ON ps."productId" = ws."productId" AND ps."isPreferred" = true
WHERE ws."quantity" > 0;

-- Update existing StockHistory records to include supplier information where possible
UPDATE "StockHistory" 
SET "productSupplierId" = ps."id"
FROM "ProductSupplier" ps
WHERE "StockHistory"."productId" = ps."productId" 
  AND ps."isPreferred" = true;

-- Link existing StockHistory records to the newly created batches where appropriate
UPDATE "StockHistory" 
SET "batchId" = sb."id"
FROM "StockBatch" sb
WHERE "StockHistory"."productId" = sb."productId" 
  AND "StockHistory"."storeStockId" = sb."storeStockId"
  AND sb."batchNumber" LIKE 'MIGRATED-STORE-%';

UPDATE "StockHistory" 
SET "batchId" = sb."id"
FROM "StockBatch" sb
WHERE "StockHistory"."productId" = sb."productId" 
  AND "StockHistory"."warehouseStockId" = sb."warehouseStockId"
  AND sb."batchNumber" LIKE 'MIGRATED-WAREHOUSE-%';
