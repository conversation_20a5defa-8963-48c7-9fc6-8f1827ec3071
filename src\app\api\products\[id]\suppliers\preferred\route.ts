import { NextRequest, NextResponse } from "next/server";
import { z } from "zod";
import { prisma } from "@/lib/prisma";
import { verifyAuthToken } from "@/lib/auth-utils";

// PUT /api/products/[id]/suppliers/preferred - Set preferred supplier for a product
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication
    const auth = await verifyAuthToken(request);

    if (!auth.authenticated) {
      return NextResponse.json(
        { error: auth.error },
        { status: auth.status }
      );
    }

    // Check permissions
    const hasPermission = ["SUPER_ADMIN", "WAREHOUSE_ADMIN"].includes(auth.user.role);
    if (!hasPermission) {
      return NextResponse.json(
        { error: "Unauthorized - Insufficient permissions" },
        { status: 403 }
      );
    }

    const productId = params.id;

    // Verify product exists
    const product = await prisma.product.findUnique({
      where: { id: productId },
      select: { id: true, name: true }
    });

    if (!product) {
      return NextResponse.json(
        { error: "Product not found" },
        { status: 404 }
      );
    }

    // Get request body
    const body = await request.json();

    // Validate data
    const schema = z.object({
      supplierId: z.string().min(1, { message: "Supplier ID is required" }),
    });

    const validationResult = schema.safeParse(body);
    if (!validationResult.success) {
      return NextResponse.json(
        { error: "Validation failed", issues: validationResult.error.issues },
        { status: 400 }
      );
    }

    const { supplierId } = validationResult.data;

    // Verify the product-supplier relationship exists
    const productSupplier = await prisma.productSupplier.findUnique({
      where: {
        productId_supplierId: {
          productId,
          supplierId
        }
      },
      include: {
        supplier: { select: { name: true } }
      }
    });

    if (!productSupplier) {
      return NextResponse.json(
        { error: "Product-supplier relationship not found" },
        { status: 404 }
      );
    }

    if (!productSupplier.isActive) {
      return NextResponse.json(
        { error: "Cannot set inactive supplier as preferred" },
        { status: 400 }
      );
    }

    // Use transaction to ensure atomicity
    const result = await prisma.$transaction(async (tx) => {
      // First, unset all preferred suppliers for this product
      await tx.productSupplier.updateMany({
        where: {
          productId,
          isPreferred: true
        },
        data: {
          isPreferred: false
        }
      });

      // Then set the specified supplier as preferred
      const updatedProductSupplier = await tx.productSupplier.update({
        where: {
          productId_supplierId: {
            productId,
            supplierId
          }
        },
        data: {
          isPreferred: true
        },
        include: {
          supplier: {
            select: {
              id: true,
              name: true,
              contactPerson: true,
              phone: true,
              email: true,
            }
          }
        }
      });

      return updatedProductSupplier;
    });

    // Log activity
    await prisma.activityLog.create({
      data: {
        userId: auth.user.id,
        action: "SET_PREFERRED_SUPPLIER",
        details: `Set ${productSupplier.supplier.name} as preferred supplier for ${product.name}`,
      },
    });

    return NextResponse.json({
      message: "Preferred supplier updated successfully",
      productSupplier: result
    });
  } catch (error) {
    console.error("Error setting preferred supplier:", error);
    return NextResponse.json(
      { error: "Failed to set preferred supplier", message: (error as Error).message },
      { status: 500 }
    );
  }
}

// DELETE /api/products/[id]/suppliers/preferred - Remove preferred supplier designation
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication
    const auth = await verifyAuthToken(request);

    if (!auth.authenticated) {
      return NextResponse.json(
        { error: auth.error },
        { status: auth.status }
      );
    }

    // Check permissions
    const hasPermission = ["SUPER_ADMIN", "WAREHOUSE_ADMIN"].includes(auth.user.role);
    if (!hasPermission) {
      return NextResponse.json(
        { error: "Unauthorized - Insufficient permissions" },
        { status: 403 }
      );
    }

    const productId = params.id;

    // Verify product exists
    const product = await prisma.product.findUnique({
      where: { id: productId },
      select: { id: true, name: true }
    });

    if (!product) {
      return NextResponse.json(
        { error: "Product not found" },
        { status: 404 }
      );
    }

    // Find current preferred supplier
    const currentPreferred = await prisma.productSupplier.findFirst({
      where: {
        productId,
        isPreferred: true
      },
      include: {
        supplier: { select: { name: true } }
      }
    });

    if (!currentPreferred) {
      return NextResponse.json(
        { error: "No preferred supplier found for this product" },
        { status: 404 }
      );
    }

    // Remove preferred designation
    await prisma.productSupplier.update({
      where: { id: currentPreferred.id },
      data: {
        isPreferred: false
      }
    });

    // Log activity
    await prisma.activityLog.create({
      data: {
        userId: auth.user.id,
        action: "REMOVE_PREFERRED_SUPPLIER",
        details: `Removed preferred supplier designation from ${currentPreferred.supplier.name} for ${product.name}`,
      },
    });

    return NextResponse.json({
      message: "Preferred supplier designation removed successfully"
    });
  } catch (error) {
    console.error("Error removing preferred supplier:", error);
    return NextResponse.json(
      { error: "Failed to remove preferred supplier", message: (error as Error).message },
      { status: 500 }
    );
  }
}

// GET /api/products/[id]/suppliers/preferred - Get preferred supplier for a product
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication
    const auth = await verifyAuthToken(request);

    if (!auth.authenticated) {
      return NextResponse.json(
        { error: auth.error },
        { status: auth.status }
      );
    }

    const productId = params.id;

    // Verify product exists
    const product = await prisma.product.findUnique({
      where: { id: productId },
      select: { id: true, name: true, sku: true }
    });

    if (!product) {
      return NextResponse.json(
        { error: "Product not found" },
        { status: 404 }
      );
    }

    // Find preferred supplier
    const preferredSupplier = await prisma.productSupplier.findFirst({
      where: {
        productId,
        isPreferred: true,
        isActive: true
      },
      include: {
        supplier: {
          select: {
            id: true,
            name: true,
            contactPerson: true,
            phone: true,
            email: true,
            address: true,
          }
        }
      }
    });

    // If no preferred supplier, get the first active supplier as fallback
    let fallbackSupplier = null;
    if (!preferredSupplier) {
      fallbackSupplier = await prisma.productSupplier.findFirst({
        where: {
          productId,
          isActive: true
        },
        include: {
          supplier: {
            select: {
              id: true,
              name: true,
              contactPerson: true,
              phone: true,
              email: true,
              address: true,
            }
          }
        },
        orderBy: {
          createdAt: 'asc' // Get the oldest (first added) supplier
        }
      });
    }

    return NextResponse.json({
      product,
      preferredSupplier,
      fallbackSupplier,
      hasPreferred: !!preferredSupplier
    });
  } catch (error) {
    console.error("Error fetching preferred supplier:", error);
    return NextResponse.json(
      { error: "Failed to fetch preferred supplier", message: (error as Error).message },
      { status: 500 }
    );
  }
}
