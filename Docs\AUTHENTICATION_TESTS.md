# Authentication System Tests

This document outlines the test cases for the authentication system in the Inventora POS and Inventory Management System.

## 1. Password Utilities Tests

### 1.1 Password Hashing

- **Test Case**: Hash a password
- **Expected Result**: The hashed password should be a string, different from the original password, and of reasonable length (>50 characters for bcrypt)

### 1.2 Password Verification

- **Test Case**: Verify a correct password
- **Expected Result**: The verification should return true when the correct password is provided

- **Test Case**: Reject an incorrect password
- **Expected Result**: The verification should return false when an incorrect password is provided

### 1.3 Hash Uniqueness

- **Test Case**: Generate different hashes for the same password
- **Expected Result**: Each hash should be different due to different salts, but both should verify correctly with the original password

## 2. Role-Based Permissions Tests

### 2.1 Permission Definitions

- **Test Case**: Check if permissions are defined for all user roles
- **Expected Result**: Each role (SUPER_ADMIN, CASHIER, FINANCE_ADMIN, WAREHOUSE_ADMIN, MARKETING) should have defined permissions

### 2.2 Super Admin Permissions

- **Test Case**: Check if SUPER_ADMIN has all permissions
- **Expected Result**: SUPER_ADMIN should have wildcard permission ('*') and access to any permission

### 2.3 Role-Specific Permissions

- **Test Case**: Check permissions for CASHIER role
- **Expected Result**: CASHIER should have access to POS and customer-related permissions but not admin or inventory permissions

- **Test Case**: Check permissions for FINANCE_ADMIN role
- **Expected Result**: FINANCE_ADMIN should have access to transaction and report-related permissions but not POS or inventory permissions

- **Test Case**: Check permissions for WAREHOUSE_ADMIN role
- **Expected Result**: WAREHOUSE_ADMIN should have access to inventory and product-related permissions but not transaction or POS permissions

- **Test Case**: Check permissions for MARKETING role
- **Expected Result**: MARKETING should have limited view permissions but not edit permissions

## 3. Authentication Middleware Tests

### 3.1 Public Routes Access

- **Test Case**: Access public routes without authentication
- **Expected Result**: Public routes (/, /login, /api/auth) should be accessible without authentication

### 3.2 Protected Routes Access

- **Test Case**: Access protected routes without authentication
- **Expected Result**: Protected routes (/dashboard, /admin/users, /backup) should redirect to login page

### 3.3 Authenticated User Access

- **Test Case**: Access dashboard with authenticated user
- **Expected Result**: Authenticated users should be able to access the dashboard

### 3.4 Admin Routes Access

- **Test Case**: Access admin routes with non-admin user
- **Expected Result**: Non-admin users should be redirected to dashboard when trying to access admin routes

- **Test Case**: Access admin routes with admin user
- **Expected Result**: Admin users should be able to access admin routes

### 3.5 Backup Routes Access

- **Test Case**: Access backup routes with non-authorized user
- **Expected Result**: Users without backup permissions should be redirected when trying to access backup routes

- **Test Case**: Access backup routes with authorized user
- **Expected Result**: Users with backup permissions (SUPER_ADMIN, FINANCE_ADMIN) should be able to access backup routes

## 4. Users API Tests

### 4.1 GET /api/users

- **Test Case**: Unauthenticated request
- **Expected Result**: Return 403 Unauthorized

- **Test Case**: Non-admin authenticated request
- **Expected Result**: Return 403 Unauthorized

- **Test Case**: Admin authenticated request
- **Expected Result**: Return list of users

- **Test Case**: Handle database errors
- **Expected Result**: Return 500 with error message

### 4.2 POST /api/users

- **Test Case**: Unauthenticated request
- **Expected Result**: Return 403 Unauthorized

- **Test Case**: Non-admin authenticated request
- **Expected Result**: Return 403 Unauthorized

- **Test Case**: Invalid input data
- **Expected Result**: Return 400 with validation errors

- **Test Case**: Email already in use
- **Expected Result**: Return 400 with "Email already in use" error

- **Test Case**: Valid input data
- **Expected Result**: Create user, log activity, and return 201 with user data

- **Test Case**: Handle database errors
- **Expected Result**: Return 500 with error message

## 5. Login Page Tests

### 5.1 Rendering

- **Test Case**: Render login form
- **Expected Result**: Form should contain email input, password input, and sign-in button

### 5.2 Validation

- **Test Case**: Submit with invalid email
- **Expected Result**: Show email validation error

- **Test Case**: Submit with short password
- **Expected Result**: Show password validation error

### 5.3 Form Submission

- **Test Case**: Submit with valid data
- **Expected Result**: Call signIn function with correct parameters

- **Test Case**: Handle failed login
- **Expected Result**: Show error message on failed login

## 6. Header Component Tests

### 6.1 Authentication States

- **Test Case**: Render loading state
- **Expected Result**: Show loading indicator when authentication is loading

- **Test Case**: Render unauthenticated state
- **Expected Result**: Show login button when user is not authenticated

- **Test Case**: Render authenticated state
- **Expected Result**: Show user name and profile when authenticated

### 6.2 User Menu

- **Test Case**: Open user menu
- **Expected Result**: User menu should be closed initially and open when clicked

- **Test Case**: Logout functionality
- **Expected Result**: Call signOut function when logout button is clicked

- **Test Case**: Display user information
- **Expected Result**: Show user initials, name, email, and role in the profile

## 7. useAuth Hook Tests

### 7.1 Initial State

- **Test Case**: Initial loading state
- **Expected Result**: Hook should return null user and loading true initially

### 7.2 Authentication States

- **Test Case**: Authenticated state
- **Expected Result**: Hook should return user data when authenticated

- **Test Case**: Unauthenticated state
- **Expected Result**: Hook should return null user when not authenticated

### 7.3 Error Handling

- **Test Case**: Handle authentication errors
- **Expected Result**: Hook should return null user on error

## Manual Testing Checklist

- [ ] Login with valid credentials
- [ ] Attempt login with invalid credentials
- [ ] Verify that protected routes redirect to login when not authenticated
- [ ] Verify that admin routes are only accessible to admin users
- [ ] Verify that backup routes are only accessible to authorized users
- [ ] Test user creation, editing, and deactivation
- [ ] Test role-based access to different features
- [ ] Test logout functionality
- [ ] Verify that activity logs are created for user actions
- [ ] Test session persistence across page refreshes
