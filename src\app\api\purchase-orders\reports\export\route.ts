import { NextRequest, NextResponse } from 'next/server';
import { verifyAuthToken } from '@/lib/auth-utils';
import * as XLSX from 'xlsx';
import { parse } from 'json2csv';

export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const auth = await verifyAuthToken(request);
    if (!auth.authenticated) {
      return NextResponse.json({ error: auth.error }, { status: auth.status });
    }

    // Only allow SUPER_ADMIN and WAREHOUSE_ADMIN to export reports
    if (!["SUPER_ADMIN", "WAREHOUSE_ADMIN"].includes(auth.user.role)) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    // Parse query parameters
    const searchParams = request.nextUrl.searchParams;
    const reportType = searchParams.get("type") || "pending";
    const format = searchParams.get("format") || "xlsx";
    const supplierId = searchParams.get("supplierId");
    const startDate = searchParams.get("startDate");
    const endDate = searchParams.get("endDate");
    const minAmount = searchParams.get("minAmount");
    const maxAmount = searchParams.get("maxAmount");

    // Fetch report data
    const reportData = await fetchReportData(reportType, supplierId, startDate, endDate, minAmount, maxAmount);

    // Generate file based on requested format
    if (format === "xlsx") {
      return exportToExcel(reportData, reportType);
    } else if (format === "pdf") {
      return exportToPdf(reportData, reportType);
    } else {
      // CSV format (default fallback)
      return exportToCsv(reportData, reportType);
    }
  } catch (error) {
    console.error("Error exporting purchase order report:", error);
    return NextResponse.json(
      { error: "Failed to export purchase order report", message: (error as Error).message },
      { status: 500 }
    );
  }
}

// Fetch report data from the reports API
async function fetchReportData(
  reportType: string,
  supplierId: string | null,
  startDate: string | null,
  endDate: string | null,
  minAmount: string | null,
  maxAmount: string | null
) {
  // Build query parameters
  const queryParams = new URLSearchParams();
  queryParams.append("type", reportType);
  
  if (supplierId) queryParams.append("supplierId", supplierId);
  if (startDate) queryParams.append("startDate", startDate);
  if (endDate) queryParams.append("endDate", endDate);
  if (minAmount) queryParams.append("minAmount", minAmount);
  if (maxAmount) queryParams.append("maxAmount", maxAmount);

  // Make internal API call
  const baseUrl = process.env.NEXTAUTH_URL || 'http://localhost:3000';
  const response = await fetch(`${baseUrl}/api/purchase-orders/reports?${queryParams.toString()}`);
  
  if (!response.ok) {
    throw new Error(`Failed to fetch report data: ${response.statusText}`);
  }

  return await response.json();
}

// Export to Excel format
async function exportToExcel(reportData: any, reportType: string) {
  const workbook = XLSX.utils.book_new();

  // Create main data sheet
  let mainData: any[] = [];
  
  switch (reportType) {
    case "pending":
      mainData = reportData.purchaseOrders.map((po: any) => ({
        "PO ID": po.id.slice(-8).toUpperCase(),
        "Order Date": new Date(po.orderDate).toLocaleDateString(),
        "Supplier": po.supplier.name,
        "Contact Person": po.supplier.contactPerson || "",
        "Phone": po.supplier.phone || "",
        "Status": po.status,
        "Total Amount": formatCurrency(po.total),
        "Item Count": po.itemCount,
        "Created By": po.createdBy.name,
        "Approved By": po.approvedBy?.name || "Not Approved",
        "Days Pending": po.daysPending,
      }));
      break;
    case "received":
      mainData = reportData.purchaseOrders.map((po: any) => ({
        "PO ID": po.id.slice(-8).toUpperCase(),
        "Order Date": new Date(po.orderDate).toLocaleDateString(),
        "Received Date": po.receivedAt ? new Date(po.receivedAt).toLocaleDateString() : "",
        "Supplier": po.supplier.name,
        "Contact Person": po.supplier.contactPerson || "",
        "Status": po.status,
        "Total Amount": formatCurrency(po.total),
        "Item Count": po.itemCount,
        "Created By": po.createdBy.name,
        "Receiving Count": po.receivingCount,
        "Fulfillment Time (Days)": po.fulfillmentTime || "",
      }));
      break;
    case "overdue":
      mainData = reportData.purchaseOrders.map((po: any) => ({
        "PO ID": po.id.slice(-8).toUpperCase(),
        "Order Date": new Date(po.orderDate).toLocaleDateString(),
        "Supplier": po.supplier.name,
        "Contact Person": po.supplier.contactPerson || "",
        "Phone": po.supplier.phone || "",
        "Status": po.status,
        "Total Amount": formatCurrency(po.total),
        "Item Count": po.itemCount,
        "Created By": po.createdBy.name,
        "Days Overdue": po.daysOverdue,
        "Severity": po.severity.toUpperCase(),
      }));
      break;
  }

  const mainSheet = XLSX.utils.json_to_sheet(mainData);
  XLSX.utils.book_append_sheet(workbook, mainSheet, "Purchase Orders");

  // Create summary sheet
  const summaryData: any[] = [];
  summaryData.push({ "Metric": "Total Purchase Orders", "Value": reportData.summary.totalPOs });
  summaryData.push({ "Metric": "Total Value", "Value": formatCurrency(reportData.summary.totalValue) });
  summaryData.push({ "Metric": "Average Value", "Value": formatCurrency(reportData.summary.avgValue) });

  if (reportType === "pending") {
    Object.entries(reportData.summary.statusBreakdown).forEach(([status, count]) => {
      summaryData.push({ "Metric": `${status} Count`, "Value": count });
    });
  } else if (reportType === "received") {
    summaryData.push({ "Metric": "Fully Received", "Value": reportData.summary.fullyReceived });
    summaryData.push({ "Metric": "Partially Received", "Value": reportData.summary.partiallyReceived });
    summaryData.push({ "Metric": "Fulfillment Rate (%)", "Value": `${reportData.summary.fulfillmentRate.toFixed(1)}%` });
  } else if (reportType === "overdue") {
    summaryData.push({ "Metric": "Average Days Overdue", "Value": reportData.summary.avgDaysOverdue.toFixed(1) });
    Object.entries(reportData.summary.severityBreakdown).forEach(([severity, count]) => {
      summaryData.push({ "Metric": `${severity.toUpperCase()} Severity`, "Value": count });
    });
  }

  const summarySheet = XLSX.utils.json_to_sheet(summaryData);
  XLSX.utils.book_append_sheet(workbook, summarySheet, "Summary");

  // Create supplier breakdown sheet
  const supplierData = Object.entries(reportData.summary.supplierBreakdown).map(([supplier, data]: [string, any]) => ({
    "Supplier": supplier,
    "Count": data.count,
    "Total Value": formatCurrency(data.totalValue),
    "Average Value": formatCurrency(data.totalValue / data.count),
    ...(reportType === "overdue" && { "Avg Days Overdue": data.avgDaysOverdue?.toFixed(1) || "0" }),
  }));

  const supplierSheet = XLSX.utils.json_to_sheet(supplierData);
  XLSX.utils.book_append_sheet(workbook, supplierSheet, "Supplier Breakdown");

  // Convert to buffer
  const buffer = XLSX.write(workbook, { type: "buffer", bookType: "xlsx" });

  // Return as downloadable file
  return new NextResponse(buffer, {
    headers: {
      "Content-Type": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
      "Content-Disposition": `attachment; filename="po_${reportType}_report_${new Date().toISOString().split('T')[0]}.xlsx"`,
    },
  });
}

// Export to CSV format
async function exportToCsv(reportData: any, reportType: string) {
  let csvData: any[] = [];
  
  switch (reportType) {
    case "pending":
      csvData = reportData.purchaseOrders.map((po: any) => ({
        "PO ID": po.id.slice(-8).toUpperCase(),
        "Order Date": new Date(po.orderDate).toLocaleDateString(),
        "Supplier": po.supplier.name,
        "Contact Person": po.supplier.contactPerson || "",
        "Phone": po.supplier.phone || "",
        "Status": po.status,
        "Total Amount": po.total,
        "Item Count": po.itemCount,
        "Created By": po.createdBy.name,
        "Approved By": po.approvedBy?.name || "Not Approved",
        "Days Pending": po.daysPending,
      }));
      break;
    case "received":
      csvData = reportData.purchaseOrders.map((po: any) => ({
        "PO ID": po.id.slice(-8).toUpperCase(),
        "Order Date": new Date(po.orderDate).toLocaleDateString(),
        "Received Date": po.receivedAt ? new Date(po.receivedAt).toLocaleDateString() : "",
        "Supplier": po.supplier.name,
        "Contact Person": po.supplier.contactPerson || "",
        "Status": po.status,
        "Total Amount": po.total,
        "Item Count": po.itemCount,
        "Created By": po.createdBy.name,
        "Receiving Count": po.receivingCount,
        "Fulfillment Time (Days)": po.fulfillmentTime || "",
      }));
      break;
    case "overdue":
      csvData = reportData.purchaseOrders.map((po: any) => ({
        "PO ID": po.id.slice(-8).toUpperCase(),
        "Order Date": new Date(po.orderDate).toLocaleDateString(),
        "Supplier": po.supplier.name,
        "Contact Person": po.supplier.contactPerson || "",
        "Phone": po.supplier.phone || "",
        "Status": po.status,
        "Total Amount": po.total,
        "Item Count": po.itemCount,
        "Created By": po.createdBy.name,
        "Days Overdue": po.daysOverdue,
        "Severity": po.severity.toUpperCase(),
      }));
      break;
  }

  // Convert to CSV
  const csv = parse(csvData, {
    fields: Object.keys(csvData[0] || {})
  });

  // Return as downloadable file
  return new NextResponse(csv, {
    headers: {
      "Content-Type": "text/csv",
      "Content-Disposition": `attachment; filename="po_${reportType}_report_${new Date().toISOString().split('T')[0]}.csv"`,
    },
  });
}

// Export to PDF format (placeholder - would need PDF library)
async function exportToPdf(reportData: any, reportType: string) {
  // For now, return CSV as fallback
  return exportToCsv(reportData, reportType);
}

// Helper function to format currency
function formatCurrency(value: number): string {
  return new Intl.NumberFormat('id-ID', {
    style: 'currency',
    currency: 'IDR',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(value);
}
