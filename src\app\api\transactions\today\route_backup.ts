import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/auth";
import { verifyAuthToken } from "@/lib/auth-utils";

// GET /api/transactions/today - Get today's transactions stats
export async function GET(req: NextRequest) {
  try {
    // Check authentication
    const auth = await verifyAuthToken(req);
    if (!auth.authenticated) {
      return NextResponse.json({ error: auth.error }, { status: auth.status });
    }

    // Get query parameters for date filtering
    const url = new URL(req.url);
    const dateParam = url.searchParams.get("date");

    // Set up date range for today (or specified date)
    let startDate: Date;
    let endDate: Date;

    // Import the utility function for date processing
    const { processDateRange } = await import("@/lib/utils");

    if (dateParam) {
      // If a specific date is provided, use it with proper timezone handling
      const { start, end } = processDateRange(dateParam, dateParam);
      startDate = start || new Date(dateParam);
      endDate = end || new Date(dateParam);
    } else {
      // Default to today with proper timezone handling
      const today = new Date();
      const { start, end } = processDateRange(today, today);
      startDate = start || today;
      endDate = end || today;
    }

    // Log the processed dates for debugging
    console.log("[API] /api/transactions/today - Date range:", {
      startDate: startDate.toISOString(),
      endDate: endDate.toISOString(),
      originalDate: dateParam || "today"
    });

    // Get today's transactions count and total revenue
    const todayStats = await prisma.transaction.aggregate({
      where: {
        createdAt: {
          gte: startDate,
          lte: endDate,
        },
        paymentStatus: "PAID", // Only count paid transactions
      },
      _sum: {
        total: true,
      },
      _count: {
        id: true,
      },
    });

    // Calculate values
    const todaySales = todayStats._count.id || 0;
    const todayRevenue = Number(todayStats._sum.total || 0);

    return NextResponse.json({
      todaySales,
      todayRevenue,
    });
  } catch (error) {
    console.error("Error fetching today's transaction statistics:", error);
    return NextResponse.json(
      { error: "Failed to fetch today's transaction statistics" },
      { status: 500 }
    );
  }
}
