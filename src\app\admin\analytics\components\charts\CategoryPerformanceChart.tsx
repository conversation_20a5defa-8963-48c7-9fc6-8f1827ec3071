"use client";

import { useState, useEffect } from "react";
import { BaseChart } from "@/components/charts/BaseChart";
import { <PERSON><PERSON><PERSON>, Pie, Cell, ResponsiveContainer, <PERSON><PERSON><PERSON>, Legend } from "recharts";
import { formatCurrency, formatPercentage, CHART_COLORS } from "@/lib/analytics/chartUtils";
import { CategoryPerformanceData, AnalyticsFilters } from "@/lib/types/analytics";

interface CategoryPerformanceChartProps {
  filters: AnalyticsFilters;
  className?: string;
  limit?: number;
}

export function CategoryPerformanceChart({
  filters,
  className,
  limit = 8,
}: CategoryPerformanceChartProps) {
  const [data, setData] = useState<CategoryPerformanceData[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchData = async () => {
    try {
      setIsLoading(true);
      setError(null);

      // Build query parameters
      const params = new URLSearchParams();

      // Date range with fallback
      if (filters.dateRange.from && filters.dateRange.to) {
        params.append("fromDate", filters.dateRange.from.toISOString());
        params.append("toDate", filters.dateRange.to.toISOString());
      } else {
        console.warn("[CategoryPerformanceChart] No date range provided, using last 30 days");
        const now = new Date();
        const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
        params.append("fromDate", thirtyDaysAgo.toISOString());
        params.append("toDate", now.toISOString());
      }

      // Other filters
      if (filters.cashierIds && filters.cashierIds.length > 0) {
        params.append("cashierIds", filters.cashierIds.join(","));
      }
      if (filters.terminalIds && filters.terminalIds.length > 0) {
        params.append("terminalIds", filters.terminalIds.join(","));
      }
      if (filters.categoryIds && filters.categoryIds.length > 0) {
        params.append("categoryIds", filters.categoryIds.join(","));
      }
      if (filters.paymentMethods && filters.paymentMethods.length > 0) {
        params.append("paymentMethods", filters.paymentMethods.join(","));
      }

      // Add limit
      params.append("limit", limit.toString());

      console.log("[CategoryPerformanceChart] Fetching data with params:", params.toString());

      const response = await fetch(`/api/analytics/category-performance?${params.toString()}`);

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();

      console.log("[CategoryPerformanceChart] API Response:", result);
      console.log("Category performance result success:", result.success);

      if (!result.success) {
        console.error("Category performance result error:", result.error);
        throw new Error(result.error || "Failed to fetch category performance");
      }

      console.log("[CategoryPerformanceChart] Data received:", result.data?.length || 0, "items");
      setData(result.data || []);
    } catch (err) {
      console.error("Error fetching category performance:", err);
      setError(err instanceof Error ? err.message : "An error occurred");
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
  }, [filters, limit]);

  const handleRefresh = () => {
    fetchData();
  };

  // Custom tooltip
  const CustomTooltip = ({ active, payload }: any) => {
    if (active && payload && payload.length && payload[0]?.payload) {
      const data = payload[0].payload;
      return (
        <div className="bg-white p-3 border border-gray-200 rounded-lg shadow-lg">
          <p className="font-medium text-gray-900">{data.name || "Unknown"}</p>
          <div className="mt-2 space-y-1">
            <p className="text-sm">
              <span className="text-blue-600">Revenue: </span>
              <span className="font-medium">{formatCurrency(data.revenue || 0)}</span>
            </p>
            <p className="text-sm">
              <span className="text-green-600">Quantity Sold: </span>
              <span className="font-medium">{(data.quantity || 0).toLocaleString()}</span>
            </p>
            <p className="text-sm">
              <span className="text-purple-600">Percentage: </span>
              <span className="font-medium">{formatPercentage(data.percentage || 0)}</span>
            </p>
          </div>
        </div>
      );
    }
    return null;
  };

  // Custom legend
  const CustomLegend = ({ payload }: any) => {
    return (
      <div className="flex flex-wrap justify-center gap-4 mt-4">
        {payload.map((entry: any, index: number) => (
          <div key={index} className="flex items-center gap-2">
            <div className="w-3 h-3 rounded-full" style={{ backgroundColor: entry.color }} />
            <span className="text-sm text-gray-600">{entry.value}</span>
          </div>
        ))}
      </div>
    );
  };

  // Prepare data for pie chart
  const chartData = Array.isArray(data)
    ? data.map((item, index) => ({
        ...item,
        value: item.revenue || 0,
        fill: CHART_COLORS.mixed[index % CHART_COLORS.mixed.length],
      }))
    : [];

  // Calculate totals for center display
  const totalRevenue = Array.isArray(data)
    ? data.reduce((sum, item) => sum + (item.revenue || 0), 0)
    : 0;
  const totalQuantity = Array.isArray(data)
    ? data.reduce((sum, item) => sum + (item.quantity || 0), 0)
    : 0;

  return (
    <BaseChart
      title="Category Performance"
      subtitle="Revenue distribution by product category"
      isLoading={isLoading}
      error={error}
      onRefresh={handleRefresh}
      className={className}
    >
      <div className="space-y-4">
        {/* Chart Area */}
        <ResponsiveContainer width="100%" height={350}>
          <PieChart>
            <Pie
              data={chartData}
              cx="50%"
              cy="50%"
              innerRadius={60}
              outerRadius={110}
              paddingAngle={2}
              dataKey="value"
            >
              {chartData.map((entry, index) => (
                <Cell key={`cell-${index}`} fill={entry.fill} />
              ))}
            </Pie>
            <Tooltip content={<CustomTooltip />} />
            <Legend content={<CustomLegend />} />
          </PieChart>
        </ResponsiveContainer>

        {/* External Statistics Card - Responsive Design */}
        <div className="bg-gray-50 rounded-lg p-3 md:p-4 border border-gray-200">
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 md:gap-4 text-center">
            <div className="bg-white rounded-md p-3 shadow-sm">
              <p className="text-xs md:text-sm text-gray-600 mb-1">Total Revenue</p>
              <p className="text-base md:text-lg font-semibold text-gray-900">
                {formatCurrency(totalRevenue)}
              </p>
            </div>
            <div className="bg-white rounded-md p-3 shadow-sm">
              <p className="text-xs md:text-sm text-gray-600 mb-1">Total Items Sold</p>
              <p className="text-base md:text-lg font-semibold text-gray-900">
                {totalQuantity.toLocaleString()}
              </p>
            </div>
          </div>
        </div>
      </div>
    </BaseChart>
  );
}
