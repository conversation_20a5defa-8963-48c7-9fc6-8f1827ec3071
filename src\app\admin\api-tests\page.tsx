"use client";

import { useState, useEffect } from "react";
import { MainLayout } from "@/components/layout/MainLayout";
import { PageHeader } from "@/components/layout/PageHeader";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { useClientAuth } from "@/hooks/use-client-auth";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Loader2 } from "lucide-react";

interface TestResult {
  test: string;
  success: boolean;
  response: string | null;
  error: string | null;
  timestamp: string;
}

interface TestSummary {
  total: number;
  passed: number;
  failed: number;
}

export default function ApiTestsPage() {
  const { user } = useClientAuth();
  const [loading, setLoading] = useState(false);
  const [results, setResults] = useState<TestResult[]>([]);
  const [summary, setSummary] = useState<TestSummary | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState("all");

  // Check if user is super admin
  const isSuperAdmin = user?.role === "SUPER_ADMIN";

  // Load the test script
  useEffect(() => {
    if (!isSuperAdmin) return;

    const script = document.createElement("script");
    script.src = "/tests/api-tests.js";
    script.async = true;
    document.body.appendChild(script);

    return () => {
      document.body.removeChild(script);
    };
  }, [isSuperAdmin]);

  // Run the tests
  const runTests = async () => {
    if (!window.runApiTests) {
      setError("Test script not loaded. Please refresh the page.");
      return;
    }

    setLoading(true);
    setError(null);
    setResults([]);
    setSummary(null);

    try {
      const testResults = await window.runApiTests();
      
      if (testResults.error) {
        setError(testResults.error);
      } else {
        setResults(testResults.results);
        setSummary(testResults.summary);
      }
    } catch (err: any) {
      setError(err.message || "An error occurred while running tests");
    } finally {
      setLoading(false);
    }
  };

  // Filter results based on active tab
  const filteredResults = results.filter(result => {
    if (activeTab === "all") return true;
    if (activeTab === "passed") return result.success;
    if (activeTab === "failed") return !result.success;
    return true;
  });

  // If not super admin, show limited view
  if (!isSuperAdmin) {
    return (
      <MainLayout>
        <PageHeader
          title="API Tests"
          description="Test the product management API routes"
        />
        
        <Alert variant="destructive" className="mb-6">
          <AlertTitle>Access Denied</AlertTitle>
          <AlertDescription>
            Only Super Admins can access the API testing page.
          </AlertDescription>
        </Alert>
      </MainLayout>
    );
  }

  return (
    <MainLayout>
      <PageHeader
        title="API Tests"
        description="Test the product management API routes"
        actions={
          <Button onClick={runTests} disabled={loading}>
            {loading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Running Tests...
              </>
            ) : (
              "Run API Tests"
            )}
          </Button>
        }
      />

      {error && (
        <Alert variant="destructive" className="mb-4">
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {summary && (
        <Card className="mb-6">
          <CardHeader>
            <CardTitle>Test Summary</CardTitle>
            <CardDescription>Results of the API tests</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex gap-4">
              <div className="flex flex-col items-center p-4 bg-muted rounded-md">
                <span className="text-2xl font-bold">{summary.total}</span>
                <span className="text-sm text-muted-foreground">Total Tests</span>
              </div>
              <div className="flex flex-col items-center p-4 bg-green-100 text-green-800 rounded-md">
                <span className="text-2xl font-bold">{summary.passed}</span>
                <span className="text-sm">Passed</span>
              </div>
              <div className="flex flex-col items-center p-4 bg-red-100 text-red-800 rounded-md">
                <span className="text-2xl font-bold">{summary.failed}</span>
                <span className="text-sm">Failed</span>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {results.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Test Results</CardTitle>
            <CardDescription>Detailed results of each API test</CardDescription>
          </CardHeader>
          <CardContent>
            <Tabs defaultValue="all" value={activeTab} onValueChange={setActiveTab}>
              <TabsList className="mb-4">
                <TabsTrigger value="all">All ({results.length})</TabsTrigger>
                <TabsTrigger value="passed">Passed ({results.filter(r => r.success).length})</TabsTrigger>
                <TabsTrigger value="failed">Failed ({results.filter(r => !r.success).length})</TabsTrigger>
              </TabsList>
              
              <TabsContent value={activeTab}>
                <div className="space-y-4">
                  {filteredResults.map((result, index) => (
                    <div 
                      key={index} 
                      className={`p-4 rounded-md border ${
                        result.success ? "border-green-200 bg-green-50" : "border-red-200 bg-red-50"
                      }`}
                    >
                      <div className="flex justify-between items-start mb-2">
                        <h3 className={`font-medium ${result.success ? "text-green-800" : "text-red-800"}`}>
                          {result.success ? "✅ " : "❌ "}{result.test}
                        </h3>
                        <span className="text-xs text-muted-foreground">
                          {new Date(result.timestamp).toLocaleTimeString()}
                        </span>
                      </div>
                      
                      {result.error && (
                        <div className="mt-2 p-2 bg-red-100 rounded text-sm">
                          <strong>Error:</strong> {result.error}
                        </div>
                      )}
                      
                      {result.response && (
                        <details className="mt-2">
                          <summary className="cursor-pointer text-sm font-medium">
                            Response Details
                          </summary>
                          <pre className="mt-2 p-2 bg-gray-100 rounded text-xs overflow-auto max-h-60">
                            {result.response}
                          </pre>
                        </details>
                      )}
                    </div>
                  ))}
                </div>
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>
      )}
    </MainLayout>
  );
}

// Add this to the global Window interface
declare global {
  interface Window {
    runApiTests: () => Promise<any>;
  }
}
