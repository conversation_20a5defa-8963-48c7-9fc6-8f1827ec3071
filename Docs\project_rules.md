# Next.js Coding Guide & Best Practices

## Table of Contents

1. [Project Structure](#project-structure)
2. [Folder Naming Conventions](#folder-naming-conventions)
3. [Page Components](#page-components)
4. [API Routes](#api-routes)
5. [State Management](#state-management)
6. [Styling](#styling)
7. [Performance Optimization](#performance-optimization)
8. [Security Best Practices](#security-best-practices)
9. [Code Quality](#code-quality)
10. [Testing](#testing)

---

## Project Structure

- **Keep it organized**: Structure your project logically and use clear naming conventions. Separate concerns between **pages**, **components**, **styles**, and **api**.
- **Use `/pages` for routing**: In Next.js, pages are automatically mapped to routes. Each file inside the `pages` directory should represent a route.
- **Modularize code**: Break down larger components into smaller, reusable components.
- **Shared state management**: For global state (like authentication), use React Context API or external solutions like Redux, Zustand, or Recoil.

Example:

```
/pages
  /index.js           # Home page
  /about.js           # About page
  /products           # Dynamic routing for products
    [id].js           # Product detail page

/components
  Header.js
  Footer.js
  ProductCard.js

/styles
  globals.css
  Home.module.css
  Product.module.css

/api
  auth.js             # API route for authentication
  products.js         # API route for product data
```

---

## Folder Naming Conventions

- **Use lowercase and hyphens**: For files and folders, use lowercase letters with hyphens for separation (e.g., `my-component.js`).
- **Component files**: Should be placed in `/components`.
- **Pages**: Use plural for pages with a list or a collection of resources (e.g., `products.js`), and singular for pages that represent a single resource (e.g., `[id].js`).

---

## Page Components

- **One component per file**: Each file should export one component.
- **Avoid inline styles**: Use CSS modules or styled-components for scoped styling.
- **Avoid side effects in rendering**: Side effects should be placed inside `useEffect` hooks, not directly in the component body.

```jsx
// Good
const ProductCard = ({ product }) => {
  return <div className="product-card">{product.name}</div>;
};

// Avoid this
const ProductCard = ({ product }) => {
  document.title = `Product - ${product.name}`; // Side effect here
  return <div className="product-card">{product.name}</div>;
};
```

---

## API Routes

- **Use API routes for server-side logic**: Place all backend logic, like database queries, in the `/api` folder.
- **Avoid heavy logic in components**: Keep your components clean by offloading logic to API routes or hooks.

Example of an API route:

```javascript
// /api/products.js
import { getProducts } from "../../lib/database";

export default async function handler(req, res) {
  const products = await getProducts();
  res.status(200).json(products);
}
```

---

## State Management

- **Use Context API for simple state**: For simple global state like user authentication, use React Context API.
- **Use external libraries for complex state**: For complex state that requires a global store (like Redux, Zustand), integrate them only when necessary.
- **Avoid prop drilling**: Use context or state management to avoid passing props down multiple layers of components.

---

## Styling

- **Use CSS modules for scoped styling**: Next.js supports CSS modules out of the box.
- **Use styled-components or Tailwind CSS for flexibility**: Tailwind can help with utility-first design, while styled-components provide a JS-based styling approach.
- **Keep styles organized**: Place component-specific styles in `.module.css` or use separate styled-components.

Example of CSS Module:

```css
/* Home.module.css */
.container {
  background-color: white;
  padding: 20px;
}
```

```jsx
// Home.js
import styles from "./Home.module.css";

const Home = () => {
  return <div className={styles.container}>Welcome Home!</div>;
};
```

---

## Performance Optimization

- **Use `next/image` for images**: This built-in component automatically optimizes images.
- **Lazy load images and components**: Use `next/dynamic` to load heavy components only when needed.
- **Prefetch data**: Use `getServerSideProps`, `getStaticProps`, or `getInitialProps` to fetch data server-side for faster initial loads.
- **Optimize bundle size**: Avoid large libraries that are not essential to your application. Use tree-shaking and code splitting.

```javascript
// Dynamically imported component
import dynamic from "next/dynamic";

const HeavyComponent = dynamic(() => import("../components/HeavyComponent"), {
  ssr: false,
});

export default function Page() {
  return <HeavyComponent />;
}
```

---

## Security Best Practices

- **Sanitize user input**: Always validate and sanitize inputs, especially in API routes or when interacting with a database.
- **Use HTTPS**: Ensure your production environment uses HTTPS.
- **Use environment variables securely**: Store sensitive data like API keys in `.env` files, and avoid exposing them in client-side code.

---

## Code Quality

- **Write clean, readable code**: Keep functions small, avoid deeply nested code, and follow established conventions.
- **Use ESLint and Prettier**: Set up ESLint and Prettier to automatically format and lint your code.
- **Version control with Git**: Always commit small, meaningful changes and write clear commit messages.

---

## Testing

- **Write unit tests**: Use Jest and React Testing Library to test individual components and logic.
- **Write end-to-end tests**: Use Cypress or Playwright for end-to-end testing.
- **Test API routes**: Test the logic inside API routes to ensure the correct data is returned.

---

## Additional Resources

- [Next.js Documentation](https://nextjs.org/docs)
- [React Documentation](https://reactjs.org/docs/getting-started.html)
- [Styled-Components](https://styled-components.com/)
- [Tailwind CSS](https://tailwindcss.com/)

---

By following these guidelines, we ensure that the Next.js app remains scalable, maintainable, and consistent throughout development. Always strive for clean, readable, and performant code.
