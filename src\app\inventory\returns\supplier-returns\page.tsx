"use client";

import { useState, useEffect } from "react";
import { MainLayout } from "@/components/layout/MainLayout";
import { PageHeader } from "@/components/layout/PageHeader";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Plus,
  Search,
  Filter,
  Eye,
  CheckCircle,
  XCircle,
  Clock,
  ArrowLeft,
  Truck,
} from "lucide-react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { toast } from "sonner";

interface SupplierReturnItem {
  id: string;
  productId: string;
  quantity: number;
  unitPrice: number;
  subtotal: number;
  product: {
    id: string;
    name: string;
    sku: string;
  };
}

interface SupplierReturn {
  id: string;
  returnDate: string;
  reason: string;
  total: number;
  status: "PENDING" | "APPROVED" | "COMPLETED" | "REJECTED";
  notes?: string;
  supplier: {
    id: string;
    name: string;
  };
  purchaseOrder: {
    id: string;
  } | null;
  items: SupplierReturnItem[];
}

interface PaginationInfo {
  page: number;
  limit: number;
  total: number;
  pages: number;
}

const statusColors = {
  PENDING: "bg-yellow-100 text-yellow-800",
  APPROVED: "bg-blue-100 text-blue-800",
  COMPLETED: "bg-green-100 text-green-800",
  REJECTED: "bg-red-100 text-red-800",
};

const statusIcons = {
  PENDING: <Clock className="h-3 w-3" />,
  APPROVED: <CheckCircle className="h-3 w-3" />,
  COMPLETED: <CheckCircle className="h-3 w-3" />,
  REJECTED: <XCircle className="h-3 w-3" />,
};

export default function SupplierReturnsPage() {
  const router = useRouter();
  const [supplierReturns, setSupplierReturns] = useState<SupplierReturn[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState<string>("all");
  const [pagination, setPagination] = useState<PaginationInfo>({
    page: 1,
    limit: 10,
    total: 0,
    pages: 0,
  });

  const fetchSupplierReturns = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams({
        page: pagination.page.toString(),
        limit: pagination.limit.toString(),
      });

      if (searchTerm) {
        params.append("search", searchTerm);
      }
      if (statusFilter !== "all") {
        params.append("status", statusFilter);
      }

      const response = await fetch(`/api/supplier-returns?${params}`);
      if (!response.ok) {
        throw new Error("Failed to fetch supplier returns");
      }

      const data = await response.json();
      setSupplierReturns(data.supplierReturns);
      setPagination(data.pagination);
    } catch (error) {
      console.error("Error fetching supplier returns:", error);
      toast.error("Failed to load supplier returns");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchSupplierReturns();
  }, [pagination.page, searchTerm, statusFilter]);

  const handleSearch = (value: string) => {
    setSearchTerm(value);
    setPagination((prev) => ({ ...prev, page: 1 }));
  };

  const handleStatusFilter = (value: string) => {
    setStatusFilter(value);
    setPagination((prev) => ({ ...prev, page: 1 }));
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("id-ID", {
      style: "currency",
      currency: "IDR",
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("id-ID", {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  return (
    <MainLayout>
      <div className="space-y-6">
        <PageHeader title="Supplier Returns" description="Manage returns to suppliers">
          <div className="flex gap-2">
            <Link href="/inventory/returns">
              <Button variant="outline">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Customer Returns
              </Button>
            </Link>
            <Link href="/inventory/returns/supplier-returns/new">
              <Button>
                <Plus className="h-4 w-4 mr-2" />
                New Supplier Return
              </Button>
            </Link>
          </div>
        </PageHeader>

        {/* Filters */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Filters</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input
                    placeholder="Search by reason, supplier, or purchase order ID..."
                    value={searchTerm}
                    onChange={(e) => handleSearch(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>
              <Select value={statusFilter} onValueChange={handleStatusFilter}>
                <SelectTrigger className="w-48">
                  <Filter className="h-4 w-4 mr-2" />
                  <SelectValue placeholder="Filter by status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Status</SelectItem>
                  <SelectItem value="PENDING">Pending</SelectItem>
                  <SelectItem value="APPROVED">Approved</SelectItem>
                  <SelectItem value="COMPLETED">Completed</SelectItem>
                  <SelectItem value="REJECTED">Rejected</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </CardContent>
        </Card>

        {/* Supplier Returns Table */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Truck className="h-5 w-5" />
              Supplier Returns List
            </CardTitle>
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className="flex justify-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
              </div>
            ) : supplierReturns.length === 0 ? (
              <div className="text-center py-8 text-muted-foreground">
                No supplier returns found
              </div>
            ) : (
              <div className="space-y-4">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Return ID</TableHead>
                      <TableHead>Date</TableHead>
                      <TableHead>Supplier</TableHead>
                      <TableHead>Purchase Order</TableHead>
                      <TableHead>Reason</TableHead>
                      <TableHead>Items</TableHead>
                      <TableHead>Total</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {supplierReturns.map((supplierReturn) => (
                      <TableRow key={supplierReturn.id}>
                        <TableCell className="font-mono text-sm">
                          {supplierReturn.id.slice(-8)}
                        </TableCell>
                        <TableCell>{formatDate(supplierReturn.returnDate)}</TableCell>
                        <TableCell>{supplierReturn.supplier.name}</TableCell>
                        <TableCell className="font-mono text-sm">
                          {supplierReturn.purchaseOrder ? (
                            supplierReturn.purchaseOrder.id.slice(-8)
                          ) : (
                            <span className="text-muted-foreground italic">Customer Return</span>
                          )}
                        </TableCell>
                        <TableCell className="max-w-xs truncate">{supplierReturn.reason}</TableCell>
                        <TableCell>{supplierReturn.items.length}</TableCell>
                        <TableCell>{formatCurrency(supplierReturn.total)}</TableCell>
                        <TableCell>
                          <Badge
                            variant="secondary"
                            className={statusColors[supplierReturn.status]}
                          >
                            {statusIcons[supplierReturn.status]}
                            <span className="ml-1">{supplierReturn.status}</span>
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <Link href={`/inventory/returns/supplier-returns/${supplierReturn.id}`}>
                            <Button variant="ghost" size="sm">
                              <Eye className="h-4 w-4" />
                            </Button>
                          </Link>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>

                {/* Pagination */}
                {pagination.pages > 1 && (
                  <div className="flex justify-between items-center">
                    <div className="text-sm text-muted-foreground">
                      Showing {(pagination.page - 1) * pagination.limit + 1} to{" "}
                      {Math.min(pagination.page * pagination.limit, pagination.total)} of{" "}
                      {pagination.total} supplier returns
                    </div>
                    <div className="flex gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setPagination((prev) => ({ ...prev, page: prev.page - 1 }))}
                        disabled={pagination.page === 1}
                      >
                        Previous
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setPagination((prev) => ({ ...prev, page: prev.page + 1 }))}
                        disabled={pagination.page === pagination.pages}
                      >
                        Next
                      </Button>
                    </div>
                  </div>
                )}
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </MainLayout>
  );
}
