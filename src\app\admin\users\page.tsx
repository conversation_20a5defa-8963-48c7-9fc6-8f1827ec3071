"use client";

import { useState, useEffect } from "react";
import { MainLayout } from "@/components/layout/MainLayout";
import { PageHeader } from "@/components/layout/PageHeader";
import { Button } from "@/components/ui/button";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Checkbox } from "@/components/ui/checkbox";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { User<PERSON><PERSON>, Edit, UserX, Check, X, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>Off } from "lucide-react";
import { useRouter } from "next/navigation";
import { Badge } from "@/components/ui/badge";
import { format } from "date-fns";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { useClientAuth } from "@/hooks/use-client-auth";

// Define user type
type User = {
  id: string;
  name: string;
  email: string;
  role: string;
  active: boolean;
  createdAt: string;
  updatedAt: string;
  cashDrawer?: {
    id: string;
    name: string;
    isActive: boolean;
    terminal?: {
      id: string;
      name: string;
      location?: string;
    };
  };
};

// Create user form schema
const createUserSchema = z.object({
  name: z.string().min(2, { message: "Name must be at least 2 characters" }),
  email: z.string().email({ message: "Invalid email address" }),
  password: z.string().min(6, { message: "Password must be at least 6 characters" }),
  role: z.enum([
    "DEVELOPER",
    "SUPER_ADMIN",
    "CASHIER",
    "FINANCE_ADMIN",
    "WAREHOUSE_ADMIN",
    "MARKETING",
  ]),
});

// Edit user form schema
const editUserSchema = z.object({
  name: z.string().min(2, { message: "Name must be at least 2 characters" }),
  email: z.string().email({ message: "Invalid email address" }),
  password: z.string().min(6, { message: "Password must be at least 6 characters" }).optional(),
  role: z.enum([
    "DEVELOPER",
    "SUPER_ADMIN",
    "CASHIER",
    "FINANCE_ADMIN",
    "WAREHOUSE_ADMIN",
    "MARKETING",
  ]),
  active: z.boolean(),
});

export default function UsersPage() {
  const router = useRouter();
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [createDialogOpen, setCreateDialogOpen] = useState(false);
  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [showPassword, setShowPassword] = useState(false);
  const [currentUserId, setCurrentUserId] = useState<string>("");
  const { user: currentUser } = useClientAuth();
  const isDeveloper = currentUser?.role === "DEVELOPER";

  // Initialize create user form
  const createForm = useForm<z.infer<typeof createUserSchema>>({
    resolver: zodResolver(createUserSchema),
    defaultValues: {
      name: "",
      email: "",
      password: "",
      role: "CASHIER",
    },
  });

  // Initialize edit user form
  const editForm = useForm<z.infer<typeof editUserSchema>>({
    resolver: zodResolver(editUserSchema),
    defaultValues: {
      name: "",
      email: "",
      password: "",
      role: "CASHIER",
      active: true,
    },
  });

  // Fetch users
  const fetchUsers = async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await fetch("/api/users");

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to fetch users");
      }

      const data = await response.json();
      setUsers(data.users);
    } catch (err) {
      setError(err.message || "An error occurred while fetching users");
      console.error("Error fetching users:", err);
    } finally {
      setLoading(false);
    }
  };

  // Get current user ID
  const getCurrentUser = async () => {
    try {
      const response = await fetch("/api/auth/session");
      if (response.ok) {
        const data = await response.json();
        if (data.user && data.user.id) {
          setCurrentUserId(data.user.id);
        }
      }
    } catch (err) {
      console.error("Error fetching current user:", err);
    }
  };

  // Load users and current user on component mount
  useEffect(() => {
    fetchUsers();
    getCurrentUser();
  }, []);

  // Handle create user form submission
  const onCreateSubmit = async (values: z.infer<typeof createUserSchema>) => {
    try {
      setError(null);
      const response = await fetch("/api/users", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(values),
      });

      const data = await response.json();

      if (!response.ok) {
        // Check for specific error types
        if (data.error === "Email already in use") {
          // Set a specific error for the email field
          createForm.setError("email", {
            type: "manual",
            message: "This email is already in use. Please use a different email address.",
          });
          return;
        }

        throw new Error(data.error || "Failed to create user");
      }

      // Close dialog and refresh users
      setCreateDialogOpen(false);
      createForm.reset();
      fetchUsers();
    } catch (err) {
      setError(err.message || "An error occurred while creating the user");
      console.error("Error creating user:", err);
    }
  };

  // Handle edit user form submission
  const onEditSubmit = async (values: z.infer<typeof editUserSchema>) => {
    if (!selectedUser) return;

    try {
      setError(null);

      // Remove password if it's empty
      const submitData = { ...values };
      if (!submitData.password) {
        delete submitData.password;
      }

      // Ensure own account stays active
      if (selectedUser.id === currentUserId) {
        submitData.active = true;
      }

      const response = await fetch(`/api/users/${selectedUser.id}`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(submitData),
      });

      const data = await response.json();

      if (!response.ok) {
        // Check for specific error types
        if (data.error === "Email already in use") {
          // Set a specific error for the email field
          editForm.setError("email", {
            type: "manual",
            message: "This email is already in use. Please use a different email address.",
          });
          return;
        }

        throw new Error(data.error || "Failed to update user");
      }

      // Close dialog and refresh users
      setEditDialogOpen(false);
      setSelectedUser(null);
      fetchUsers();
    } catch (err) {
      setError(err.message || "An error occurred while updating the user");
      console.error("Error updating user:", err);
    }
  };

  // Handle user activation/deactivation
  const handleDeleteUser = async () => {
    if (!selectedUser) return;

    try {
      setError(null);

      // Toggle the active status
      const newActiveStatus = !selectedUser.active;

      const response = await fetch(`/api/users/${selectedUser.id}`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ active: newActiveStatus }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(
          errorData.error || `Failed to ${newActiveStatus ? "activate" : "deactivate"} user`
        );
      }

      // Close dialog and refresh users
      setDeleteDialogOpen(false);
      setSelectedUser(null);
      fetchUsers();
    } catch (err) {
      setError(
        err.message ||
          `An error occurred while ${selectedUser.active ? "deactivating" : "activating"} the user`
      );
      console.error(`Error ${selectedUser.active ? "deactivating" : "activating"} user:`, err);
    }
  };

  // Set up edit form when a user is selected
  useEffect(() => {
    if (selectedUser && editDialogOpen) {
      editForm.reset({
        name: selectedUser.name,
        email: selectedUser.email,
        password: "", // Always reset password field to empty string
        role: selectedUser.role as any,
        active: selectedUser.active,
      });
    }
  }, [selectedUser, editDialogOpen, editForm]);

  // Get role badge color
  const getRoleBadgeVariant = (role: string) => {
    switch (role) {
      case "DEVELOPER":
        return "purple"; // Special color for developer role
      case "SUPER_ADMIN":
        return "destructive";
      case "FINANCE_ADMIN":
        return "default";
      case "WAREHOUSE_ADMIN":
        return "secondary";
      case "MARKETING":
        return "outline";
      default:
        return "default";
    }
  };

  return (
    <MainLayout>
      <PageHeader
        title="User Management"
        description="Manage system users and their permissions"
        actions={
          <Button onClick={() => setCreateDialogOpen(true)}>
            <UserPlus className="mr-2 h-4 w-4" />
            Add User
          </Button>
        }
      />

      {error && (
        <Alert variant="destructive" className="mb-4">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Name</TableHead>
              <TableHead>Email</TableHead>
              <TableHead>Role</TableHead>
              <TableHead>Drawer Assignment</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Created</TableHead>
              <TableHead className="text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {loading ? (
              <TableRow>
                <TableCell colSpan={7} className="text-center py-8">
                  Loading users...
                </TableCell>
              </TableRow>
            ) : users.length === 0 ? (
              <TableRow>
                <TableCell colSpan={7} className="text-center py-8">
                  No users found
                </TableCell>
              </TableRow>
            ) : (
              users.map((user) => (
                <TableRow key={user.id}>
                  <TableCell className="font-medium">{user.name}</TableCell>
                  <TableCell>{user.email}</TableCell>
                  <TableCell>
                    <Badge variant={getRoleBadgeVariant(user.role)}>
                      {user.role.replace("_", " ")}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    {user.role === "CASHIER" ? (
                      user.cashDrawer ? (
                        <div className="space-y-1">
                          <div className="text-sm font-medium text-green-700">
                            {user.cashDrawer.name}
                          </div>
                          {user.cashDrawer.terminal ? (
                            <div className="text-xs text-blue-600">
                              📍 {user.cashDrawer.terminal.name}
                              {user.cashDrawer.terminal.location &&
                                ` (${user.cashDrawer.terminal.location})`}
                            </div>
                          ) : (
                            <div className="text-xs text-orange-600">
                              ⚠️ Not assigned to terminal
                            </div>
                          )}
                          {!user.cashDrawer.isActive && (
                            <div className="text-xs text-red-600">❌ Drawer inactive</div>
                          )}
                        </div>
                      ) : (
                        <div className="text-sm text-muted-foreground">No drawer assigned</div>
                      )
                    ) : (
                      <div className="text-sm text-muted-foreground">N/A</div>
                    )}
                  </TableCell>
                  <TableCell>
                    {user.active ? (
                      <Badge variant="success" className="bg-green-100 text-green-800">
                        <Check className="mr-1 h-3 w-3" />
                        Active
                      </Badge>
                    ) : (
                      <Badge variant="outline" className="bg-gray-100 text-gray-800">
                        <X className="mr-1 h-3 w-3" />
                        Inactive
                      </Badge>
                    )}
                  </TableCell>
                  <TableCell>{format(new Date(user.createdAt), "MMM d, yyyy")}</TableCell>
                  <TableCell className="text-right">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => {
                        setSelectedUser(user);
                        setEditDialogOpen(true);
                      }}
                    >
                      <Edit className="h-4 w-4" />
                    </Button>
                    {user.id !== currentUserId ? (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => {
                          setSelectedUser(user);
                          setDeleteDialogOpen(true);
                        }}
                      >
                        {user.active ? (
                          <UserX className="h-4 w-4" />
                        ) : (
                          <UserCheck className="h-4 w-4" />
                        )}
                      </Button>
                    ) : null}
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>

      {/* Create User Dialog */}
      <Dialog open={createDialogOpen} onOpenChange={setCreateDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Create New User</DialogTitle>
            <DialogDescription>
              Add a new user to the system. They'll receive login credentials.
            </DialogDescription>
          </DialogHeader>
          <Form {...createForm}>
            <form onSubmit={createForm.handleSubmit(onCreateSubmit)} className="space-y-4">
              <FormField
                control={createForm.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Name</FormLabel>
                    <FormControl>
                      <Input placeholder="John Doe" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={createForm.control}
                name="email"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Email</FormLabel>
                    <FormControl>
                      <Input placeholder="<EMAIL>" type="email" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={createForm.control}
                name="password"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Password</FormLabel>
                    <FormControl>
                      <div className="relative">
                        <Input
                          placeholder="••••••••"
                          type={showPassword ? "text" : "password"}
                          {...field}
                        />
                        <button
                          type="button"
                          className="absolute right-2 top-2.5 text-muted-foreground"
                          onClick={() => setShowPassword(!showPassword)}
                        >
                          {showPassword ? (
                            <EyeOff className="h-4 w-4" />
                          ) : (
                            <Eye className="h-4 w-4" />
                          )}
                        </button>
                      </div>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={createForm.control}
                name="role"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Role</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select a role" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {isDeveloper && <SelectItem value="DEVELOPER">Developer</SelectItem>}
                        <SelectItem value="SUPER_ADMIN">Super Admin</SelectItem>
                        <SelectItem value="CASHIER">Cashier</SelectItem>
                        <SelectItem value="FINANCE_ADMIN">Finance Admin</SelectItem>
                        <SelectItem value="WAREHOUSE_ADMIN">Warehouse Admin</SelectItem>
                        <SelectItem value="MARKETING">Marketing</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <DialogFooter>
                <Button type="button" variant="outline" onClick={() => setCreateDialogOpen(false)}>
                  Cancel
                </Button>
                <Button type="submit">Create User</Button>
              </DialogFooter>
            </form>
          </Form>
        </DialogContent>
      </Dialog>

      {/* Edit User Dialog */}
      <Dialog open={editDialogOpen} onOpenChange={setEditDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Edit User</DialogTitle>
            <DialogDescription>Update user information and permissions.</DialogDescription>
          </DialogHeader>
          <Form {...editForm}>
            <form onSubmit={editForm.handleSubmit(onEditSubmit)} className="space-y-4">
              <FormField
                control={editForm.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Name</FormLabel>
                    <FormControl>
                      <Input placeholder="John Doe" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={editForm.control}
                name="email"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Email</FormLabel>
                    <FormControl>
                      <Input placeholder="<EMAIL>" type="email" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={editForm.control}
                name="password"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Password (leave blank to keep current)</FormLabel>
                    <FormControl>
                      <div className="relative">
                        <Input
                          placeholder="••••••••"
                          type={showPassword ? "text" : "password"}
                          {...field}
                        />
                        <button
                          type="button"
                          className="absolute right-2 top-2.5 text-muted-foreground"
                          onClick={() => setShowPassword(!showPassword)}
                        >
                          {showPassword ? (
                            <EyeOff className="h-4 w-4" />
                          ) : (
                            <Eye className="h-4 w-4" />
                          )}
                        </button>
                      </div>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={editForm.control}
                name="role"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Role</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select a role" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {isDeveloper && <SelectItem value="DEVELOPER">Developer</SelectItem>}
                        <SelectItem value="SUPER_ADMIN">Super Admin</SelectItem>
                        <SelectItem value="CASHIER">Cashier</SelectItem>
                        <SelectItem value="FINANCE_ADMIN">Finance Admin</SelectItem>
                        <SelectItem value="WAREHOUSE_ADMIN">Warehouse Admin</SelectItem>
                        <SelectItem value="MARKETING">Marketing</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
              {/* Hide Active checkbox when editing own account */}
              {selectedUser?.id !== currentUserId && (
                <FormField
                  control={editForm.control}
                  name="active"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                      <FormControl>
                        <Checkbox checked={field.value} onCheckedChange={field.onChange} />
                      </FormControl>
                      <div className="space-y-1 leading-none">
                        <FormLabel>Active</FormLabel>
                        <p className="text-sm text-muted-foreground">
                          Inactive users cannot log in to the system
                        </p>
                      </div>
                    </FormItem>
                  )}
                />
              )}
              <DialogFooter>
                <Button type="button" variant="outline" onClick={() => setEditDialogOpen(false)}>
                  Cancel
                </Button>
                <Button type="submit">Save Changes</Button>
              </DialogFooter>
            </form>
          </Form>
        </DialogContent>
      </Dialog>

      {/* Activate/Deactivate User Dialog */}
      <Dialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>{selectedUser?.active ? "Deactivate User" : "Activate User"}</DialogTitle>
            <DialogDescription>
              {selectedUser?.active
                ? "Are you sure you want to deactivate this user? They will no longer be able to log in."
                : "Are you sure you want to activate this user? They will be able to log in again."}
            </DialogDescription>
          </DialogHeader>
          <div className="py-4">
            <p className="text-sm font-medium">User: {selectedUser?.name}</p>
            <p className="text-sm text-muted-foreground">{selectedUser?.email}</p>
          </div>
          <DialogFooter>
            <Button type="button" variant="outline" onClick={() => setDeleteDialogOpen(false)}>
              Cancel
            </Button>
            <Button
              type="button"
              variant={selectedUser?.active ? "destructive" : "default"}
              onClick={handleDeleteUser}
            >
              {selectedUser?.active ? "Deactivate User" : "Activate User"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </MainLayout>
  );
}
