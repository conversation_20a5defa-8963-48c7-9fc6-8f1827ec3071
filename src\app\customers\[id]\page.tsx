"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { MainLayout } from "@/components/layout/MainLayout";
import { PageHeader } from "@/components/layout/PageHeader";
import { Button } from "@/components/ui/button";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { CustomerForm } from "@/components/customers/CustomerForm";
import {
  ArrowLeft,
  Edit,
  Phone,
  Mail,
  MapPin,
  User,
  AlertCircle,
  Loader2,
  Calendar,
  Receipt,
} from "lucide-react";
import { toast } from "sonner";
import Link from "next/link";
import { formatCurrency, formatDate } from "@/lib/utils";

interface Customer {
  id: string;
  name: string;
  phone?: string;
  email?: string;
  address?: string;
  customerType: "REGULAR" | "FRIEND" | "VIP" | "FAMILY";
  createdAt: string;
  updatedAt: string;
  _count?: {
    transactions: number;
  };
  transactions?: Transaction[];
}

interface Transaction {
  id: string;
  transactionDate: string;
  total: number;
  paymentMethod: string;
  paymentStatus: string;
  status: string;
  items: any[];
  cashier: {
    id: string;
    name: string;
  };
}

export default function CustomerDetailPage({ params }: { params: { id: string } }) {
  const router = useRouter();
  const [customer, setCustomer] = useState<Customer | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Fetch customer details
  const fetchCustomer = async () => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await fetch(`/api/customers/${params.id}?includeTransactions=true`);
      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || "Failed to fetch customer details");
      }

      setCustomer(data.customer);
    } catch (err) {
      setError(err instanceof Error ? err.message : "An unknown error occurred");
      console.error("Error fetching customer details:", err);
    } finally {
      setIsLoading(false);
    }
  };

  // Update customer
  const handleUpdateCustomer = async (data: any) => {
    setIsSubmitting(true);
    setError(null);

    try {
      const response = await fetch(`/api/customers/${params.id}`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || "Failed to update customer");
      }

      toast.success("Customer updated successfully");
      setEditDialogOpen(false);
      fetchCustomer(); // Refresh the data
    } catch (err) {
      setError(err instanceof Error ? err.message : "An unknown error occurred");
      console.error("Error updating customer:", err);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Fetch customer on initial load
  useEffect(() => {
    fetchCustomer();
  }, [params.id]);

  if (isLoading) {
    return (
      <MainLayout>
        <div className="flex justify-center items-center h-64">
          <Loader2 className="h-8 w-8 animate-spin text-gray-500" />
        </div>
      </MainLayout>
    );
  }

  if (error) {
    return (
      <MainLayout>
        <Alert variant="destructive" className="mb-6">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
        <Button variant="outline" onClick={() => router.push("/customers")}>
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Customers
        </Button>
      </MainLayout>
    );
  }

  if (!customer) {
    return (
      <MainLayout>
        <Alert variant="destructive" className="mb-6">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>Customer not found</AlertDescription>
        </Alert>
        <Button variant="outline" onClick={() => router.push("/customers")}>
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Customers
        </Button>
      </MainLayout>
    );
  }

  return (
    <MainLayout>
      <PageHeader
        title={customer.name}
        description="Customer details and transaction history"
        actions={
          <div className="flex gap-2">
            <Button variant="outline" asChild>
              <Link href="/customers">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back
              </Link>
            </Button>
            <Button onClick={() => setEditDialogOpen(true)}>
              <Edit className="h-4 w-4 mr-2" />
              Edit
            </Button>
          </div>
        }
      />

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
        <Card>
          <CardHeader>
            <CardTitle>Contact Information</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {customer.phone && (
                <div className="flex items-center">
                  <Phone className="h-4 w-4 mr-2 text-gray-500" />
                  <span>{customer.phone}</span>
                </div>
              )}
              {customer.email && (
                <div className="flex items-center">
                  <Mail className="h-4 w-4 mr-2 text-gray-500" />
                  <span>{customer.email}</span>
                </div>
              )}
              {customer.address && (
                <div className="flex items-center">
                  <MapPin className="h-4 w-4 mr-2 text-gray-500" />
                  <span>{customer.address}</span>
                </div>
              )}
              <div className="flex items-center">
                <User className="h-4 w-4 mr-2 text-gray-500" />
                <span>
                  {customer.customerType === "REGULAR"
                    ? "Regular Customer"
                    : customer.customerType === "FRIEND"
                      ? "Friend"
                      : "Family"}
                </span>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Customer Details</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center">
                <Calendar className="h-4 w-4 mr-2 text-gray-500" />
                <span>Created: {formatDate(new Date(customer.createdAt))}</span>
              </div>
              <div className="flex items-center">
                <Calendar className="h-4 w-4 mr-2 text-gray-500" />
                <span>Last Updated: {formatDate(new Date(customer.updatedAt))}</span>
              </div>
              <div className="flex items-center">
                <Receipt className="h-4 w-4 mr-2 text-gray-500" />
                <span>
                  Transactions:{" "}
                  {customer._count?.transactions || customer.transactions?.length || 0}
                </span>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Quick Actions</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <Button className="w-full" asChild>
                <Link href={`/pos?customerId=${customer.id}`}>
                  <Receipt className="h-4 w-4 mr-2" />
                  New Transaction
                </Link>
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Transaction History</CardTitle>
          <CardDescription>Recent transactions for this customer</CardDescription>
        </CardHeader>
        <CardContent>
          {customer.transactions && customer.transactions.length > 0 ? (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Date</TableHead>
                  <TableHead>Items</TableHead>
                  <TableHead>Total</TableHead>
                  <TableHead>Payment</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Cashier</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {customer.transactions.map((transaction) => (
                  <TableRow key={transaction.id}>
                    <TableCell>{formatDate(new Date(transaction.transactionDate))}</TableCell>
                    <TableCell>{transaction.items.length} items</TableCell>
                    <TableCell>{formatCurrency(transaction.total)}</TableCell>
                    <TableCell>
                      {transaction.paymentMethod} - {transaction.paymentStatus}
                    </TableCell>
                    <TableCell>{transaction.status}</TableCell>
                    <TableCell>{transaction.cashier.name}</TableCell>
                    <TableCell className="text-right">
                      <Button variant="outline" size="sm" asChild>
                        <Link href={`/transactions/${transaction.id}`}>View</Link>
                      </Button>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          ) : (
            <div className="text-center py-8 text-gray-500">
              No transactions found for this customer
            </div>
          )}
        </CardContent>
      </Card>

      {/* Edit Customer Dialog */}
      <Dialog open={editDialogOpen} onOpenChange={setEditDialogOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>Edit Customer</DialogTitle>
            <DialogDescription>
              Update customer information. Modify the details below.
            </DialogDescription>
          </DialogHeader>
          <CustomerForm
            initialData={customer}
            onSubmit={handleUpdateCustomer}
            isSubmitting={isSubmitting}
            error={error || undefined}
          />
        </DialogContent>
      </Dialog>
    </MainLayout>
  );
}
