"use client";

import { useState, useEffect } from "react";
import { MainLayout } from "@/components/layout/MainLayout";
import { PageHeader } from "@/components/layout/PageHeader";
import { Button } from "@/components/ui/button";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { ProductForm, ProductFormValues } from "@/components/products/ProductForm";
import { ArrowLeft, Loader2 } from "lucide-react";
import { useRouter, useParams } from "next/navigation";
import Link from "next/link";

export default function EditProductPage() {
  const router = useRouter();
  const params = useParams();
  const id = params.id as string;

  const [product, setProduct] = useState<ProductFormValues | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Fetch product data on component mount
  useEffect(() => {
    const fetchProduct = async () => {
      setIsLoading(true);
      setError(null);

      try {
        const response = await fetch(`/api/products/${id}`);

        if (!response.ok) {
          throw new Error("Failed to fetch product");
        }

        const data = await response.json();

        // Log the product data to see what we're working with
        console.log("Product data from API:", JSON.stringify(data.product, null, 2));
        console.log("Store stock data:", JSON.stringify(data.product.storeStock, null, 2));

        // Extract stock quantity from storeStock
        let stockQuantity = 0;

        // Log the storeStock data specifically to debug
        console.log("StoreStock data type:", typeof data.product.storeStock);
        console.log("Is storeStock an array?", Array.isArray(data.product.storeStock));

        // Check if storeStock exists and has items
        if (data.product.storeStock) {
          if (Array.isArray(data.product.storeStock)) {
            if (data.product.storeStock.length > 0) {
              console.log("StoreStock first item:", data.product.storeStock[0]);
              stockQuantity = Number(data.product.storeStock[0].quantity || 0);
              console.log("Extracted quantity from storeStock array:", stockQuantity);
            }
          } else if (typeof data.product.storeStock === "object") {
            console.log("StoreStock as object:", data.product.storeStock);
            stockQuantity = Number(data.product.storeStock.quantity || 0);
            console.log("Extracted quantity from storeStock object:", stockQuantity);
          }
        }

        const formattedProduct = {
          ...data.product,
          basePrice: Number(data.product.basePrice),
          friendPrice: data.product.friendPrice ? Number(data.product.friendPrice) : undefined,
          familyPrice: data.product.familyPrice ? Number(data.product.familyPrice) : undefined,
          quantity: stockQuantity,
        };

        console.log("Formatted product with quantity:", formattedProduct.quantity);

        setProduct(formattedProduct);
      } catch (error) {
        console.error("Error fetching product:", error);
        setError("Failed to load product. Please try again.");
      } finally {
        setIsLoading(false);
      }
    };

    fetchProduct();
  }, [id]);

  // Handle form submission
  const handleSubmit = async (data: ProductFormValues) => {
    setIsSubmitting(true);
    setError(null);

    try {
      // Process data before submission
      const processedData = {
        ...data,
        // Ensure optional fields are properly handled
        description: data.description,
        // If barcode is empty or just whitespace, set it to null
        barcode: data.barcode && data.barcode.trim() !== "" ? data.barcode : null,
        imageUrl: data.imageUrl,
        purchasePrice: data.purchasePrice,
        optionalPrice1: data.optionalPrice1,
        optionalPrice2: data.optionalPrice2,
        expiryDate: data.expiryDate,
        // Ensure categoryId and supplierId are properly handled
        categoryId: data.categoryId === "none" ? null : data.categoryId,
        supplierId: data.supplierId === "none" ? null : data.supplierId,
      };

      console.log("Original form data:", JSON.stringify(data, null, 2));
      console.log("Processed data for submission:", JSON.stringify(processedData, null, 2));

      const response = await fetch(`/api/products/${id}`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(processedData),
      });

      const responseData = await response.json();

      if (!response.ok) {
        console.error("Server response:", responseData);

        // Handle specific error types
        if (responseData.error === "Barcode already exists") {
          // Set field-specific error for barcode
          setError("Validation failed");
          return {
            success: false,
            fieldError: {
              field: "barcode",
              message: "This barcode is already in use by another product",
            },
          };
        } else if (responseData.error === "SKU already exists") {
          // Set field-specific error for SKU
          setError("Validation failed");
          return {
            success: false,
            fieldError: {
              field: "sku",
              message: "This SKU is already in use by another product",
            },
          };
        }

        let errorMessage = responseData.error || "Failed to update product";

        // Add more details if available
        if (responseData.message) {
          errorMessage += `: ${responseData.message}`;
        }

        throw new Error(errorMessage);
      }

      // Redirect to products page on success
      router.push("/inventory/products");
      return { success: true };
    } catch (error) {
      console.error("Error updating product:", error);
      setError((error as Error).message || "Failed to update product. Please try again.");
      return { success: false };
    } finally {
      setIsSubmitting(false);
    }
  };

  if (isLoading) {
    return (
      <MainLayout>
        <div className="flex justify-center items-center p-12">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
          <span className="ml-2">Loading product...</span>
        </div>
      </MainLayout>
    );
  }

  if (error && !product) {
    return (
      <MainLayout>
        <PageHeader
          title="Edit Product"
          description="Update product information"
          actions={
            <Button variant="outline" asChild>
              <Link href="/inventory/products">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Products
              </Link>
            </Button>
          }
        />
        <Alert variant="destructive" className="mt-6">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      </MainLayout>
    );
  }

  return (
    <MainLayout>
      <PageHeader
        title="Edit Product"
        description={`Update information for ${product?.name}`}
        actions={
          <Button variant="outline" asChild>
            <Link href="/inventory/products">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Products
            </Link>
          </Button>
        }
      />

      <div className="mt-6">
        {product && (
          <ProductForm
            initialData={product}
            onSubmit={handleSubmit}
            isSubmitting={isSubmitting}
            error={error}
          />
        )}
      </div>
    </MainLayout>
  );
}
