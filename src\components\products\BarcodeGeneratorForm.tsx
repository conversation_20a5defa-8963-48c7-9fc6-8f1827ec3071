"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Loader2, Search, X } from "lucide-react";
import { Badge } from "@/components/ui/badge";

interface Product {
  id: string;
  name: string;
  sku: string;
  barcode: string | null;
  categoryId?: string | null;
  category?: {
    id: string;
    name: string;
  } | null;
}

interface BarcodeGeneratorFormProps {
  onProductSelection: (products: Product[]) => void;
  selectedProducts: Product[];
  singleProductMode?: boolean;
}

export function BarcodeGeneratorForm({
  onProductSelection,
  selectedProducts,
  singleProductMode = false,
}: BarcodeGeneratorFormProps) {
  const [searchTerm, setSearchTerm] = useState("");
  const [category, setCategory] = useState("all");
  const [categories, setCategories] = useState<{ id: string; name: string }[]>([]);
  const [products, setProducts] = useState<Product[]>([]);
  const [filteredProducts, setFilteredProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Fetch categories
  useEffect(() => {
    const fetchCategories = async () => {
      try {
        const response = await fetch("/api/categories");
        if (!response.ok) {
          throw new Error("Failed to fetch categories");
        }
        const data = await response.json();
        setCategories(data.categories || []);
      } catch (error) {
        console.error("Error fetching categories:", error);
      }
    };

    fetchCategories();
  }, []);

  // Fetch products
  useEffect(() => {
    const fetchProducts = async () => {
      setLoading(true);
      setError(null);

      try {
        // Only fetch products with barcodes
        const response = await fetch("/api/products?limit=500&hasBarcodes=true");
        if (!response.ok) {
          throw new Error("Failed to fetch products");
        }

        const data = await response.json();

        // Ensure products have categoryId property
        const productsWithCategoryId = data.products.map((product: any) => ({
          ...product,
          categoryId: product.category?.id || null,
        }));

        setProducts(productsWithCategoryId || []);
        setFilteredProducts(productsWithCategoryId || []);
      } catch (error) {
        console.error("Error fetching products:", error);
        setError("Failed to load products. Please try again.");
      } finally {
        setLoading(false);
      }
    };

    fetchProducts();
  }, []);

  // Filter products based on search term and category
  useEffect(() => {
    let filtered = [...products];

    // Filter by search term
    if (searchTerm) {
      const term = searchTerm.toLowerCase();
      filtered = filtered.filter(
        (product) =>
          product.name.toLowerCase().includes(term) ||
          product.sku.toLowerCase().includes(term) ||
          (product.barcode && product.barcode.toLowerCase().includes(term))
      );
    }

    // Filter by category
    if (category && category !== "all") {
      // Log for debugging
      console.log("Filtering by category:", category);
      console.log("Products before filter:", products.length);

      filtered = filtered.filter((product) => {
        // Log a few products to check their category structure
        if (product.categoryId === category) {
          return true;
        }
        return false;
      });

      console.log("Products after filter:", filtered.length);
    }

    setFilteredProducts(filtered);
  }, [searchTerm, category, products]);

  // Handle search input change
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
  };

  // Handle category change
  const handleCategoryChange = (value: string) => {
    setCategory(value);
  };

  // Handle product selection
  const handleSelectProduct = (product: Product) => {
    const isSelected = selectedProducts.some((p) => p.id === product.id);

    if (isSelected) {
      // Remove product from selection
      onProductSelection(selectedProducts.filter((p) => p.id !== product.id));
    } else {
      // Add product to selection
      onProductSelection([...selectedProducts, product]);
    }
  };

  // Handle select all
  const handleSelectAll = () => {
    // If all filtered products are already selected, deselect all
    const allSelected = filteredProducts.every((product) =>
      selectedProducts.some((p) => p.id === product.id)
    );

    if (allSelected) {
      // Remove all filtered products from selection
      const filteredIds = new Set(filteredProducts.map((p) => p.id));
      onProductSelection(selectedProducts.filter((p) => !filteredIds.has(p.id)));
    } else {
      // Add all filtered products to selection
      const newSelection = [...selectedProducts];

      filteredProducts.forEach((product) => {
        if (!newSelection.some((p) => p.id === product.id)) {
          newSelection.push(product);
        }
      });

      onProductSelection(newSelection);
    }
  };

  // Handle clear selection
  const handleClearSelection = () => {
    onProductSelection([]);
  };

  // Check if a product is selected
  const isProductSelected = (productId: string) => {
    return selectedProducts.some((p) => p.id === productId);
  };

  return (
    <div className="space-y-6">
      <div className="space-y-4">
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="relative flex-1">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              type="search"
              placeholder="Search products by name, SKU, or barcode..."
              className="pl-9"
              value={searchTerm}
              onChange={handleSearchChange}
            />
          </div>
          <div className="w-full sm:w-1/3">
            <Select value={category} onValueChange={handleCategoryChange}>
              <SelectTrigger>
                <SelectValue placeholder="All Categories" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Categories</SelectItem>
                {categories.map((category) => (
                  <SelectItem key={category.id} value={category.id}>
                    {category.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>

        {singleProductMode && (
          <Alert>
            <AlertDescription>
              Single product mode is active. Only one product can be selected.
            </AlertDescription>
          </Alert>
        )}

        <div className="flex justify-between items-center">
          <div className="flex items-center space-x-2">
            <Checkbox
              id="selectAll"
              checked={
                filteredProducts.length > 0 &&
                filteredProducts.every((product) =>
                  selectedProducts.some((p) => p.id === product.id)
                )
              }
              onCheckedChange={handleSelectAll}
            />
            <Label htmlFor="selectAll">Select All</Label>
          </div>
          <div className="flex items-center gap-2">
            <span className="text-sm text-muted-foreground">
              {selectedProducts.length} product(s) selected
            </span>
            {selectedProducts.length > 0 && (
              <Button variant="outline" size="sm" onClick={handleClearSelection}>
                Clear
              </Button>
            )}
          </div>
        </div>
      </div>

      {error && (
        <Alert variant="destructive">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {loading ? (
        <div className="flex justify-center items-center p-8">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
          <span className="ml-2">Loading products...</span>
        </div>
      ) : filteredProducts.length === 0 ? (
        <div className="text-center p-8 border rounded-lg bg-muted/20">
          <p className="text-muted-foreground">No products found matching your criteria.</p>
        </div>
      ) : (
        <div className="border rounded-md">
          <div className="grid grid-cols-1 divide-y">
            {filteredProducts.map((product) => (
              <div
                key={product.id}
                className={`p-4 flex items-center gap-3 hover:bg-muted/50 cursor-pointer ${
                  isProductSelected(product.id) ? "bg-muted/50" : ""
                }`}
                onClick={() => handleSelectProduct(product)}
              >
                <Checkbox checked={isProductSelected(product.id)} />
                <div className="flex-1">
                  <div className="font-medium">{product.name}</div>
                  <div className="text-sm text-muted-foreground">
                    SKU: {product.sku} | Barcode: {product.barcode || "None"}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {selectedProducts.length > 0 && (
        <div className="mt-6">
          <h3 className="text-sm font-medium mb-2">Selected Products:</h3>
          <div className="h-[150px] rounded-md border p-2 overflow-auto">
            <div className="flex flex-wrap gap-2">
              {selectedProducts.map((product) => (
                <Badge
                  key={product.id}
                  variant="secondary"
                  className="flex items-center gap-1 py-1.5"
                >
                  {product.name}
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-4 w-4 p-0 ml-1"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleSelectProduct(product);
                    }}
                  >
                    <X className="h-3 w-3" />
                  </Button>
                </Badge>
              ))}
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
